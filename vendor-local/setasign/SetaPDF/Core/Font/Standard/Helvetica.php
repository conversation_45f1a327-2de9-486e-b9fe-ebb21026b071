<?php
/**
 * This file is part of the SetaPDF-Core Component
 * 
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Core
 * @subpackage Font
 * @license    https://www.setasign.com/ Commercial
 * @version    $Id: Helvetica.php 1926 2024-03-13 15:33:09Z jan.slabon $
 */

/**
 * Class representing the PDF standard font Helvetica 
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Core
 * @subpackage Font
 * @license    https://www.setasign.com/ Commercial
 */
class SetaPDF_Core_Font_Standard_Helvetica extends SetaPDF_Core_Font_Standard
{
    /**
     * Gets a default dictionary for this font.
     *
     * @param string $encoding
     * @return SetaPDF_Core_Type_Dictionary
     */
    public static function getDefaultDictionary($encoding = 'WinAnsi')
    {
        $encoding = str_replace('Encoding', '', $encoding);
        $dictionary = new SetaPDF_Core_Type_Dictionary();
        $dictionary['Type'] = new SetaPDF_Core_Type_Name('Font', true);
        $dictionary['Subtype'] = new SetaPDF_Core_Type_Name('Type1', true);
        $dictionary['BaseFont'] = new SetaPDF_Core_Type_Name('Helvetica', true);
        $dictionary['Encoding'] = new SetaPDF_Core_Type_Name($encoding . 'Encoding');

        return $dictionary;
    }
    
    /**
     * Creates a font object of this font.
     * 
     * @param SetaPDF_Core_Document $document
     * @param string $baseEncoding
     * @param array $diffEncoding
     * @return SetaPDF_Core_Font_Standard_Helvetica
     * @throws SetaPDF_Core_Type_Exception
     */
    public static function create(
        SetaPDF_Core_Document $document,
        $baseEncoding = SetaPDF_Core_Encoding::WIN_ANSI,
        $diffEncoding = []
    )
    {
        $dictionary = self::getDefaultDictionary($baseEncoding);
        parent::_createDifferenceArray($dictionary, $baseEncoding, $diffEncoding);
        $fontObject = $document->createNewObject($dictionary);
        
        return new self($fontObject);  
    }

    /**
     * Constructor.
     *
     * @param SetaPDF_Core_Type_IndirectObjectInterface|SetaPDF_Core_Type_Dictionary $indirectObjectOrDictionary
     * @throws SetaPDF_Core_Type_Exception
     */
    public function __construct($indirectObjectOrDictionary)
    {
        parent::__construct($indirectObjectOrDictionary);
        
        $this->_fontName = 'Helvetica';
        $this->_fontFamily = 'Helvetica';
        
        $this->_info = [
            SetaPDF_Core_Font::INFO_COPYRIGHT => '(c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.',
            SetaPDF_Core_Font::INFO_CREATION_DATE => 'Date: Thu May  1 12:38:23 1997',
            SetaPDF_Core_Font::INFO_UNIQUE_ID => '43054',
            SetaPDF_Core_Font::INFO_VERSION => '002.000'            
        ];
        
        $this->_isBold = false;
        $this->_isItalic = false;
        $this->_isMonospace = false;
        
        $this->_fontBBox = [-166, -225, 1000, 931];
        
        $this->_italicAngle = 0;
        
        $this->_ascent = 718;        
        $this->_descent = -207;        
        
        $this->_capHeight = 718;
        $this->_xHeight = 523;

        $this->_underlinePosition = -100;
        $this->_underlineThickness = 50;

        $this->_widths = [
            "\x00\x20" => 278,  "\x00\x21" => 278,  "\x00\x22" => 355,  "\x00\x23" => 556,
            "\x00\x24" => 556,  "\x00\x25" => 889,  "\x00\x26" => 667,  "\x20\x19" => 222,
            "\x00\x28" => 333,  "\x00\x29" => 333,  "\x00\x2a" => 389,  "\x00\x2b" => 584,
            "\x00\x2c" => 278,  "\x00\x2d" => 333,  "\x00\x2e" => 278,  "\x00\x2f" => 278,
            "\x00\x30" => 556,  "\x00\x31" => 556,  "\x00\x32" => 556,  "\x00\x33" => 556,
            "\x00\x34" => 556,  "\x00\x35" => 556,  "\x00\x36" => 556,  "\x00\x37" => 556,
            "\x00\x38" => 556,  "\x00\x39" => 556,  "\x00\x3a" => 278,  "\x00\x3b" => 278,
            "\x00\x3c" => 584,  "\x00\x3d" => 584,  "\x00\x3e" => 584,  "\x00\x3f" => 556,
            "\x00\x40" => 1015, "\x00\x41" => 667,  "\x00\x42" => 667,  "\x00\x43" => 722,
            "\x00\x44" => 722,  "\x00\x45" => 667,  "\x00\x46" => 611,  "\x00\x47" => 778,
            "\x00\x48" => 722,  "\x00\x49" => 278,  "\x00\x4a" => 500,  "\x00\x4b" => 667,
            "\x00\x4c" => 556,  "\x00\x4d" => 833,  "\x00\x4e" => 722,  "\x00\x4f" => 778,
            "\x00\x50" => 667,  "\x00\x51" => 778,  "\x00\x52" => 722,  "\x00\x53" => 667,
            "\x00\x54" => 611,  "\x00\x55" => 722,  "\x00\x56" => 667,  "\x00\x57" => 944,
            "\x00\x58" => 667,  "\x00\x59" => 667,  "\x00\x5a" => 611,  "\x00\x5b" => 278,
            "\x00\x5c" => 278,  "\x00\x5d" => 278,  "\x00\x5e" => 469,  "\x00\x5f" => 556,
            "\x20\x18" => 222,  "\x00\x61" => 556,  "\x00\x62" => 556,  "\x00\x63" => 500,
            "\x00\x64" => 556,  "\x00\x65" => 556,  "\x00\x66" => 278,  "\x00\x67" => 556,
            "\x00\x68" => 556,  "\x00\x69" => 222,  "\x00\x6a" => 222,  "\x00\x6b" => 500,
            "\x00\x6c" => 222,  "\x00\x6d" => 833,  "\x00\x6e" => 556,  "\x00\x6f" => 556,
            "\x00\x70" => 556,  "\x00\x71" => 556,  "\x00\x72" => 333,  "\x00\x73" => 500,
            "\x00\x74" => 278,  "\x00\x75" => 556,  "\x00\x76" => 500,  "\x00\x77" => 722,
            "\x00\x78" => 500,  "\x00\x79" => 500,  "\x00\x7a" => 500,  "\x00\x7b" => 334,
            "\x00\x7c" => 260,  "\x00\x7d" => 334,  "\x00\x7e" => 584,  "\x00\xa1" => 333,
            "\x00\xa2" => 556,  "\x00\xa3" => 556,  "\x20\x44" => 167,  "\x00\xa5" => 556,
            "\x01\x92" => 556,  "\x00\xa7" => 556,  "\x00\xa4" => 556,  "\x00\x27" => 191,
            "\x20\x1c" => 333,  "\x00\xab" => 556,  "\x20\x39" => 333,  "\x20\x3a" => 333,
            "\xfb\x01" => 500,  "\xfb\x02" => 500,  "\x20\x13" => 556,  "\x20\x20" => 556,
            "\x20\x21" => 556,  "\x00\xb7" => 278,  "\x00\xb6" => 537,  "\x20\x22" => 350,
            "\x20\x1a" => 222,  "\x20\x1e" => 333,  "\x20\x1d" => 333,  "\x00\xbb" => 556,
            "\x20\x26" => 1000, "\x20\x30" => 1000, "\x00\xbf" => 611,  "\x00\x60" => 333,
            "\x00\xb4" => 333,  "\x02\xc6" => 333,  "\x02\xdc" => 333,  "\x00\xaf" => 333,
            "\x02\xd8" => 333,  "\x02\xd9" => 333,  "\x00\xa8" => 333,  "\x02\xda" => 333,
            "\x00\xb8" => 333,  "\x02\xdd" => 333,  "\x02\xdb" => 333,  "\x02\xc7" => 333,
            "\x20\x14" => 1000, "\x00\xc6" => 1000, "\x00\xaa" => 370,  "\x01\x41" => 556,
            "\x00\xd8" => 778,  "\x01\x52" => 1000, "\x00\xba" => 365,  "\x00\xe6" => 889,
            "\x01\x31" => 278,  "\x01\x42" => 222,  "\x00\xf8" => 611,  "\x01\x53" => 944,
            "\x00\xdf" => 611,  "\x00\xcf" => 278,  "\x00\xe9" => 556,  "\x01\x03" => 556,
            "\x01\x71" => 556,  "\x01\x1b" => 556,  "\x01\x78" => 667,  "\x00\xf7" => 584,
            "\x00\xdd" => 667,  "\x00\xc2" => 667,  "\x00\xe1" => 556,  "\x00\xdb" => 722,
            "\x00\xfd" => 500,  "\x02\x19" => 500,  "\x00\xea" => 556,  "\x01\x6e" => 722,
            "\x00\xdc" => 722,  "\x01\x05" => 556,  "\x00\xda" => 722,  "\x01\x73" => 556,
            "\x00\xcb" => 667,  "\x01\x10" => 722,  "\xf6\xc3" => 250,  "\x00\xa9" => 737,
            "\x01\x12" => 667,  "\x01\x0d" => 500,  "\x00\xe5" => 556,  "\x01\x45" => 722,
            "\x01\x3a" => 222,  "\x00\xe0" => 556,  "\x01\x62" => 611,  "\x01\x06" => 722,
            "\x00\xe3" => 556,  "\x01\x16" => 667,  "\x01\x61" => 500,  "\x01\x5f" => 500,
            "\x00\xed" => 278,  "\x25\xca" => 471,  "\x01\x58" => 722,  "\x01\x22" => 778,
            "\x00\xfb" => 556,  "\x00\xe2" => 556,  "\x01\x00" => 667,  "\x01\x59" => 333,
            "\x00\xe7" => 500,  "\x01\x7b" => 611,  "\x00\xde" => 667,  "\x01\x4c" => 778,
            "\x01\x54" => 722,  "\x01\x5a" => 667,  "\x01\x0f" => 643,  "\x01\x6a" => 722,
            "\x01\x6f" => 556,  "\x00\xb3" => 333,  "\x00\xd2" => 778,  "\x00\xc0" => 667,
            "\x01\x02" => 667,  "\x00\xd7" => 584,  "\x00\xfa" => 556,  "\x01\x64" => 611,
            "\x22\x02" => 476,  "\x00\xff" => 500,  "\x01\x43" => 722,  "\x00\xee" => 278,
            "\x00\xca" => 667,  "\x00\xe4" => 556,  "\x00\xeb" => 556,  "\x01\x07" => 500,
            "\x01\x44" => 556,  "\x01\x6b" => 556,  "\x01\x47" => 722,  "\x00\xcd" => 278,
            "\x00\xb1" => 584,  "\x00\xa6" => 260,  "\x00\xae" => 737,  "\x01\x1e" => 778,
            "\x01\x30" => 278,  "\x22\x11" => 600,  "\x00\xc8" => 667,  "\x01\x55" => 333,
            "\x01\x4d" => 556,  "\x01\x79" => 611,  "\x01\x7d" => 611,  "\x22\x65" => 549,
            "\x00\xd0" => 722,  "\x00\xc7" => 722,  "\x01\x3c" => 222,  "\x01\x65" => 317,
            "\x01\x19" => 556,  "\x01\x72" => 722,  "\x00\xc1" => 667,  "\x00\xc4" => 667,
            "\x00\xe8" => 556,  "\x01\x7a" => 500,  "\x01\x2f" => 222,  "\x00\xd3" => 778,
            "\x00\xf3" => 556,  "\x01\x01" => 556,  "\x01\x5b" => 500,  "\x00\xef" => 278,
            "\x00\xd4" => 778,  "\x00\xd9" => 722,  "\x22\x06" => 612,  "\x00\xfe" => 556,
            "\x00\xb2" => 333,  "\x00\xd6" => 778,  "\x00\xb5" => 556,  "\x00\xec" => 278,
            "\x01\x51" => 556,  "\x01\x18" => 667,  "\x01\x11" => 556,  "\x00\xbe" => 834,
            "\x01\x5e" => 667,  "\x01\x3e" => 299,  "\x01\x36" => 667,  "\x01\x39" => 556,
            "\x21\x22" => 1000, "\x01\x17" => 556,  "\x00\xcc" => 278,  "\x01\x2a" => 278,
            "\x01\x3d" => 556,  "\x00\xbd" => 834,  "\x22\x64" => 549,  "\x00\xf4" => 556,
            "\x00\xf1" => 556,  "\x01\x70" => 722,  "\x00\xc9" => 667,  "\x01\x13" => 556,
            "\x01\x1f" => 556,  "\x00\xbc" => 834,  "\x01\x60" => 667,  "\x02\x18" => 667,
            "\x01\x50" => 778,  "\x00\xb0" => 400,  "\x00\xf2" => 556,  "\x01\x0c" => 722,
            "\x00\xf9" => 556,  "\x22\x1a" => 453,  "\x01\x0e" => 722,  "\x01\x57" => 333,
            "\x00\xd1" => 722,  "\x00\xf5" => 556,  "\x01\x56" => 722,  "\x01\x3b" => 556,
            "\x00\xc3" => 667,  "\x01\x04" => 667,  "\x00\xc5" => 667,  "\x00\xd5" => 778,
            "\x01\x7c" => 500,  "\x01\x1a" => 667,  "\x01\x2e" => 278,  "\x01\x37" => 500,
            "\x22\x12" => 584,  "\x00\xce" => 278,  "\x01\x48" => 556,  "\x01\x63" => 278,
            "\x00\xac" => 584,  "\x00\xf6" => 556,  "\x00\xfc" => 556,  "\x22\x60" => 549,
            "\x01\x23" => 556,  "\x00\xf0" => 556,  "\x01\x7e" => 500,  "\x01\x46" => 556,
            "\x00\xb9" => 333,  "\x01\x2b" => 278,  "\x20\xac" => 556,
        ];

        $this->_kerningPairs = [
            "\x00\x41" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\xc1" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x01\x02" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\xc2" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\xc4" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\xc0" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x01\x00" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x01\x04" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\xc5" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\xc3" => [
                "\x00\x43" => -30,  "\x01\x06" => -30,  "\x01\x0c" => -30,  "\x00\xc7" => -30,
                "\x00\x47" => -30,  "\x01\x1e" => -30,  "\x01\x22" => -30,  "\x00\x4f" => -30,
                "\x00\xd3" => -30,  "\x00\xd4" => -30,  "\x00\xd6" => -30,  "\x00\xd2" => -30,
                "\x01\x50" => -30,  "\x01\x4c" => -30,  "\x00\xd8" => -30,  "\x00\xd5" => -30,
                "\x00\x51" => -30,  "\x00\x54" => -120, "\x01\x64" => -120, "\x01\x62" => -120,
                "\x00\x55" => -50,  "\x00\xda" => -50,  "\x00\xdb" => -50,  "\x00\xdc" => -50,
                "\x00\xd9" => -50,  "\x01\x70" => -50,  "\x01\x6a" => -50,  "\x01\x72" => -50,
                "\x01\x6e" => -50,  "\x00\x56" => -70,  "\x00\x57" => -50,  "\x00\x59" => -100,
                "\x00\xdd" => -100, "\x01\x78" => -100, "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x76" => -40,
                "\x00\x77" => -40,  "\x00\x79" => -40,  "\x00\xfd" => -40,  "\x00\xff" => -40,
            ],
            "\x00\x42" => [
                "\x00\x55" => -10,  "\x00\xda" => -10,  "\x00\xdb" => -10,  "\x00\xdc" => -10,
                "\x00\xd9" => -10,  "\x01\x70" => -10,  "\x01\x6a" => -10,  "\x01\x72" => -10,
                "\x01\x6e" => -10,  "\x00\x2c" => -20,  "\x00\x2e" => -20,
            ],
            "\x00\x43" => [
                "\x00\x2c" => -30,  "\x00\x2e" => -30,
            ],
            "\x01\x06" => [
                "\x00\x2c" => -30,  "\x00\x2e" => -30,
            ],
            "\x01\x0c" => [
                "\x00\x2c" => -30,  "\x00\x2e" => -30,
            ],
            "\x00\xc7" => [
                "\x00\x2c" => -30,  "\x00\x2e" => -30,
            ],
            "\x00\x44" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x56" => -70,  "\x00\x57" => -40,
                "\x00\x59" => -90,  "\x00\xdd" => -90,  "\x01\x78" => -90,  "\x00\x2c" => -70,
                "\x00\x2e" => -70,
            ],
            "\x01\x0e" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x56" => -70,  "\x00\x57" => -40,
                "\x00\x59" => -90,  "\x00\xdd" => -90,  "\x01\x78" => -90,  "\x00\x2c" => -70,
                "\x00\x2e" => -70,
            ],
            "\x01\x10" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x56" => -70,  "\x00\x57" => -40,
                "\x00\x59" => -90,  "\x00\xdd" => -90,  "\x01\x78" => -90,  "\x00\x2c" => -70,
                "\x00\x2e" => -70,
            ],
            "\x00\x46" => [
                "\x00\x41" => -80,  "\x00\xc1" => -80,  "\x01\x02" => -80,  "\x00\xc2" => -80,
                "\x00\xc4" => -80,  "\x00\xc0" => -80,  "\x01\x00" => -80,  "\x01\x04" => -80,
                "\x00\xc5" => -80,  "\x00\xc3" => -80,  "\x00\x61" => -50,  "\x00\xe1" => -50,
                "\x01\x03" => -50,  "\x00\xe2" => -50,  "\x00\xe4" => -50,  "\x00\xe0" => -50,
                "\x01\x01" => -50,  "\x01\x05" => -50,  "\x00\xe5" => -50,  "\x00\xe3" => -50,
                "\x00\x2c" => -150, "\x00\x65" => -30,  "\x00\xe9" => -30,  "\x01\x1b" => -30,
                "\x00\xea" => -30,  "\x00\xeb" => -30,  "\x01\x17" => -30,  "\x00\xe8" => -30,
                "\x01\x13" => -30,  "\x01\x19" => -30,  "\x00\x6f" => -30,  "\x00\xf3" => -30,
                "\x00\xf4" => -30,  "\x00\xf6" => -30,  "\x00\xf2" => -30,  "\x01\x51" => -30,
                "\x01\x4d" => -30,  "\x00\xf8" => -30,  "\x00\xf5" => -30,  "\x00\x2e" => -150,
                "\x00\x72" => -45,  "\x01\x55" => -45,  "\x01\x59" => -45,  "\x01\x57" => -45,
            ],
            "\x00\x4a" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x61" => -20,  "\x00\xe1" => -20,
                "\x01\x03" => -20,  "\x00\xe2" => -20,  "\x00\xe4" => -20,  "\x00\xe0" => -20,
                "\x01\x01" => -20,  "\x01\x05" => -20,  "\x00\xe5" => -20,  "\x00\xe3" => -20,
                "\x00\x2c" => -30,  "\x00\x2e" => -30,  "\x00\x75" => -20,  "\x00\xfa" => -20,
                "\x00\xfb" => -20,  "\x00\xfc" => -20,  "\x00\xf9" => -20,  "\x01\x71" => -20,
                "\x01\x6b" => -20,  "\x01\x73" => -20,  "\x01\x6f" => -20,
            ],
            "\x00\x4b" => [
                "\x00\x4f" => -50,  "\x00\xd3" => -50,  "\x00\xd4" => -50,  "\x00\xd6" => -50,
                "\x00\xd2" => -50,  "\x01\x50" => -50,  "\x01\x4c" => -50,  "\x00\xd8" => -50,
                "\x00\xd5" => -50,  "\x00\x65" => -40,  "\x00\xe9" => -40,  "\x01\x1b" => -40,
                "\x00\xea" => -40,  "\x00\xeb" => -40,  "\x01\x17" => -40,  "\x00\xe8" => -40,
                "\x01\x13" => -40,  "\x01\x19" => -40,  "\x00\x6f" => -40,  "\x00\xf3" => -40,
                "\x00\xf4" => -40,  "\x00\xf6" => -40,  "\x00\xf2" => -40,  "\x01\x51" => -40,
                "\x01\x4d" => -40,  "\x00\xf8" => -40,  "\x00\xf5" => -40,  "\x00\x75" => -30,
                "\x00\xfa" => -30,  "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,
                "\x01\x71" => -30,  "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,
                "\x00\x79" => -50,  "\x00\xfd" => -50,  "\x00\xff" => -50,
            ],
            "\x01\x36" => [
                "\x00\x4f" => -50,  "\x00\xd3" => -50,  "\x00\xd4" => -50,  "\x00\xd6" => -50,
                "\x00\xd2" => -50,  "\x01\x50" => -50,  "\x01\x4c" => -50,  "\x00\xd8" => -50,
                "\x00\xd5" => -50,  "\x00\x65" => -40,  "\x00\xe9" => -40,  "\x01\x1b" => -40,
                "\x00\xea" => -40,  "\x00\xeb" => -40,  "\x01\x17" => -40,  "\x00\xe8" => -40,
                "\x01\x13" => -40,  "\x01\x19" => -40,  "\x00\x6f" => -40,  "\x00\xf3" => -40,
                "\x00\xf4" => -40,  "\x00\xf6" => -40,  "\x00\xf2" => -40,  "\x01\x51" => -40,
                "\x01\x4d" => -40,  "\x00\xf8" => -40,  "\x00\xf5" => -40,  "\x00\x75" => -30,
                "\x00\xfa" => -30,  "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,
                "\x01\x71" => -30,  "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,
                "\x00\x79" => -50,  "\x00\xfd" => -50,  "\x00\xff" => -50,
            ],
            "\x00\x4c" => [
                "\x00\x54" => -110, "\x01\x64" => -110, "\x01\x62" => -110, "\x00\x56" => -110,
                "\x00\x57" => -70,  "\x00\x59" => -140, "\x00\xdd" => -140, "\x01\x78" => -140,
                "\x20\x1d" => -140, "\x20\x19" => -160, "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x39" => [
                "\x00\x54" => -110, "\x01\x64" => -110, "\x01\x62" => -110, "\x00\x56" => -110,
                "\x00\x57" => -70,  "\x00\x59" => -140, "\x00\xdd" => -140, "\x01\x78" => -140,
                "\x20\x1d" => -140, "\x20\x19" => -160, "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x3d" => [
                "\x00\x54" => -110, "\x01\x64" => -110, "\x01\x62" => -110, "\x00\x56" => -110,
                "\x00\x57" => -70,  "\x00\x59" => -140, "\x00\xdd" => -140, "\x01\x78" => -140,
                "\x20\x1d" => -140, "\x20\x19" => -160, "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x3b" => [
                "\x00\x54" => -110, "\x01\x64" => -110, "\x01\x62" => -110, "\x00\x56" => -110,
                "\x00\x57" => -70,  "\x00\x59" => -140, "\x00\xdd" => -140, "\x01\x78" => -140,
                "\x20\x1d" => -140, "\x20\x19" => -160, "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x41" => [
                "\x00\x54" => -110, "\x01\x64" => -110, "\x01\x62" => -110, "\x00\x56" => -110,
                "\x00\x57" => -70,  "\x00\x59" => -140, "\x00\xdd" => -140, "\x01\x78" => -140,
                "\x20\x1d" => -140, "\x20\x19" => -160, "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\x4f" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\xd3" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\xd4" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\xd6" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\xd2" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x01\x50" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x01\x4c" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\xd8" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\xd5" => [
                "\x00\x41" => -20,  "\x00\xc1" => -20,  "\x01\x02" => -20,  "\x00\xc2" => -20,
                "\x00\xc4" => -20,  "\x00\xc0" => -20,  "\x01\x00" => -20,  "\x01\x04" => -20,
                "\x00\xc5" => -20,  "\x00\xc3" => -20,  "\x00\x54" => -40,  "\x01\x64" => -40,
                "\x01\x62" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x58" => -60,
                "\x00\x59" => -70,  "\x00\xdd" => -70,  "\x01\x78" => -70,  "\x00\x2c" => -40,
                "\x00\x2e" => -40,
            ],
            "\x00\x50" => [
                "\x00\x41" => -120, "\x00\xc1" => -120, "\x01\x02" => -120, "\x00\xc2" => -120,
                "\x00\xc4" => -120, "\x00\xc0" => -120, "\x01\x00" => -120, "\x01\x04" => -120,
                "\x00\xc5" => -120, "\x00\xc3" => -120, "\x00\x61" => -40,  "\x00\xe1" => -40,
                "\x01\x03" => -40,  "\x00\xe2" => -40,  "\x00\xe4" => -40,  "\x00\xe0" => -40,
                "\x01\x01" => -40,  "\x01\x05" => -40,  "\x00\xe5" => -40,  "\x00\xe3" => -40,
                "\x00\x2c" => -180, "\x00\x65" => -50,  "\x00\xe9" => -50,  "\x01\x1b" => -50,
                "\x00\xea" => -50,  "\x00\xeb" => -50,  "\x01\x17" => -50,  "\x00\xe8" => -50,
                "\x01\x13" => -50,  "\x01\x19" => -50,  "\x00\x6f" => -50,  "\x00\xf3" => -50,
                "\x00\xf4" => -50,  "\x00\xf6" => -50,  "\x00\xf2" => -50,  "\x01\x51" => -50,
                "\x01\x4d" => -50,  "\x00\xf8" => -50,  "\x00\xf5" => -50,  "\x00\x2e" => -180,
            ],
            "\x00\x51" => [
                "\x00\x55" => -10,  "\x00\xda" => -10,  "\x00\xdb" => -10,  "\x00\xdc" => -10,
                "\x00\xd9" => -10,  "\x01\x70" => -10,  "\x01\x6a" => -10,  "\x01\x72" => -10,
                "\x01\x6e" => -10,
            ],
            "\x00\x52" => [
                "\x00\x4f" => -20,  "\x00\xd3" => -20,  "\x00\xd4" => -20,  "\x00\xd6" => -20,
                "\x00\xd2" => -20,  "\x01\x50" => -20,  "\x01\x4c" => -20,  "\x00\xd8" => -20,
                "\x00\xd5" => -20,  "\x00\x54" => -30,  "\x01\x64" => -30,  "\x01\x62" => -30,
                "\x00\x55" => -40,  "\x00\xda" => -40,  "\x00\xdb" => -40,  "\x00\xdc" => -40,
                "\x00\xd9" => -40,  "\x01\x70" => -40,  "\x01\x6a" => -40,  "\x01\x72" => -40,
                "\x01\x6e" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x59" => -50,
                "\x00\xdd" => -50,  "\x01\x78" => -50,
            ],
            "\x01\x54" => [
                "\x00\x4f" => -20,  "\x00\xd3" => -20,  "\x00\xd4" => -20,  "\x00\xd6" => -20,
                "\x00\xd2" => -20,  "\x01\x50" => -20,  "\x01\x4c" => -20,  "\x00\xd8" => -20,
                "\x00\xd5" => -20,  "\x00\x54" => -30,  "\x01\x64" => -30,  "\x01\x62" => -30,
                "\x00\x55" => -40,  "\x00\xda" => -40,  "\x00\xdb" => -40,  "\x00\xdc" => -40,
                "\x00\xd9" => -40,  "\x01\x70" => -40,  "\x01\x6a" => -40,  "\x01\x72" => -40,
                "\x01\x6e" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x59" => -50,
                "\x00\xdd" => -50,  "\x01\x78" => -50,
            ],
            "\x01\x58" => [
                "\x00\x4f" => -20,  "\x00\xd3" => -20,  "\x00\xd4" => -20,  "\x00\xd6" => -20,
                "\x00\xd2" => -20,  "\x01\x50" => -20,  "\x01\x4c" => -20,  "\x00\xd8" => -20,
                "\x00\xd5" => -20,  "\x00\x54" => -30,  "\x01\x64" => -30,  "\x01\x62" => -30,
                "\x00\x55" => -40,  "\x00\xda" => -40,  "\x00\xdb" => -40,  "\x00\xdc" => -40,
                "\x00\xd9" => -40,  "\x01\x70" => -40,  "\x01\x6a" => -40,  "\x01\x72" => -40,
                "\x01\x6e" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x59" => -50,
                "\x00\xdd" => -50,  "\x01\x78" => -50,
            ],
            "\x01\x56" => [
                "\x00\x4f" => -20,  "\x00\xd3" => -20,  "\x00\xd4" => -20,  "\x00\xd6" => -20,
                "\x00\xd2" => -20,  "\x01\x50" => -20,  "\x01\x4c" => -20,  "\x00\xd8" => -20,
                "\x00\xd5" => -20,  "\x00\x54" => -30,  "\x01\x64" => -30,  "\x01\x62" => -30,
                "\x00\x55" => -40,  "\x00\xda" => -40,  "\x00\xdb" => -40,  "\x00\xdc" => -40,
                "\x00\xd9" => -40,  "\x01\x70" => -40,  "\x01\x6a" => -40,  "\x01\x72" => -40,
                "\x01\x6e" => -40,  "\x00\x56" => -50,  "\x00\x57" => -30,  "\x00\x59" => -50,
                "\x00\xdd" => -50,  "\x01\x78" => -50,
            ],
            "\x00\x53" => [
                "\x00\x2c" => -20,  "\x00\x2e" => -20,
            ],
            "\x01\x5a" => [
                "\x00\x2c" => -20,  "\x00\x2e" => -20,
            ],
            "\x01\x60" => [
                "\x00\x2c" => -20,  "\x00\x2e" => -20,
            ],
            "\x01\x5e" => [
                "\x00\x2c" => -20,  "\x00\x2e" => -20,
            ],
            "\x02\x18" => [
                "\x00\x2c" => -20,  "\x00\x2e" => -20,
            ],
            "\x00\x54" => [
                "\x00\x41" => -120, "\x00\xc1" => -120, "\x01\x02" => -120, "\x00\xc2" => -120,
                "\x00\xc4" => -120, "\x00\xc0" => -120, "\x01\x00" => -120, "\x01\x04" => -120,
                "\x00\xc5" => -120, "\x00\xc3" => -120, "\x00\x4f" => -40,  "\x00\xd3" => -40,
                "\x00\xd4" => -40,  "\x00\xd6" => -40,  "\x00\xd2" => -40,  "\x01\x50" => -40,
                "\x01\x4c" => -40,  "\x00\xd8" => -40,  "\x00\xd5" => -40,  "\x00\x61" => -120,
                "\x00\xe1" => -120, "\x01\x03" => -60,  "\x00\xe2" => -120, "\x00\xe4" => -120,
                "\x00\xe0" => -120, "\x01\x01" => -60,  "\x01\x05" => -120, "\x00\xe5" => -120,
                "\x00\xe3" => -60,  "\x00\x3a" => -20,  "\x00\x2c" => -120, "\x00\x65" => -120,
                "\x00\xe9" => -120, "\x01\x1b" => -120, "\x00\xea" => -120, "\x00\xeb" => -120,
                "\x01\x17" => -120, "\x00\xe8" => -60,  "\x01\x13" => -60,  "\x01\x19" => -120,
                "\x00\x2d" => -140, "\x00\x6f" => -120, "\x00\xf3" => -120, "\x00\xf4" => -120,
                "\x00\xf6" => -120, "\x00\xf2" => -120, "\x01\x51" => -120, "\x01\x4d" => -60,
                "\x00\xf8" => -120, "\x00\xf5" => -60,  "\x00\x2e" => -120, "\x00\x72" => -120,
                "\x01\x55" => -120, "\x01\x59" => -120, "\x01\x57" => -120, "\x00\x3b" => -20,
                "\x00\x75" => -120, "\x00\xfa" => -120, "\x00\xfb" => -120, "\x00\xfc" => -120,
                "\x00\xf9" => -120, "\x01\x71" => -120, "\x01\x6b" => -60,  "\x01\x73" => -120,
                "\x01\x6f" => -120, "\x00\x77" => -120, "\x00\x79" => -120, "\x00\xfd" => -120,
                "\x00\xff" => -60,
            ],
            "\x01\x64" => [
                "\x00\x41" => -120, "\x00\xc1" => -120, "\x01\x02" => -120, "\x00\xc2" => -120,
                "\x00\xc4" => -120, "\x00\xc0" => -120, "\x01\x00" => -120, "\x01\x04" => -120,
                "\x00\xc5" => -120, "\x00\xc3" => -120, "\x00\x4f" => -40,  "\x00\xd3" => -40,
                "\x00\xd4" => -40,  "\x00\xd6" => -40,  "\x00\xd2" => -40,  "\x01\x50" => -40,
                "\x01\x4c" => -40,  "\x00\xd8" => -40,  "\x00\xd5" => -40,  "\x00\x61" => -120,
                "\x00\xe1" => -120, "\x01\x03" => -60,  "\x00\xe2" => -120, "\x00\xe4" => -120,
                "\x00\xe0" => -120, "\x01\x01" => -60,  "\x01\x05" => -120, "\x00\xe5" => -120,
                "\x00\xe3" => -60,  "\x00\x3a" => -20,  "\x00\x2c" => -120, "\x00\x65" => -120,
                "\x00\xe9" => -120, "\x01\x1b" => -120, "\x00\xea" => -120, "\x00\xeb" => -120,
                "\x01\x17" => -120, "\x00\xe8" => -60,  "\x01\x13" => -60,  "\x01\x19" => -120,
                "\x00\x2d" => -140, "\x00\x6f" => -120, "\x00\xf3" => -120, "\x00\xf4" => -120,
                "\x00\xf6" => -120, "\x00\xf2" => -120, "\x01\x51" => -120, "\x01\x4d" => -60,
                "\x00\xf8" => -120, "\x00\xf5" => -60,  "\x00\x2e" => -120, "\x00\x72" => -120,
                "\x01\x55" => -120, "\x01\x59" => -120, "\x01\x57" => -120, "\x00\x3b" => -20,
                "\x00\x75" => -120, "\x00\xfa" => -120, "\x00\xfb" => -120, "\x00\xfc" => -120,
                "\x00\xf9" => -120, "\x01\x71" => -120, "\x01\x6b" => -60,  "\x01\x73" => -120,
                "\x01\x6f" => -120, "\x00\x77" => -120, "\x00\x79" => -120, "\x00\xfd" => -120,
                "\x00\xff" => -60,
            ],
            "\x01\x62" => [
                "\x00\x41" => -120, "\x00\xc1" => -120, "\x01\x02" => -120, "\x00\xc2" => -120,
                "\x00\xc4" => -120, "\x00\xc0" => -120, "\x01\x00" => -120, "\x01\x04" => -120,
                "\x00\xc5" => -120, "\x00\xc3" => -120, "\x00\x4f" => -40,  "\x00\xd3" => -40,
                "\x00\xd4" => -40,  "\x00\xd6" => -40,  "\x00\xd2" => -40,  "\x01\x50" => -40,
                "\x01\x4c" => -40,  "\x00\xd8" => -40,  "\x00\xd5" => -40,  "\x00\x61" => -120,
                "\x00\xe1" => -120, "\x01\x03" => -60,  "\x00\xe2" => -120, "\x00\xe4" => -120,
                "\x00\xe0" => -120, "\x01\x01" => -60,  "\x01\x05" => -120, "\x00\xe5" => -120,
                "\x00\xe3" => -60,  "\x00\x3a" => -20,  "\x00\x2c" => -120, "\x00\x65" => -120,
                "\x00\xe9" => -120, "\x01\x1b" => -120, "\x00\xea" => -120, "\x00\xeb" => -120,
                "\x01\x17" => -120, "\x00\xe8" => -60,  "\x01\x13" => -60,  "\x01\x19" => -120,
                "\x00\x2d" => -140, "\x00\x6f" => -120, "\x00\xf3" => -120, "\x00\xf4" => -120,
                "\x00\xf6" => -120, "\x00\xf2" => -120, "\x01\x51" => -120, "\x01\x4d" => -60,
                "\x00\xf8" => -120, "\x00\xf5" => -60,  "\x00\x2e" => -120, "\x00\x72" => -120,
                "\x01\x55" => -120, "\x01\x59" => -120, "\x01\x57" => -120, "\x00\x3b" => -20,
                "\x00\x75" => -120, "\x00\xfa" => -120, "\x00\xfb" => -120, "\x00\xfc" => -120,
                "\x00\xf9" => -120, "\x01\x71" => -120, "\x01\x6b" => -60,  "\x01\x73" => -120,
                "\x01\x6f" => -120, "\x00\x77" => -120, "\x00\x79" => -120, "\x00\xfd" => -120,
                "\x00\xff" => -60,
            ],
            "\x00\x55" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x00\xda" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x00\xdb" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x00\xdc" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x00\xd9" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x01\x70" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x01\x6a" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x01\x72" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x01\x6e" => [
                "\x00\x41" => -40,  "\x00\xc1" => -40,  "\x01\x02" => -40,  "\x00\xc2" => -40,
                "\x00\xc4" => -40,  "\x00\xc0" => -40,  "\x01\x00" => -40,  "\x01\x04" => -40,
                "\x00\xc5" => -40,  "\x00\xc3" => -40,  "\x00\x2c" => -40,  "\x00\x2e" => -40,
            ],
            "\x00\x56" => [
                "\x00\x41" => -80,  "\x00\xc1" => -80,  "\x01\x02" => -80,  "\x00\xc2" => -80,
                "\x00\xc4" => -80,  "\x00\xc0" => -80,  "\x01\x00" => -80,  "\x01\x04" => -80,
                "\x00\xc5" => -80,  "\x00\xc3" => -80,  "\x00\x47" => -40,  "\x01\x1e" => -40,
                "\x01\x22" => -40,  "\x00\x4f" => -40,  "\x00\xd3" => -40,  "\x00\xd4" => -40,
                "\x00\xd6" => -40,  "\x00\xd2" => -40,  "\x01\x50" => -40,  "\x01\x4c" => -40,
                "\x00\xd8" => -40,  "\x00\xd5" => -40,  "\x00\x61" => -70,  "\x00\xe1" => -70,
                "\x01\x03" => -70,  "\x00\xe2" => -70,  "\x00\xe4" => -70,  "\x00\xe0" => -70,
                "\x01\x01" => -70,  "\x01\x05" => -70,  "\x00\xe5" => -70,  "\x00\xe3" => -70,
                "\x00\x3a" => -40,  "\x00\x2c" => -125, "\x00\x65" => -80,  "\x00\xe9" => -80,
                "\x01\x1b" => -80,  "\x00\xea" => -80,  "\x00\xeb" => -80,  "\x01\x17" => -80,
                "\x00\xe8" => -80,  "\x01\x13" => -80,  "\x01\x19" => -80,  "\x00\x2d" => -80,
                "\x00\x6f" => -80,  "\x00\xf3" => -80,  "\x00\xf4" => -80,  "\x00\xf6" => -80,
                "\x00\xf2" => -80,  "\x01\x51" => -80,  "\x01\x4d" => -80,  "\x00\xf8" => -80,
                "\x00\xf5" => -80,  "\x00\x2e" => -125, "\x00\x3b" => -40,  "\x00\x75" => -70,
                "\x00\xfa" => -70,  "\x00\xfb" => -70,  "\x00\xfc" => -70,  "\x00\xf9" => -70,
                "\x01\x71" => -70,  "\x01\x6b" => -70,  "\x01\x73" => -70,  "\x01\x6f" => -70,
            ],
            "\x00\x57" => [
                "\x00\x41" => -50,  "\x00\xc1" => -50,  "\x01\x02" => -50,  "\x00\xc2" => -50,
                "\x00\xc4" => -50,  "\x00\xc0" => -50,  "\x01\x00" => -50,  "\x01\x04" => -50,
                "\x00\xc5" => -50,  "\x00\xc3" => -50,  "\x00\x4f" => -20,  "\x00\xd3" => -20,
                "\x00\xd4" => -20,  "\x00\xd6" => -20,  "\x00\xd2" => -20,  "\x01\x50" => -20,
                "\x01\x4c" => -20,  "\x00\xd8" => -20,  "\x00\xd5" => -20,  "\x00\x61" => -40,
                "\x00\xe1" => -40,  "\x01\x03" => -40,  "\x00\xe2" => -40,  "\x00\xe4" => -40,
                "\x00\xe0" => -40,  "\x01\x01" => -40,  "\x01\x05" => -40,  "\x00\xe5" => -40,
                "\x00\xe3" => -40,  "\x00\x2c" => -80,  "\x00\x65" => -30,  "\x00\xe9" => -30,
                "\x01\x1b" => -30,  "\x00\xea" => -30,  "\x00\xeb" => -30,  "\x01\x17" => -30,
                "\x00\xe8" => -30,  "\x01\x13" => -30,  "\x01\x19" => -30,  "\x00\x2d" => -40,
                "\x00\x6f" => -30,  "\x00\xf3" => -30,  "\x00\xf4" => -30,  "\x00\xf6" => -30,
                "\x00\xf2" => -30,  "\x01\x51" => -30,  "\x01\x4d" => -30,  "\x00\xf8" => -30,
                "\x00\xf5" => -30,  "\x00\x2e" => -80,  "\x00\x75" => -30,  "\x00\xfa" => -30,
                "\x00\xfb" => -30,  "\x00\xfc" => -30,  "\x00\xf9" => -30,  "\x01\x71" => -30,
                "\x01\x6b" => -30,  "\x01\x73" => -30,  "\x01\x6f" => -30,  "\x00\x79" => -20,
                "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\x59" => [
                "\x00\x41" => -110, "\x00\xc1" => -110, "\x01\x02" => -110, "\x00\xc2" => -110,
                "\x00\xc4" => -110, "\x00\xc0" => -110, "\x01\x00" => -110, "\x01\x04" => -110,
                "\x00\xc5" => -110, "\x00\xc3" => -110, "\x00\x4f" => -85,  "\x00\xd3" => -85,
                "\x00\xd4" => -85,  "\x00\xd6" => -85,  "\x00\xd2" => -85,  "\x01\x50" => -85,
                "\x01\x4c" => -85,  "\x00\xd8" => -85,  "\x00\xd5" => -85,  "\x00\x61" => -140,
                "\x00\xe1" => -140, "\x01\x03" => -70,  "\x00\xe2" => -140, "\x00\xe4" => -140,
                "\x00\xe0" => -140, "\x01\x01" => -70,  "\x01\x05" => -140, "\x00\xe5" => -140,
                "\x00\xe3" => -140, "\x00\x3a" => -60,  "\x00\x2c" => -140, "\x00\x65" => -140,
                "\x00\xe9" => -140, "\x01\x1b" => -140, "\x00\xea" => -140, "\x00\xeb" => -140,
                "\x01\x17" => -140, "\x00\xe8" => -140, "\x01\x13" => -70,  "\x01\x19" => -140,
                "\x00\x2d" => -140, "\x00\x69" => -20,  "\x00\xed" => -20,  "\x01\x2f" => -20,
                "\x00\x6f" => -140, "\x00\xf3" => -140, "\x00\xf4" => -140, "\x00\xf6" => -140,
                "\x00\xf2" => -140, "\x01\x51" => -140, "\x01\x4d" => -140, "\x00\xf8" => -140,
                "\x00\xf5" => -140, "\x00\x2e" => -140, "\x00\x3b" => -60,  "\x00\x75" => -110,
                "\x00\xfa" => -110, "\x00\xfb" => -110, "\x00\xfc" => -110, "\x00\xf9" => -110,
                "\x01\x71" => -110, "\x01\x6b" => -110, "\x01\x73" => -110, "\x01\x6f" => -110,
            ],
            "\x00\xdd" => [
                "\x00\x41" => -110, "\x00\xc1" => -110, "\x01\x02" => -110, "\x00\xc2" => -110,
                "\x00\xc4" => -110, "\x00\xc0" => -110, "\x01\x00" => -110, "\x01\x04" => -110,
                "\x00\xc5" => -110, "\x00\xc3" => -110, "\x00\x4f" => -85,  "\x00\xd3" => -85,
                "\x00\xd4" => -85,  "\x00\xd6" => -85,  "\x00\xd2" => -85,  "\x01\x50" => -85,
                "\x01\x4c" => -85,  "\x00\xd8" => -85,  "\x00\xd5" => -85,  "\x00\x61" => -140,
                "\x00\xe1" => -140, "\x01\x03" => -70,  "\x00\xe2" => -140, "\x00\xe4" => -140,
                "\x00\xe0" => -140, "\x01\x01" => -70,  "\x01\x05" => -140, "\x00\xe5" => -140,
                "\x00\xe3" => -70,  "\x00\x3a" => -60,  "\x00\x2c" => -140, "\x00\x65" => -140,
                "\x00\xe9" => -140, "\x01\x1b" => -140, "\x00\xea" => -140, "\x00\xeb" => -140,
                "\x01\x17" => -140, "\x00\xe8" => -140, "\x01\x13" => -70,  "\x01\x19" => -140,
                "\x00\x2d" => -140, "\x00\x69" => -20,  "\x00\xed" => -20,  "\x01\x2f" => -20,
                "\x00\x6f" => -140, "\x00\xf3" => -140, "\x00\xf4" => -140, "\x00\xf6" => -140,
                "\x00\xf2" => -140, "\x01\x51" => -140, "\x01\x4d" => -70,  "\x00\xf8" => -140,
                "\x00\xf5" => -140, "\x00\x2e" => -140, "\x00\x3b" => -60,  "\x00\x75" => -110,
                "\x00\xfa" => -110, "\x00\xfb" => -110, "\x00\xfc" => -110, "\x00\xf9" => -110,
                "\x01\x71" => -110, "\x01\x6b" => -110, "\x01\x73" => -110, "\x01\x6f" => -110,
            ],
            "\x01\x78" => [
                "\x00\x41" => -110, "\x00\xc1" => -110, "\x01\x02" => -110, "\x00\xc2" => -110,
                "\x00\xc4" => -110, "\x00\xc0" => -110, "\x01\x00" => -110, "\x01\x04" => -110,
                "\x00\xc5" => -110, "\x00\xc3" => -110, "\x00\x4f" => -85,  "\x00\xd3" => -85,
                "\x00\xd4" => -85,  "\x00\xd6" => -85,  "\x00\xd2" => -85,  "\x01\x50" => -85,
                "\x01\x4c" => -85,  "\x00\xd8" => -85,  "\x00\xd5" => -85,  "\x00\x61" => -140,
                "\x00\xe1" => -140, "\x01\x03" => -70,  "\x00\xe2" => -140, "\x00\xe4" => -140,
                "\x00\xe0" => -140, "\x01\x01" => -70,  "\x01\x05" => -140, "\x00\xe5" => -140,
                "\x00\xe3" => -70,  "\x00\x3a" => -60,  "\x00\x2c" => -140, "\x00\x65" => -140,
                "\x00\xe9" => -140, "\x01\x1b" => -140, "\x00\xea" => -140, "\x00\xeb" => -140,
                "\x01\x17" => -140, "\x00\xe8" => -140, "\x01\x13" => -70,  "\x01\x19" => -140,
                "\x00\x2d" => -140, "\x00\x69" => -20,  "\x00\xed" => -20,  "\x01\x2f" => -20,
                "\x00\x6f" => -140, "\x00\xf3" => -140, "\x00\xf4" => -140, "\x00\xf6" => -140,
                "\x00\xf2" => -140, "\x01\x51" => -140, "\x01\x4d" => -140, "\x00\xf8" => -140,
                "\x00\xf5" => -140, "\x00\x2e" => -140, "\x00\x3b" => -60,  "\x00\x75" => -110,
                "\x00\xfa" => -110, "\x00\xfb" => -110, "\x00\xfc" => -110, "\x00\xf9" => -110,
                "\x01\x71" => -110, "\x01\x6b" => -110, "\x01\x73" => -110, "\x01\x6f" => -110,
            ],
            "\x00\x61" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\xe1" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x03" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\xe2" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\xe4" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\xe0" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x01" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x01\x05" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\xe5" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\xe3" => [
                "\x00\x76" => -20,  "\x00\x77" => -20,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\x62" => [
                "\x00\x62" => -10,  "\x00\x2c" => -40,  "\x00\x6c" => -20,  "\x01\x3a" => -20,
                "\x01\x3c" => -20,  "\x01\x42" => -20,  "\x00\x2e" => -40,  "\x00\x75" => -20,
                "\x00\xfa" => -20,  "\x00\xfb" => -20,  "\x00\xfc" => -20,  "\x00\xf9" => -20,
                "\x01\x71" => -20,  "\x01\x6b" => -20,  "\x01\x73" => -20,  "\x01\x6f" => -20,
                "\x00\x76" => -20,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\x63" => [
                "\x00\x2c" => -15,  "\x00\x6b" => -20,  "\x01\x37" => -20,
            ],
            "\x01\x07" => [
                "\x00\x2c" => -15,  "\x00\x6b" => -20,  "\x01\x37" => -20,
            ],
            "\x01\x0d" => [
                "\x00\x2c" => -15,  "\x00\x6b" => -20,  "\x01\x37" => -20,
            ],
            "\x00\xe7" => [
                "\x00\x2c" => -15,  "\x00\x6b" => -20,  "\x01\x37" => -20,
            ],
            "\x00\x3a" => [
                "\x00\x20" => -50,
            ],
            "\x00\x2c" => [
                "\x20\x1d" => -100, "\x20\x19" => -100,
            ],
            "\x00\x65" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\xe9" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x01\x1b" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\xea" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\xeb" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x01\x17" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\xe8" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x01\x13" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x01\x19" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x76" => -30,  "\x00\x77" => -20,
                "\x00\x78" => -30,  "\x00\x79" => -20,  "\x00\xfd" => -20,  "\x00\xff" => -20,
            ],
            "\x00\x66" => [
                "\x00\x61" => -30,  "\x00\xe1" => -30,  "\x01\x03" => -30,  "\x00\xe2" => -30,
                "\x00\xe4" => -30,  "\x00\xe0" => -30,  "\x01\x01" => -30,  "\x01\x05" => -30,
                "\x00\xe5" => -30,  "\x00\xe3" => -30,  "\x00\x2c" => -30,  "\x01\x31" => -28,
                "\x00\x65" => -30,  "\x00\xe9" => -30,  "\x01\x1b" => -30,  "\x00\xea" => -30,
                "\x00\xeb" => -30,  "\x01\x17" => -30,  "\x00\xe8" => -30,  "\x01\x13" => -30,
                "\x01\x19" => -30,  "\x00\x6f" => -30,  "\x00\xf3" => -30,  "\x00\xf4" => -30,
                "\x00\xf6" => -30,  "\x00\xf2" => -30,  "\x01\x51" => -30,  "\x01\x4d" => -30,
                "\x00\xf8" => -30,  "\x00\xf5" => -30,  "\x00\x2e" => -30,  "\x20\x1d" => 60,
                "\x20\x19" => 50,
            ],
            "\x00\x67" => [
                "\x00\x72" => -10,  "\x01\x55" => -10,  "\x01\x59" => -10,  "\x01\x57" => -10,
            ],
            "\x01\x1f" => [
                "\x00\x72" => -10,  "\x01\x55" => -10,  "\x01\x59" => -10,  "\x01\x57" => -10,
            ],
            "\x01\x23" => [
                "\x00\x72" => -10,  "\x01\x55" => -10,  "\x01\x59" => -10,  "\x01\x57" => -10,
            ],
            "\x00\x68" => [
                "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\x6b" => [
                "\x00\x65" => -20,  "\x00\xe9" => -20,  "\x01\x1b" => -20,  "\x00\xea" => -20,
                "\x00\xeb" => -20,  "\x01\x17" => -20,  "\x00\xe8" => -20,  "\x01\x13" => -20,
                "\x01\x19" => -20,  "\x00\x6f" => -20,  "\x00\xf3" => -20,  "\x00\xf4" => -20,
                "\x00\xf6" => -20,  "\x00\xf2" => -20,  "\x01\x51" => -20,  "\x01\x4d" => -20,
                "\x00\xf8" => -20,  "\x00\xf5" => -20,
            ],
            "\x01\x37" => [
                "\x00\x65" => -20,  "\x00\xe9" => -20,  "\x01\x1b" => -20,  "\x00\xea" => -20,
                "\x00\xeb" => -20,  "\x01\x17" => -20,  "\x00\xe8" => -20,  "\x01\x13" => -20,
                "\x01\x19" => -20,  "\x00\x6f" => -20,  "\x00\xf3" => -20,  "\x00\xf4" => -20,
                "\x00\xf6" => -20,  "\x00\xf2" => -20,  "\x01\x51" => -20,  "\x01\x4d" => -20,
                "\x00\xf8" => -20,  "\x00\xf5" => -20,
            ],
            "\x00\x6d" => [
                "\x00\x75" => -10,  "\x00\xfa" => -10,  "\x00\xfb" => -10,  "\x00\xfc" => -10,
                "\x00\xf9" => -10,  "\x01\x71" => -10,  "\x01\x6b" => -10,  "\x01\x73" => -10,
                "\x01\x6f" => -10,  "\x00\x79" => -15,  "\x00\xfd" => -15,  "\x00\xff" => -15,
            ],
            "\x00\x6e" => [
                "\x00\x75" => -10,  "\x00\xfa" => -10,  "\x00\xfb" => -10,  "\x00\xfc" => -10,
                "\x00\xf9" => -10,  "\x01\x71" => -10,  "\x01\x6b" => -10,  "\x01\x73" => -10,
                "\x01\x6f" => -10,  "\x00\x76" => -20,  "\x00\x79" => -15,  "\x00\xfd" => -15,
                "\x00\xff" => -15,
            ],
            "\x01\x44" => [
                "\x00\x75" => -10,  "\x00\xfa" => -10,  "\x00\xfb" => -10,  "\x00\xfc" => -10,
                "\x00\xf9" => -10,  "\x01\x71" => -10,  "\x01\x6b" => -10,  "\x01\x73" => -10,
                "\x01\x6f" => -10,  "\x00\x76" => -20,  "\x00\x79" => -15,  "\x00\xfd" => -15,
                "\x00\xff" => -15,
            ],
            "\x01\x48" => [
                "\x00\x75" => -10,  "\x00\xfa" => -10,  "\x00\xfb" => -10,  "\x00\xfc" => -10,
                "\x00\xf9" => -10,  "\x01\x71" => -10,  "\x01\x6b" => -10,  "\x01\x73" => -10,
                "\x01\x6f" => -10,  "\x00\x76" => -20,  "\x00\x79" => -15,  "\x00\xfd" => -15,
                "\x00\xff" => -15,
            ],
            "\x01\x46" => [
                "\x00\x75" => -10,  "\x00\xfa" => -10,  "\x00\xfb" => -10,  "\x00\xfc" => -10,
                "\x00\xf9" => -10,  "\x01\x71" => -10,  "\x01\x6b" => -10,  "\x01\x73" => -10,
                "\x01\x6f" => -10,  "\x00\x76" => -20,  "\x00\x79" => -15,  "\x00\xfd" => -15,
                "\x00\xff" => -15,
            ],
            "\x00\xf1" => [
                "\x00\x75" => -10,  "\x00\xfa" => -10,  "\x00\xfb" => -10,  "\x00\xfc" => -10,
                "\x00\xf9" => -10,  "\x01\x71" => -10,  "\x01\x6b" => -10,  "\x01\x73" => -10,
                "\x01\x6f" => -10,  "\x00\x76" => -20,  "\x00\x79" => -15,  "\x00\xfd" => -15,
                "\x00\xff" => -15,
            ],
            "\x00\x6f" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\xf3" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\xf4" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\xf6" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\xf2" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x01\x51" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x01\x4d" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\xf8" => [
                "\x00\x61" => -55,  "\x00\xe1" => -55,  "\x01\x03" => -55,  "\x00\xe2" => -55,
                "\x00\xe4" => -55,  "\x00\xe0" => -55,  "\x01\x01" => -55,  "\x01\x05" => -55,
                "\x00\xe5" => -55,  "\x00\xe3" => -55,  "\x00\x62" => -55,  "\x00\x63" => -55,
                "\x01\x07" => -55,  "\x01\x0d" => -55,  "\x00\xe7" => -55,  "\x00\x2c" => -95,
                "\x00\x64" => -55,  "\x01\x11" => -55,  "\x00\x65" => -55,  "\x00\xe9" => -55,
                "\x01\x1b" => -55,  "\x00\xea" => -55,  "\x00\xeb" => -55,  "\x01\x17" => -55,
                "\x00\xe8" => -55,  "\x01\x13" => -55,  "\x01\x19" => -55,  "\x00\x66" => -55,
                "\x00\x67" => -55,  "\x01\x1f" => -55,  "\x01\x23" => -55,  "\x00\x68" => -55,
                "\x00\x69" => -55,  "\x00\xed" => -55,  "\x00\xee" => -55,  "\x00\xef" => -55,
                "\x00\xec" => -55,  "\x01\x2b" => -55,  "\x01\x2f" => -55,  "\x00\x6a" => -55,
                "\x00\x6b" => -55,  "\x01\x37" => -55,  "\x00\x6c" => -55,  "\x01\x3a" => -55,
                "\x01\x3c" => -55,  "\x01\x42" => -55,  "\x00\x6d" => -55,  "\x00\x6e" => -55,
                "\x01\x44" => -55,  "\x01\x48" => -55,  "\x01\x46" => -55,  "\x00\xf1" => -55,
                "\x00\x6f" => -55,  "\x00\xf3" => -55,  "\x00\xf4" => -55,  "\x00\xf6" => -55,
                "\x00\xf2" => -55,  "\x01\x51" => -55,  "\x01\x4d" => -55,  "\x00\xf8" => -55,
                "\x00\xf5" => -55,  "\x00\x70" => -55,  "\x00\x2e" => -95,  "\x00\x71" => -55,
                "\x00\x72" => -55,  "\x01\x55" => -55,  "\x01\x59" => -55,  "\x01\x57" => -55,
                "\x00\x73" => -55,  "\x01\x5b" => -55,  "\x01\x61" => -55,  "\x01\x5f" => -55,
                "\x02\x19" => -55,  "\x00\x74" => -55,  "\x01\x63" => -55,  "\x00\x75" => -55,
                "\x00\xfa" => -55,  "\x00\xfb" => -55,  "\x00\xfc" => -55,  "\x00\xf9" => -55,
                "\x01\x71" => -55,  "\x01\x6b" => -55,  "\x01\x73" => -55,  "\x01\x6f" => -55,
                "\x00\x76" => -70,  "\x00\x77" => -70,  "\x00\x78" => -85,  "\x00\x79" => -70,
                "\x00\xfd" => -70,  "\x00\xff" => -70,  "\x00\x7a" => -55,  "\x01\x7a" => -55,
                "\x01\x7e" => -55,  "\x01\x7c" => -55,
            ],
            "\x00\xf5" => [
                "\x00\x2c" => -40,  "\x00\x2e" => -40,  "\x00\x76" => -15,  "\x00\x77" => -15,
                "\x00\x78" => -30,  "\x00\x79" => -30,  "\x00\xfd" => -30,  "\x00\xff" => -30,
            ],
            "\x00\x70" => [
                "\x00\x2c" => -35,  "\x00\x2e" => -35,  "\x00\x79" => -30,  "\x00\xfd" => -30,
                "\x00\xff" => -30,
            ],
            "\x00\x2e" => [
                "\x20\x1d" => -100, "\x20\x19" => -100, "\x00\x20" => -60,
            ],
            "\x20\x1d" => [
                "\x00\x20" => -40,
            ],
            "\x20\x18" => [
                "\x20\x18" => -57,
            ],
            "\x20\x19" => [
                "\x00\x64" => -50,  "\x01\x11" => -50,  "\x20\x19" => -57,  "\x00\x72" => -50,
                "\x01\x55" => -50,  "\x01\x59" => -50,  "\x01\x57" => -50,  "\x00\x73" => -50,
                "\x01\x5b" => -50,  "\x01\x61" => -50,  "\x01\x5f" => -50,  "\x02\x19" => -50,
                "\x00\x20" => -70,
            ],
            "\x00\x72" => [
                "\x00\x61" => -10,  "\x00\xe1" => -10,  "\x01\x03" => -10,  "\x00\xe2" => -10,
                "\x00\xe4" => -10,  "\x00\xe0" => -10,  "\x01\x01" => -10,  "\x01\x05" => -10,
                "\x00\xe5" => -10,  "\x00\xe3" => -10,  "\x00\x3a" => 30,   "\x00\x2c" => -50,
                "\x00\x69" => 15,   "\x00\xed" => 15,   "\x00\xee" => 15,   "\x00\xef" => 15,
                "\x00\xec" => 15,   "\x01\x2b" => 15,   "\x01\x2f" => 15,   "\x00\x6b" => 15,
                "\x01\x37" => 15,   "\x00\x6c" => 15,   "\x01\x3a" => 15,   "\x01\x3c" => 15,
                "\x01\x42" => 15,   "\x00\x6d" => 25,   "\x00\x6e" => 25,   "\x01\x44" => 25,
                "\x01\x48" => 25,   "\x01\x46" => 25,   "\x00\xf1" => 25,   "\x00\x70" => 30,
                "\x00\x2e" => -50,  "\x00\x3b" => 30,   "\x00\x74" => 40,   "\x01\x63" => 40,
                "\x00\x75" => 15,   "\x00\xfa" => 15,   "\x00\xfb" => 15,   "\x00\xfc" => 15,
                "\x00\xf9" => 15,   "\x01\x71" => 15,   "\x01\x6b" => 15,   "\x01\x73" => 15,
                "\x01\x6f" => 15,   "\x00\x76" => 30,   "\x00\x79" => 30,   "\x00\xfd" => 30,
                "\x00\xff" => 30,
            ],
            "\x01\x55" => [
                "\x00\x61" => -10,  "\x00\xe1" => -10,  "\x01\x03" => -10,  "\x00\xe2" => -10,
                "\x00\xe4" => -10,  "\x00\xe0" => -10,  "\x01\x01" => -10,  "\x01\x05" => -10,
                "\x00\xe5" => -10,  "\x00\xe3" => -10,  "\x00\x3a" => 30,   "\x00\x2c" => -50,
                "\x00\x69" => 15,   "\x00\xed" => 15,   "\x00\xee" => 15,   "\x00\xef" => 15,
                "\x00\xec" => 15,   "\x01\x2b" => 15,   "\x01\x2f" => 15,   "\x00\x6b" => 15,
                "\x01\x37" => 15,   "\x00\x6c" => 15,   "\x01\x3a" => 15,   "\x01\x3c" => 15,
                "\x01\x42" => 15,   "\x00\x6d" => 25,   "\x00\x6e" => 25,   "\x01\x44" => 25,
                "\x01\x48" => 25,   "\x01\x46" => 25,   "\x00\xf1" => 25,   "\x00\x70" => 30,
                "\x00\x2e" => -50,  "\x00\x3b" => 30,   "\x00\x74" => 40,   "\x01\x63" => 40,
                "\x00\x75" => 15,   "\x00\xfa" => 15,   "\x00\xfb" => 15,   "\x00\xfc" => 15,
                "\x00\xf9" => 15,   "\x01\x71" => 15,   "\x01\x6b" => 15,   "\x01\x73" => 15,
                "\x01\x6f" => 15,   "\x00\x76" => 30,   "\x00\x79" => 30,   "\x00\xfd" => 30,
                "\x00\xff" => 30,
            ],
            "\x01\x59" => [
                "\x00\x61" => -10,  "\x00\xe1" => -10,  "\x01\x03" => -10,  "\x00\xe2" => -10,
                "\x00\xe4" => -10,  "\x00\xe0" => -10,  "\x01\x01" => -10,  "\x01\x05" => -10,
                "\x00\xe5" => -10,  "\x00\xe3" => -10,  "\x00\x3a" => 30,   "\x00\x2c" => -50,
                "\x00\x69" => 15,   "\x00\xed" => 15,   "\x00\xee" => 15,   "\x00\xef" => 15,
                "\x00\xec" => 15,   "\x01\x2b" => 15,   "\x01\x2f" => 15,   "\x00\x6b" => 15,
                "\x01\x37" => 15,   "\x00\x6c" => 15,   "\x01\x3a" => 15,   "\x01\x3c" => 15,
                "\x01\x42" => 15,   "\x00\x6d" => 25,   "\x00\x6e" => 25,   "\x01\x44" => 25,
                "\x01\x48" => 25,   "\x01\x46" => 25,   "\x00\xf1" => 25,   "\x00\x70" => 30,
                "\x00\x2e" => -50,  "\x00\x3b" => 30,   "\x00\x74" => 40,   "\x01\x63" => 40,
                "\x00\x75" => 15,   "\x00\xfa" => 15,   "\x00\xfb" => 15,   "\x00\xfc" => 15,
                "\x00\xf9" => 15,   "\x01\x71" => 15,   "\x01\x6b" => 15,   "\x01\x73" => 15,
                "\x01\x6f" => 15,   "\x00\x76" => 30,   "\x00\x79" => 30,   "\x00\xfd" => 30,
                "\x00\xff" => 30,
            ],
            "\x01\x57" => [
                "\x00\x61" => -10,  "\x00\xe1" => -10,  "\x01\x03" => -10,  "\x00\xe2" => -10,
                "\x00\xe4" => -10,  "\x00\xe0" => -10,  "\x01\x01" => -10,  "\x01\x05" => -10,
                "\x00\xe5" => -10,  "\x00\xe3" => -10,  "\x00\x3a" => 30,   "\x00\x2c" => -50,
                "\x00\x69" => 15,   "\x00\xed" => 15,   "\x00\xee" => 15,   "\x00\xef" => 15,
                "\x00\xec" => 15,   "\x01\x2b" => 15,   "\x01\x2f" => 15,   "\x00\x6b" => 15,
                "\x01\x37" => 15,   "\x00\x6c" => 15,   "\x01\x3a" => 15,   "\x01\x3c" => 15,
                "\x01\x42" => 15,   "\x00\x6d" => 25,   "\x00\x6e" => 25,   "\x01\x44" => 25,
                "\x01\x48" => 25,   "\x01\x46" => 25,   "\x00\xf1" => 25,   "\x00\x70" => 30,
                "\x00\x2e" => -50,  "\x00\x3b" => 30,   "\x00\x74" => 40,   "\x01\x63" => 40,
                "\x00\x75" => 15,   "\x00\xfa" => 15,   "\x00\xfb" => 15,   "\x00\xfc" => 15,
                "\x00\xf9" => 15,   "\x01\x71" => 15,   "\x01\x6b" => 15,   "\x01\x73" => 15,
                "\x01\x6f" => 15,   "\x00\x76" => 30,   "\x00\x79" => 30,   "\x00\xfd" => 30,
                "\x00\xff" => 30,
            ],
            "\x00\x73" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x77" => -30,
            ],
            "\x01\x5b" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x77" => -30,
            ],
            "\x01\x61" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x77" => -30,
            ],
            "\x01\x5f" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x77" => -30,
            ],
            "\x02\x19" => [
                "\x00\x2c" => -15,  "\x00\x2e" => -15,  "\x00\x77" => -30,
            ],
            "\x00\x3b" => [
                "\x00\x20" => -50,
            ],
            "\x00\x20" => [
                "\x00\x54" => -50,  "\x01\x64" => -50,  "\x01\x62" => -50,  "\x00\x56" => -50,
                "\x00\x57" => -40,  "\x00\x59" => -90,  "\x00\xdd" => -90,  "\x01\x78" => -90,
                "\x20\x1c" => -30,  "\x20\x18" => -60,
            ],
            "\x00\x76" => [
                "\x00\x61" => -25,  "\x00\xe1" => -25,  "\x01\x03" => -25,  "\x00\xe2" => -25,
                "\x00\xe4" => -25,  "\x00\xe0" => -25,  "\x01\x01" => -25,  "\x01\x05" => -25,
                "\x00\xe5" => -25,  "\x00\xe3" => -25,  "\x00\x2c" => -80,  "\x00\x65" => -25,
                "\x00\xe9" => -25,  "\x01\x1b" => -25,  "\x00\xea" => -25,  "\x00\xeb" => -25,
                "\x01\x17" => -25,  "\x00\xe8" => -25,  "\x01\x13" => -25,  "\x01\x19" => -25,
                "\x00\x6f" => -25,  "\x00\xf3" => -25,  "\x00\xf4" => -25,  "\x00\xf6" => -25,
                "\x00\xf2" => -25,  "\x01\x51" => -25,  "\x01\x4d" => -25,  "\x00\xf8" => -25,
                "\x00\xf5" => -25,  "\x00\x2e" => -80,
            ],
            "\x00\x77" => [
                "\x00\x61" => -15,  "\x00\xe1" => -15,  "\x01\x03" => -15,  "\x00\xe2" => -15,
                "\x00\xe4" => -15,  "\x00\xe0" => -15,  "\x01\x01" => -15,  "\x01\x05" => -15,
                "\x00\xe5" => -15,  "\x00\xe3" => -15,  "\x00\x2c" => -60,  "\x00\x65" => -10,
                "\x00\xe9" => -10,  "\x01\x1b" => -10,  "\x00\xea" => -10,  "\x00\xeb" => -10,
                "\x01\x17" => -10,  "\x00\xe8" => -10,  "\x01\x13" => -10,  "\x01\x19" => -10,
                "\x00\x6f" => -10,  "\x00\xf3" => -10,  "\x00\xf4" => -10,  "\x00\xf6" => -10,
                "\x00\xf2" => -10,  "\x01\x51" => -10,  "\x01\x4d" => -10,  "\x00\xf8" => -10,
                "\x00\xf5" => -10,  "\x00\x2e" => -60,
            ],
            "\x00\x78" => [
                "\x00\x65" => -30,  "\x00\xe9" => -30,  "\x01\x1b" => -30,  "\x00\xea" => -30,
                "\x00\xeb" => -30,  "\x01\x17" => -30,  "\x00\xe8" => -30,  "\x01\x13" => -30,
                "\x01\x19" => -30,
            ],
            "\x00\x79" => [
                "\x00\x61" => -20,  "\x00\xe1" => -20,  "\x01\x03" => -20,  "\x00\xe2" => -20,
                "\x00\xe4" => -20,  "\x00\xe0" => -20,  "\x01\x01" => -20,  "\x01\x05" => -20,
                "\x00\xe5" => -20,  "\x00\xe3" => -20,  "\x00\x2c" => -100, "\x00\x65" => -20,
                "\x00\xe9" => -20,  "\x01\x1b" => -20,  "\x00\xea" => -20,  "\x00\xeb" => -20,
                "\x01\x17" => -20,  "\x00\xe8" => -20,  "\x01\x13" => -20,  "\x01\x19" => -20,
                "\x00\x6f" => -20,  "\x00\xf3" => -20,  "\x00\xf4" => -20,  "\x00\xf6" => -20,
                "\x00\xf2" => -20,  "\x01\x51" => -20,  "\x01\x4d" => -20,  "\x00\xf8" => -20,
                "\x00\xf5" => -20,  "\x00\x2e" => -100,
            ],
            "\x00\xfd" => [
                "\x00\x61" => -20,  "\x00\xe1" => -20,  "\x01\x03" => -20,  "\x00\xe2" => -20,
                "\x00\xe4" => -20,  "\x00\xe0" => -20,  "\x01\x01" => -20,  "\x01\x05" => -20,
                "\x00\xe5" => -20,  "\x00\xe3" => -20,  "\x00\x2c" => -100, "\x00\x65" => -20,
                "\x00\xe9" => -20,  "\x01\x1b" => -20,  "\x00\xea" => -20,  "\x00\xeb" => -20,
                "\x01\x17" => -20,  "\x00\xe8" => -20,  "\x01\x13" => -20,  "\x01\x19" => -20,
                "\x00\x6f" => -20,  "\x00\xf3" => -20,  "\x00\xf4" => -20,  "\x00\xf6" => -20,
                "\x00\xf2" => -20,  "\x01\x51" => -20,  "\x01\x4d" => -20,  "\x00\xf8" => -20,
                "\x00\xf5" => -20,  "\x00\x2e" => -100,
            ],
            "\x00\xff" => [
                "\x00\x61" => -20,  "\x00\xe1" => -20,  "\x01\x03" => -20,  "\x00\xe2" => -20,
                "\x00\xe4" => -20,  "\x00\xe0" => -20,  "\x01\x01" => -20,  "\x01\x05" => -20,
                "\x00\xe5" => -20,  "\x00\xe3" => -20,  "\x00\x2c" => -100, "\x00\x65" => -20,
                "\x00\xe9" => -20,  "\x01\x1b" => -20,  "\x00\xea" => -20,  "\x00\xeb" => -20,
                "\x01\x17" => -20,  "\x00\xe8" => -20,  "\x01\x13" => -20,  "\x01\x19" => -20,
                "\x00\x6f" => -20,  "\x00\xf3" => -20,  "\x00\xf4" => -20,  "\x00\xf6" => -20,
                "\x00\xf2" => -20,  "\x01\x51" => -20,  "\x01\x4d" => -20,  "\x00\xf8" => -20,
                "\x00\xf5" => -20,  "\x00\x2e" => -100,
            ],
            "\x00\x7a" => [
                "\x00\x65" => -15,  "\x00\xe9" => -15,  "\x01\x1b" => -15,  "\x00\xea" => -15,
                "\x00\xeb" => -15,  "\x01\x17" => -15,  "\x00\xe8" => -15,  "\x01\x13" => -15,
                "\x01\x19" => -15,  "\x00\x6f" => -15,  "\x00\xf3" => -15,  "\x00\xf4" => -15,
                "\x00\xf6" => -15,  "\x00\xf2" => -15,  "\x01\x51" => -15,  "\x01\x4d" => -15,
                "\x00\xf8" => -15,  "\x00\xf5" => -15,
            ],
            "\x01\x7a" => [
                "\x00\x65" => -15,  "\x00\xe9" => -15,  "\x01\x1b" => -15,  "\x00\xea" => -15,
                "\x00\xeb" => -15,  "\x01\x17" => -15,  "\x00\xe8" => -15,  "\x01\x13" => -15,
                "\x01\x19" => -15,  "\x00\x6f" => -15,  "\x00\xf3" => -15,  "\x00\xf4" => -15,
                "\x00\xf6" => -15,  "\x00\xf2" => -15,  "\x01\x51" => -15,  "\x01\x4d" => -15,
                "\x00\xf8" => -15,  "\x00\xf5" => -15,
            ],
            "\x01\x7e" => [
                "\x00\x65" => -15,  "\x00\xe9" => -15,  "\x01\x1b" => -15,  "\x00\xea" => -15,
                "\x00\xeb" => -15,  "\x01\x17" => -15,  "\x00\xe8" => -15,  "\x01\x13" => -15,
                "\x01\x19" => -15,  "\x00\x6f" => -15,  "\x00\xf3" => -15,  "\x00\xf4" => -15,
                "\x00\xf6" => -15,  "\x00\xf2" => -15,  "\x01\x51" => -15,  "\x01\x4d" => -15,
                "\x00\xf8" => -15,  "\x00\xf5" => -15,
            ],
            "\x01\x7c" => [
                "\x00\x65" => -15,  "\x00\xe9" => -15,  "\x01\x1b" => -15,  "\x00\xea" => -15,
                "\x00\xeb" => -15,  "\x01\x17" => -15,  "\x00\xe8" => -15,  "\x01\x13" => -15,
                "\x01\x19" => -15,  "\x00\x6f" => -15,  "\x00\xf3" => -15,  "\x00\xf4" => -15,
                "\x00\xf6" => -15,  "\x00\xf2" => -15,  "\x01\x51" => -15,  "\x01\x4d" => -15,
                "\x00\xf8" => -15,  "\x00\xf5" => -15,
            ],
        ];
    }
}
