version: "2"

options:
  verbose: true
  compose-file-path: './docker-compose.yml'
  max_attempt: 10

syncs:
  app-sync:
    src: './'
    sync_host_port: 10877
    sync_userid: '33'
    sync_args: '-prefer newer -copyonconflict'
    sync_excludes: ["public/vendor",
                    "public/images",
                    "public/fonts",
                    "storage",
                    "bitbucket-pipelines",
                    "docker",
                    "test-reports",
                    "tests-disabled",
                    ".idea",
                    ".git"]
    notify_terminal: true