<?php

namespace App\Enums\Meilisearch\Filters;

use Carbon\Carbon;

enum LastUpdated: string
{
    public const TODAY = 'today';
    public const YESTERDAY = 'yesterday';
    public const PAST_WEEK = 'past_week';
    public const PAST_MONTH = 'past_month';
    public const PAST_YEAR = 'past_year';
    public const CUSTOM = 'custom';

    public const ALL = [
        self::TODAY,
        self::YESTERDAY,
        self::PAST_WEEK,
        self::PAST_MONTH,
        self::PAST_YEAR,
        self::CUSTOM,
    ];

    public static function getDateForOption(string $option): string
    {
        $now = Carbon::now()->startOfDay();

        return (match ($option) {
            self::TODAY => $now,
            self::YESTERDAY => $now->subDay(),
            self::PAST_WEEK => $now->subWeek(),
            self::PAST_MONTH => $now->subMonth(),
            self::PAST_YEAR => $now->subYear(),
        })->timestamp;
    }
}
