<?php

namespace App\ValueObject\Pagination;

use App\ServiceTask;

class ApiTasksForUserFilter extends ApiTasksFilter
{
    public function __construct(
        public ?array $statuses,
        public ?array $types,
        public ?string $createdAfter,
        public ?string $updatedAfter,
        public ?string $sort = ServiceTask::ID,
        public ?string $direction = 'asc',
        public ?int $page = 1,
        public ?int $limit = 100
    ) {
        parent::__construct($statuses, $types, $createdAfter, $updatedAfter, $sort, $direction, $page, $limit);
    }
}
