<?php

namespace App\ValueObject\AuditCaseImporter;

use App\ValueObject\Xlsx;

/**
 * @deprecated Class is no longer needed; AuditCaseImporterService should be used.
 */
class PrivatePersonXlsx extends Xlsx
{
    private const INTERNAL_CLIENT_ID = 'Klantnummer';
    private const COMPANY_NAME1 = 'Voorletters';
    private const COMPANY_NAME2 = 'Voorvoegsel';
    private const COMPANY_NAME3 = 'Geslachtsnaam';
    private const ADDRESS1 = 'Privé correspondentieadres Straat';
    private const ADDRESS2 = 'Privé correspondentieadres (huis)nummer';
    private const ADDRESS3 = 'Privé correspondentieadres Postcode';
    private const ADDRESS4 = 'Privé correspondentieadres Plaats';
    private const VAT_NUMBER = 'OB/BTW nummer';
    private const BSN = 'BSN van de persoon';
    private const STATUS = 'Klantstatus';
    private const CLIENT = 'Klant';

    private const MANDATORY_FIELDS = [
        self::INTERNAL_CLIENT_ID,
        self::COMPANY_NAME1,
        self::COMPANY_NAME2,
        self::COMPANY_NAME3,
        self::ADDRESS1,
        self::ADDRESS2,
        self::ADDRESS3,
        self::ADDRESS4,
        self::VAT_NUMBER,
        self::BSN,
        self::STATUS
    ];

    private const INTERNAL_USER_FIELDS = [
        'Vestigingsmanager',
        'Declarant',
        'Relatiebeheerder',
        'Verantwoordelijk accountant',
        '1e Assistent',
        '2e Assistent',
        '3e Assistent',
        '4e Assistent',
        '5e Assistent',
        '6e Assistent',
        'Verantwoordelijk belastingadviseur',
        'Belasting assistent',
        '2e Belasting assistent',
        '3e Belasting assistent',
        '4e Belasting assistent',
        'Verantwoordelijk jurist',
        'Juridisch assistent',
        '2e Juridisch assistent',
        '3e Juridisch assistent',
        '4e Juridisch assistent',
        'Salarisadministrateur',
        'Salaris assistent',
        '2e Salaris assistent',
        '3e Salaris assistent',
        '4e Salaris assistent',
        'Management consultant',
        'Management consultancy assistent',
        '2e Management consultancy assistent',
        '3e Management consultancy assistent',
        '4e Management consultancy assistent'
    ];

    public function isValid()
    {
        return count(array_intersect(
            self::MANDATORY_FIELDS,
            $this->headers
        )) == count(self::MANDATORY_FIELDS);
    }

    public function internalClientId(array $row): string
    {
        return $row[self::INTERNAL_CLIENT_ID];
    }

    public function name(array $row): string
    {
        $name = !empty($row[self::COMPANY_NAME2]) ?
            $row[self::COMPANY_NAME1] . ' ' . $row[self::COMPANY_NAME2] . ' ' . $row[self::COMPANY_NAME3] :
            $row[self::COMPANY_NAME1] . ' ' . $row[self::COMPANY_NAME3];
        return html_entity_decode($name);
    }

    public function address(array $row): string
    {
        return $row[self::ADDRESS1] . ' ' . $row[self::ADDRESS2] . ' ' . $row[self::ADDRESS3] . ' ' . $row[self::ADDRESS4]; //phpcs:ignore
    }

    public function vatNumber(array $row): string
    {
        return $row[self::VAT_NUMBER];
    }

    public function bsnNumber(array $row): string
    {
        return str_replace('.', '', $row[self::BSN]);
    }

    public function readyToImport(array $row): bool
    {
        return $row[self::STATUS] === self::CLIENT;
    }

    public function internalUsers(array $row): array
    {
        $internalUsers = [];
        foreach (self::INTERNAL_USER_FIELDS as $field) {
            if (!empty($row[$field])) {
                $internalUsers[] = $row[$field];
            }
        }
        return array_unique($internalUsers);
    }
}
