<?php

namespace App\ValueObject\AuditCaseImporter;

use App\ValueObject\ServiceTask\BSN;
use App\ValueObject\MobileNumber;

/**
 * This ValueObject represents a single row (record) of an Auditcase user export XLSX file. It contains getter methods
 * for the relevant values.
 */
class UserRecord
{
    private array $row;
    private ?int $key;

    private const TYPE_CUSTOMER_PRIVATE_PERSON = 'contactpersoon (klant-particulier)';
    private const TYPE_CUSTOMER_PERSON = 'contactpersoon (klant-persoon)';
    private const TYPE_CUSTOMER_COMPANY = 'contactpersoon (klant-bedrijf)';

    // if type column contains one of these values: create or update user
    private const USER_TYPES = [
        self::TYPE_CUSTOMER_PRIVATE_PERSON,
        self::TYPE_CUSTOMER_PERSON,
        self::TYPE_CUSTOMER_COMPANY
    ];

    // if type column contains one of these values: create or update company
    private const COMPANY_TYPES = [
        'klant (particulier)',
        'klant (persoon)',
        'klant (bedrijf)'
    ];

    private const IGNORE_TYPES = [
        'relatie (particulier)',
        'relatie (bedrijf)',
        'contactpersoon (relatie-bedrijf)',
        'alg. correspondentie'
    ];

    private const TYPE = 'Type';
    private const CUSTOMER_ID = 'Klantnummer';
    private const STATUS = 'Status';
    private const COMPANY_NAME = 'Klantnaam';
    private const EMAIL = 'E-mail adres-direct (persoon)';
    private const EMAIL2 = 'Persoonlijk (e-mail adres)';
    private const FIRSTNAME = 'First Name';
    private const INITIALS = 'Voorletters';
    private const INFIX = 'Tussenvoegsel';
    private const LASTNAME = 'Eigen naam';
    private const MOBILE = 'Mobielnr. zakelijk (persoon)';
    private const MOBILE2 = 'Mobiel (persoon)';

    private const ADDRESS = 'Straatnaam + huisnr. (corr. adres)';
    private const POSTALCODE = 'Postcode (corr. adres)';
    private const CITY = 'Woonplaats (corr. adres)';
    private const COUNTRY = 'Land (corr. adres)';
    private const BSN = 'BSN';
    private const KVK_NUMBER = 'KvK nummer';
    private const FISCAL_NUMBER = 'VPB nummer';
    private const VAT_NUMBER = 'BTW nummer';
    private const WAGE_TAX_NUMBER = 'Loonheffingsnummer';
    private const WORKSTOP = 'Werkstop';
    private const TAG = 'Kantoor';

    private const INTERNAL_NAMES = ['Relatiebeheerder', 'Verantwoordelijk accountant', '1e Assistent', '2e Assistent',
        '3e Assistent', '4e Assistent', '5e Assistent', '6e Assistent', 'Salarisadministrateur', 'Salaris assistent',
        'Belasting assistent', '2e Belasting assistent', '3e Belasting assistent', '4e Belasting assistent',
        'Verantwoordelijk belastingadviseur',
        // For De Beer Accountants
        'Klantmanager', 'Opdrachtleider', 'Administratie', 'Fiscaal contactpersoon',
    ];

    private const REQUIRED_USER_FIELDS = [
        self::TYPE,
        self::EMAIL
    ];

    private const REQUIRED_COMPANY_FIELDS = [
        self::TYPE,
        self::COMPANY_NAME
    ];

    public function __construct(array $row, ?int $key = null)
    {
        $this->row = $row;
        $this->key = $key;
    }

    /**
     * @return int|null Identifier for the record. Corresponds with row number in spreadsheet file.
     */
    public function key(): ?int
    {
        return $this->key;
    }

    /**
     * @return string Content of cell in "Type" column.
     */
    public function type(): string
    {
        return $this->row[self::TYPE];
    }

    /**
     * @return bool TRUE if this record has a type that should be ignored.
     */
    public function ignoreType(): bool
    {
        return in_array($this->row[self::TYPE], self::IGNORE_TYPES);
    }

    /**
     * @return bool TRUE if type is contact person for a company
     */
    public function isCompanyContactPerson(): bool
    {
        return $this->type() == self::TYPE_CUSTOMER_COMPANY;
    }

    /**
     * @return bool TRUE if type is contact person for a person/particulier
     */
    public function isRegularContactPerson(): bool
    {
        return in_array($this->type(), [self::TYPE_CUSTOMER_PERSON, self::TYPE_CUSTOMER_PRIVATE_PERSON]);
    }

    /**
     * @return bool TRUE if record can be used to create/update a Hix user model.
     */
    public function isUser(): bool
    {
        return in_array($this->row[self::TYPE], self::USER_TYPES);
    }

    /**
     * @return bool TRUE if record can be used to create/update a Hix company model.
     */
    public function isCompany(): bool
    {
        return in_array($this->row[self::TYPE], self::COMPANY_TYPES);
    }

    /**
     * @return bool TRUE if the columns contain the minimal data required to create models.
     */
    public function isValid(): bool
    {
        if ($this->isUser()) {
            $required = self::REQUIRED_USER_FIELDS;
        } elseif ($this->isCompany()) {
            $required = self::REQUIRED_COMPANY_FIELDS;
        } else {
            return false;
        }

        foreach ($required as $key) {
            if (empty($this->row[$key])) {
                return false;
            }
        }
        return true;
    }

    /**
     * @return bool TRUE if the status value is "Klant".
     */
    public function isCustomer(): bool
    {
        return $this->row[self::STATUS] === 'Klant';
    }

    /**
     * @return bool TRUE if the status value is "Prospect".
     */
    public function isProspect(): bool
    {
        return $this->row[self::STATUS] === 'Prospect';
    }

    /**
     * @return string|null Internal client ID, is used as unique identifier for the records.
     */
    public function customerId(): ?string
    {
        if (!empty($this->row[self::CUSTOMER_ID])) {
            return $this->row[self::CUSTOMER_ID];
        }
        return null;
    }

    /**
     * If multiple addresses separated with ; (semi colon) exist return the first one.
     * @return string Email address of user.
     */
    public function email(): string
    {
        if ($this->isCompanyContactPerson()) {
            $email = $this->row[self::EMAIL];
        } elseif ($this->isRegularContactPerson()) {
            $email = $this->row[self::EMAIL2];
        } else {
            return '';
        }

        if ($p = strpos($email, ';') !== false) {
            $email = substr($email, 0, $p);
        }
        return $email;
    }

    /**
     * @return string Mobile phone number of user.
     */
    public function mobile(): string
    {
        if ($this->isCompanyContactPerson()) {
            $mobile = $this->row[self::MOBILE];
        } elseif ($this->isRegularContactPerson()) {
            $mobile = $this->row[self::MOBILE2];
        } else {
            return '';
        }
        try {
            return (string)new MobileNumber($mobile);
        } catch (\InvalidArgumentException $e) {
            // pass
        }
        return '';
    }

    /**
     * @return string First name of user.
     */
    public function firstname(): string
    {
        if (strlen($this->row[self::FIRSTNAME])) {
            return $this->row[self::FIRSTNAME];
        }
        return $this->row[self::INITIALS];
    }

    /**
     * @return string Last name of user, including "infix" ("de", "van der") for NL names.
     */
    public function lastname(): string
    {
        return trim($this->row[self::INFIX] . ' ' . $this->row[self::LASTNAME]);
    }

    /**
     * @return bool TRUE if the user should be blocked (or not created)
     */
    public function blocked(): bool
    {
        return $this->row[self::WORKSTOP] === 'Ja';
    }

    /**
     * @return string Name of company (not always the same in KvK register)
     */
    public function companyName(): string
    {
        return html_entity_decode($this->row[self::COMPANY_NAME]);
    }

    /**
     * @return string Company correspondence (mail) address. Can be different from visiting address.
     * Optional country name in NL.
     */
    public function companyAddress(): string
    {
        return trim($this->row[self::ADDRESS] . "\n" . $this->row[self::POSTALCODE] . ' ' . $this->row[self::CITY] . "\n" . $this->row[self::COUNTRY]); //phpcs:ignore
    }

    public function kvkNumber(): ?string
    {
        if (!empty($this->row[self::KVK_NUMBER])) {
            return $this->row[self::KVK_NUMBER];
        }
        return null;
    }

    public function fiscalNumber(): ?string
    {
        if (!empty($this->row[self::FISCAL_NUMBER])) {
            return $this->row[self::FISCAL_NUMBER];
        }
        return null;
    }

    public function vatNumber(): ?string
    {
        if (!empty($this->row[self::VAT_NUMBER])) {
            return $this->row[self::VAT_NUMBER];
        }
        return null;
    }

    public function wageTaxNumber(): ?string
    {
        if (!empty($this->row[self::WAGE_TAX_NUMBER])) {
            return $this->row[self::WAGE_TAX_NUMBER];
        }
        return null;
    }

    /**
     * @return string|null Optional BSN (Burger Service Nummer) of user
     */
    public function bsn(): ?string
    {
        if (!empty($this->row[self::BSN])) {
            return (string)new BSN($this->row[self::BSN]);
        }
        return null;
    }

    /**
     * @return array List of unique names (first name + last name) for internal users
     */
    public function internalNames(): array
    {
        $names = [];
        foreach (self::INTERNAL_NAMES as $col) {
            if (!empty($this->row[$col])) {
                $names[] = trim($this->row[$col]);
            }
        }
        return array_unique($names);
    }

    /**
     * @return string Maximum 20 characters, rest will be trimmed off.
     */
    public function tag(): string
    {
        return mb_substr(trim($this->row[self::TAG]), 0, 20);
    }
}
