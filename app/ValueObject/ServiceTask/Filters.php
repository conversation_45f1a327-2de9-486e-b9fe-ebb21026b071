<?php

namespace App\ValueObject\ServiceTask;

class Filters
{
    private array $years;
    private array $types;
    private array $uploadTypes;
    private string $tab;
    private ?array $initialFilters;

    public function __construct(
        string $tab,
        ?array $initialFilters = null,
        array $years = [],
        array $types = [],
        array $uploadTypes = []
    ) {
        $this->tab = $tab;
        $this->initialFilters = $initialFilters;
        $this->years = $years;
        $this->types = $types;
        $this->uploadTypes = $uploadTypes;
    }

    public function tab(): string
    {
        return $this->tab;
    }

    public function initialFilters(): ?array
    {
        return $this->initialFilters;
    }

    public function years(): array
    {
        return $this->years;
    }

    public function types(): array
    {
        return $this->types;
    }

    public function uploadTypes(): array
    {
        return $this->uploadTypes;
    }

    public function setYears(array $years): self
    {
        $this->years = $years;
        return $this;
    }

    public function setTypes(array $types): self
    {
        $this->types = $types;
        return $this;
    }

    public function setUploadTypes(array $uploadTypes): self
    {
        $this->uploadTypes = $uploadTypes;
        return $this;
    }
}
