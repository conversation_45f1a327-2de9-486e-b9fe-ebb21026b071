<?php

namespace App\ValueObject\Placeholder\Stamp;

use App\Support\Pdf\Signing\TextLine;

class InputTextPlaceholder extends StampPlaceholder
{
    public const DEFAULT_WIDTH = 180;
    public const DEFAULT_HEIGHT = 20;
    public const TEXT_ALIGNMENT = self::ALIGN_LEFT;

    public function getPdfObject(\SetaPDF_Core_Document $document): \SetaPDF_Core_XObject_Form
    {
        $font = new \SetaPDF_Core_Font_Type0_Subset($document, resource_path('fonts/Arial.ttf'));

        $textBlock = new TextLine($font, 12);
        $textBlock->setWidth($this->getWidth());
        $textBlock->setLineHeight(12);
        $textBlock->setText($this->text);
        $textBlock->setAlign(static::TEXT_ALIGNMENT);

        $xObject = \SetaPDF_Core_XObject_Form::create($document, [0, 0, $this->getWidth(), static::DEFAULT_HEIGHT]);
        $canvas = $this->getCanvas($xObject);

        $textBlock->draw($canvas, 4, 6);

        return $xObject;
    }
}
