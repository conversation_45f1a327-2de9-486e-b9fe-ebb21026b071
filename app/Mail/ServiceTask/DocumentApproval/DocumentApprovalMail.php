<?php

namespace App\Mail\ServiceTask\DocumentApproval;

use App\Interfaces\EmailTemplate\CustomizableMail;
use App\Mail\MailKey;
use App\Mail\ServiceTask\GenericApprovalMail;
use App\Objects\EmailTemplate\Content\TaskApprovalNeededContent;
use App\Objects\EmailTemplate\Content\TaskInformContent;
use App\ServiceTaskResponse;
use App\Traits\EmailTemplate\CustomizableMailTrait;

class DocumentApprovalMail extends GenericApprovalMail implements CustomizableMail
{
    use CustomizableMailTrait;

    public function getEmailTemplateEvent(): ?string
    {
        $task = $this->getTask();
        $response = $this->getResponse();
        if ($response->permission === ServiceTaskResponse::ACTION_INFORM || $task->isCompleted()) {
            if ($task->hasUploadType()) {
                return TaskInformContent::EVENT_TASK_UPLOAD_TYPE_INFORM;
            }
            return TaskInformContent::EVENT_TASK_DOCUMENT_INFORM;
        }

        if ($task->hasUploadType()) {
            return TaskApprovalNeededContent::EVENT_TASK_UPLOAD_TYPE_APPROVAL_REQUESTED;
        }
        return TaskApprovalNeededContent::EVENT_TASK_DOCUMENT_APPROVAL_REQUESTED;
    }

    public function getMailKey(): string
    {
        $task = $this->getTask();
        $response = $this->getResponse();
        if ($response->permission === ServiceTaskResponse::ACTION_INFORM || $task->isCompleted()) {
            if ($task->hasUploadType()) {
                return MailKey::SERVICE_TASK_INFORM_UPLOAD_TYPE;
            }
            return MailKey::SERVICE_TASK_INFORM;
        }

        if ($task->hasUploadType()) {
            return MailKey::SERVICE_TASK_APPROVAL_REQUESTED_UPLOAD_TYPE;
        }
        return MailKey::SERVICE_TASK_APPROVAL_REQUESTED;
    }
}
