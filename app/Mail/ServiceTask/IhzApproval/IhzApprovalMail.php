<?php

namespace App\Mail\ServiceTask\IhzApproval;

use App\Interfaces\EmailTemplate\CustomizableMail;
use App\Mail\ServiceTask\GenericApprovalMail;
use App\Objects\EmailTemplate\Content\TaskApprovalNeededContent;
use App\Objects\EmailTemplate\Content\TaskInformContent;
use App\ServiceTaskResponse;
use App\Traits\EmailTemplate\CustomizableMailTrait;
use App\ValueObject\Declarations\DeclarationData;

class IhzApprovalMail extends GenericApprovalMail implements CustomizableMail
{
    use CustomizableMailTrait;

    public function getEmailTemplateEvent(): ?string
    {
        $task = $this->getTask();
        $response = $this->getResponse();
        if ($response->permission === ServiceTaskResponse::ACTION_INFORM || $task->isCompleted()) {
            return TaskInformContent::EVENT_TASK_IHZ_INFORM;
        }
        return TaskApprovalNeededContent::EVENT_TASK_IHZ_APPROVAL_REQUESTED;
    }

    /**
     * Replace company with name of natural person if found.
     * @return array
     */
    public function getReplacements(): array
    {
        $replacements = parent::getReplacements();
        $name = array_get($this->getTask()->data, DeclarationData::NATURAL_PERSON_NAME);
        if (isset($name) && strlen($name)) {
            $replacements['company'] = $name;
        }
        return $replacements;
    }
}
