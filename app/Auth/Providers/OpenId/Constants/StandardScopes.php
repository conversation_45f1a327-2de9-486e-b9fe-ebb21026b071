<?php

namespace App\Auth\Providers\OpenId\Constants;

class StandardScopes
{
    /**
     *  REQUIRED. Informs the Authorization Server that the Client is making an OpenID Connect request.
     * If the 'openid' scope value is not present, the behavior is entirely unspecified.
     */
    public const OPENID = 'openid';
    /**
     * OPTIONAL. This scope value requests access to the End-User's default profile Claims, which are:
     * 'name', 'family_name', 'given_name', 'middle_name', 'nickname', 'preferred_username', 'profile', 'picture',
     * 'website', 'gender', 'birthdate', 'zoneinfo', 'locale', and 'updated_at'
     */
    public const PROFILE = 'profile';
    /**
     * This scope value requests access to the 'email' and 'email_verified' Claims.
     */
    public const EMAIL = 'email';
    /**
     * OPTIONAL. This scope value requests access to the 'address' Claim.
     */
    public const ADDRESS = 'address';
    /**
     * OPTIONAL. This scope value requests access to the 'phone_number' and 'phone_number_verified' Claims.
     */
    public const PHONE = 'phone';
    /**
     * This scope value MUST NOT be used with the OpenID Connect Implicit Client Implementer's Guide 1.0.
     * @see OpenID Connect Basic Client Implementer's Guide 1.0
     * (http://openid.net/specs/openid-connect-implicit-1_0.html#OpenID.Basic)
     * for its usage in that subset of OpenID Connect.
     */
    public const OFFLINE_ACCESS = 'offline_access';
}
