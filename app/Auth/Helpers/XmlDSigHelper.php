<?php

namespace App\Auth\Helpers;

use App\Helpers\Xml\Xml;
use DOMDocument;
use DOMElement;
use Exception;
use LightSaml\Credential\KeyHelper;
use LightSaml\Credential\X509Certificate;
use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecEnc;
use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityDSig;
use Rob<PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityKey;

// phpcs:ignoreFile
class XmlDSigHelper
{
    public function sign_enveloped(Xml $xml, $pki_id, DOMElement $beforeNode = null)
    {
        $objDSig = new XMLSecurityDSig();
        $sigdoc = new \DOMDocument();
        $sigdoc->loadXML('<ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:SignedInfo><ds:SignatureMethod /></ds:SignedInfo></ds:Signature>'); //phpcs:ignore
        $objDSig->sigNode = $sigdoc->documentElement;
        $objDSig->setCanonicalMethod(XMLSecurityDSig::EXC_C14N);
        $objDSig->addReference(
            $xml->getRootElement(),
            XMLSecurityDSig::SHA256,
            [
                'http://www.w3.org/2000/09/xmldsig#enveloped-signature',
                'http://www.w3.org/2001/10/xml-exc-c14n#',
            ],
            [
                'id_name' => $xml->getRootIdAttrName(),
                'overwrite' => false,
            ]
        );

        $objKey = new XMLSecurityKey(XMLSecurityKey::RSA_SHA256, array('type' => 'private'));
        $objKey->loadKey(vault_path('keys/' . $pki_id . '.sk'), true);
        $objDSig->sign($objKey);

        $objDSig->add509Cert(file_get_contents(vault_path('keys/' . $pki_id . '.crt')));

        $objDSig->insertSignature($xml->getRootElement(), $beforeNode);

        return $xml;
    }

    /**
     * @param DOMDocument $xml
     * @param string $pki_id
     * @param bool $key_by_id
     * @return bool|int
     * @throws Exception
     */
    public function verify_enveloped(
        DOMDocument $xml,
        $pki_id = "SecureLoginIdentityProviderSandbox",
        $key_by_id = true
    ) {
        $objXMLSecDSig = new XMLSecurityDSig();

        $objDSig = $objXMLSecDSig->locateSignature($xml);
        if (!$objDSig) {
            throw new Exception("Cannot locate Signature Node");
        }
        $objXMLSecDSig->canonicalizeSignedInfo();
        $objXMLSecDSig->idKeys = array('AssertionID');

        $retVal = $objXMLSecDSig->validateReference();
        if (!$retVal) {
            throw new Exception("Reference Validation Failed");
        }

        $objKey = $objXMLSecDSig->locateKey();
        if (!$objKey) {
            throw new Exception("We have no idea about the key");
        }
        //Important statement. In order to force verification with given key
        $key = null;

        $objKeyInfo = XMLSecEnc::staticLocateKeyInfo($objKey, $objDSig);
        if (!$objKeyInfo->key && empty($key)) {
            if ($key_by_id) {
                $objKey->loadKey(vault_path('keys/' . $pki_id . '.crt'), true);
            } else {
                $issuer_cert = new X509Certificate();
                $issuer_cert->setData($pki_id);
                $objKey = KeyHelper::createPublicKey($issuer_cert);
            }
        }

        return $objXMLSecDSig->verify($objKey);
    }

    public function encrypt($xml, $pki_id = "SecureLoginIdentityProviderSandbox")
    {
        $objSessionKey = new XMLSecurityKey(XMLSecurityKey::AES256_CBC);
        $objSessionKey->generateSessionKey();

        $objEncryptionKey = new XMLSecurityKey(XMLSecurityKey::RSA_SHA256, array('type' => 'public'));
        $objEncryptionKey->loadKey(vault_path('keys/' . $pki_id . '.crt'), true);

        $objEnc = new XmlSecEnc();
        $objEnc->setNode($xml->getRootElement());
        $objEnc->encryptKey($objEncryptionKey, $objSessionKey);

        $objEnc->type = XMLSecEnc::Element;
        $objEnc->encryptNode($objSessionKey);

        return $xml;
    }
}
