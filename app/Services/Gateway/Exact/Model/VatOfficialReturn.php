<?php

namespace App\Services\Gateway\Exact\Model;

use Picqer\Financials\Exact\Model;
use Picqer\Financials\Exact\Query;

/**
 * This class VatOfficialReturn was made because there is no class for it in
 * the Picqer library that we use for the Exact Online API.
 */
class VatOfficialReturn extends Model
{
    use Query\Findable;

    protected $fillable = [
        'Amount',
        'Created',
        'Creator',
        'CreatorFullName',
        'Description',
        'Division',
        'Document',
        'DocumentSubject',
        'Frequency',
        'IsCorrection',
        'Modified',
        'Modifier',
        'ModifierFullName',
        'Period'
    ];

    protected $url = 'read/financial/OfficialReturns';
}
