<?php

namespace App\Services\OpenQuestions\Templates;

use App\Account;
use App\Exceptions\ForbiddenException;
use App\Exceptions\Template\QuestionConditionNotSelectedException;
use App\Exceptions\Template\QuestionNotSelectedException;
use App\Exceptions\Template\SectionConditionNotSelectedException;
use App\Exceptions\ModelNotSavedException;
use App\Exceptions\Template\SectionNotSelectedException;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\OpenQuestions\Questions\Templates\TemplateAnswer;
use App\Models\OpenQuestions\Questions\Templates\TemplateBackup;
use App\Models\OpenQuestions\Questions\Templates\TemplateElement;
use App\Models\OpenQuestions\Questions\Templates\TemplateField;
use App\Models\OpenQuestions\Questions\Templates\TemplateFieldType;
use App\Repositories\Templates\DefaultTemplateRepository;
use App\Repositories\Templates\TemplateFieldRepository;
use App\Repositories\Templates\TemplateRepository;
use App\User;
use App\ValueObject\FormBuilder\FullSchema;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;

class TemplateService
{
    private const BLOCKED_FEATURES = [
        'remove' => false,
        'clone' => false,
        'move' => false,
        'edit' => false,
        'resize' => false,
    ];

    public function __construct(
        private readonly TemplateRepository $templateRepository,
        private readonly TemplateFieldService $templateFieldService,
        private readonly TemplateFieldRepository $templateFieldRepository,
        private readonly DefaultTemplateRepository $defaultTemplateRepository
    ) {
    }

    public function getById(int $id, User $user): Template
    {
        $template = $this->templateRepository->getById($id);

        if ($user->account_id !== $template->account_id) {
            throw new ForbiddenException('User account does not match provided template account');
        }

        return $template;
    }

    public function getByIds(array $ids): Collection
    {
        return $this->templateRepository->getByIds($ids);
    }

    public function createWithFields(Template $template, User $user, array $fields, ?bool $isDefault = false): Template
    {
        $template = $this->templateRepository->store($template);
        $this->templateFieldService->massCreate($template, $user, $fields);

        if ($isDefault && $user->isAdminUser()) {
            $this->defaultTemplateRepository->updateOrCreate($template->id);
        }

        return $template;
    }

    public function update(
        Template $template,
        array $attributes,
        User $user,
        array $fields,
        ?bool $isDefaultTemplate = false
    ): Template {
        $this->templateFieldService->deleteByTemplate($template);
        $this->templateFieldService->massCreate($template, $user, $fields);

        if ($isDefaultTemplate && $user->isAdminUser()) {
            $this->defaultTemplateRepository->updateOrCreate($template->id);
        } elseif (!$isDefaultTemplate && $user->isAdminUser()) {
            $this->defaultTemplateRepository->delete($template->id);
        }

        return $this->templateRepository->update($template, $attributes);
    }

    public function delete(int $template_id, User $user): bool
    {
        $template = $this->getById($template_id, $user);
        // If a template has already been sent to a company we archive it
        if ($template->entries()->exists()) {
            return $this->templateRepository->archive($template, $user);
        }
        // If not sent we just delete it completely
        return $this->templateRepository->delete($template);
    }

    public function getByAccountId(Account $account, array $with = []): Collection
    {
        return $this->templateRepository->getByAccountId($account->id, $with);
    }

    public function hasTemplates(Account $account): bool
    {
        return $this->templateRepository->hasTemplates($account->id);
    }

    public function getAvailableTemplates(Account $account): Collection
    {
        return $this->getByAccountId(
            $account,
            [
                'fields',
                'fields.children',
                'fields.children.template',
                'fields.children.children',
                'fields.template',
                'fields.template.fields',
                'entries',
                'entries.templateAnswers',
                'entries.templateAnswers.remark',
                'entries.openQuestion',
            ]
        );
    }

    public function indexCategory(string $category, Account $account): Collection
    {
        return $this->templateRepository->getByCategory($category, $account->id);
    }

    /**
     * Store backup of request body (JSON) from Vue form builder in MongoDB which can be used for versioning and debug
     * @param array $form
     * @param int|null $templateId
     * @param User $user
     * @return bool
     */
    public function storeBackup(array $form, ?int $templateId, User $user): bool
    {
        $backup = new TemplateBackup();
        $backup->setClientDatabaseConnection($user->account_id);
        if (!Schema::connection($backup->getConnectionName())->hasTable(TemplateBackup::COLLECTION)) {
            Schema::connection($backup->getConnectionName())->create(
                TemplateBackup::COLLECTION,
                function (Blueprint $collection) {
                    $collection->expire(TemplateBackup::CREATED_AT, 60);
                }
            );
        }

        $backup->form = $form;
        $backup->template_id = $templateId;
        $backup->user_uuid = $user->uuid;
        return $backup->save();
    }

    /**
     * Get data structure containing all fields, sections and other form elements needed by Vue form builder
     * @param Template $template
     * @return array multi dimensional array to encode to JSON
     */
    public function getCompleteSchema(Template $template): array
    {
        $fullSchema = new FullSchema(
            $this->getSchemaChildren($template->fields, null),
            ['name' => $template->name, 'elements' => []]
        );

        return [
            'id' => $template->id,
            'name' => $template->name,
            'category' => $template->category,
            'form' => $fullSchema->toArray()
        ];
    }

    /**
     * List of fields that are direct descendents of the supplied parent UUID
     * @param Collection<TemplateElement> $fields
     * @param string|null $parentUuid
     * @return Collection<TemplateElement>
     */
    private function filterChildFields(Collection $fields, ?string $parentUuid): Collection
    {
        return $fields->filter(function (TemplateElement $field) use ($parentUuid) {
            return $field->parent_uuid === $parentUuid;
        });
    }

    /**
     * Data structure containing a single level of fields.
     * @param Collection<TemplateElement> $fields
     * @param string|null $parentUuid
     * @return array
     */
    public function getSchemaChildren(Collection $fields, ?string $parentUuid): array
    {
        $schema = [];
        $filteredFields = $this->filterChildFields($fields, $parentUuid);
        foreach ($filteredFields as $field) {
            $key = $field->schema_key;
            $schema[$key] = $field->schema_content;
            if ($field instanceof TemplateAnswer && $field->isBasedOnField()) {
                $schema[$key]['builder'] = array_merge($schema[$key]['builder'], self::BLOCKED_FEATURES);
            }
            if ($field->type === TemplateFieldType::SECTION) {
                if (
                    isset($field->schema_content['builder']['type'])
                    && $field->schema_content['builder']['type'] === 'container'
                ) {
                    $schema[$key]['schema'] = $this->getSchemaChildren($fields, $field->uuid);
                } else {
                    throw new \UnexpectedValueException('Unexpected schema content for template section');
                }
            }
        }
        return $schema;
    }

    /**
     * Save fields to template.
     * Remove any existing fields that are not saved now.
     * @param Template $template Existing template to which fields are saved
     * @param TemplateField[] $fields Fields that should be saved with this template
     * @return void
     * @throws \Throwable
     */
    public function saveFields(Template $template, array $fields): void
    {
        // only load IDs of existing fields, we don't need the whole model.
        $fieldIds = $template->fields()->pluck('id')->toArray();
        $savedIds = [];

        foreach ($fields as $field) {
            if (isset($field->id)) {
                if (!in_array($field->id, $fieldIds)) {
                    throw new \UnexpectedValueException('Field ID ' . $field->id . ' does not belong to template #' . $template->id); // phpcs:ignore
                }
            } else {
                // save as new
                $field->template()->associate($template);
            }
            if ($field->save(['transaction' => false])) {
                $savedIds[] = $field->id;
            } else {
                throw new ModelNotSavedException('Save returned false while saving field to template #' . $template->id); // phpcs:ignore
            }
        }
        $this->templateFieldRepository->deleteExcluded($template->id, $savedIds);
    }

    /**
     * Validate condition of the selected fields.
     * @param Template $template Existing template which is being used.
     * @param Collection $fields These are the fields that are selected.
     * @return void
     */
    public function validateConditions(Template $template, Collection $fields): void
    {
        $templateFields = $template->fields()->orderBy(TemplateField::ID)->get();

        // Iterate through all the fields of the selected template.
        /** @var TemplateField $templateField */
        foreach ($templateFields as $templateField) {
            // Verify if the field has conditions.
            if ($templateField->conditions()) {
                // If it has conditions, verify whether the field is a section,
                // as the check differs slightly in that case.
                if ($templateField->type === TemplateFieldType::SECTION) {
                    $this->checkSectionCondition($templateFields, $templateField, $fields);
                } else {
                    $this->checkFieldCondition($templateFields, $templateField, $fields);
                }
            }
        }
    }

    /**
     * Check the conditions of a section
     *
     * @param Collection<TemplateField> $templateFields
     * @param TemplateField $templateField
     * @param Collection $fields
     * @return void
     */
    protected function checkSectionCondition(
        Collection $templateFields,
        TemplateField $templateField,
        Collection $fields
    ): void {
        $fieldIds = $fields->pluck('id')->toArray();

        // Verify if the section is selected.
        $sectionChecked = $this->filterChildFields($templateFields, $templateField->uuid)
            ->whereIn('id', $fieldIds)->isNotEmpty();

        // Iterate through all the conditions.
        foreach ($templateField->conditions() as [$name]) {
            $name = $this->getKeyFromConditionName($name);

            // Retrieve the field of the condition.
            $conditionField = $templateFields->firstWhere(TemplateField::SCHEMA_KEY, $name);
            if (!$conditionField instanceof TemplateField) {
                throw new ModelNotFoundException('Cannot find template field ' . $name);
            }

            // Verify if the condition field is checked.
            $conditionChecked = in_array($conditionField->id, $fieldIds);

            // Verify if both fields have the same value. If they differ, it indicates that one of them is unchecked.
            if ($conditionChecked !== $sectionChecked) {
                // If $sectionChecked is true, it means the condition field is not checked,
                // and an error is displayed accordingly.
                if ($sectionChecked) {
                    throw new SectionConditionNotSelectedException('', params: [
                        'section' => $templateField->title,
                        'condition' => $conditionField->title,
                    ]);
                } else {
                    throw new SectionNotSelectedException(params: [
                        'section' => $templateField->title,
                        'condition' => $conditionField->title,
                    ]);
                }
            }
        }
    }

    /**
     * Check the condition of a field and throw exception when an inconsistency is found
     *
     * @param Collection<TemplateField> $templateFields
     * @param TemplateField $templateField
     * @param Collection $fields
     * @return void
     */
    protected function checkFieldCondition(
        Collection $templateFields,
        TemplateField $templateField,
        Collection $fields
    ): void {
        $fieldIds = $fields->pluck('id')->toArray();

        // Verify if the field is checked.
        $templateFieldChecked = in_array($templateField->id, $fieldIds);

        // Iterate through all the conditions.
        foreach ($templateField->conditions() as [$name]) {
            $name = $this->getKeyFromConditionName($name);
            $conditionField = $templateFields->firstWhere(TemplateField::SCHEMA_KEY, $name);

            // Verify if the condition field is checked.
            $conditionChecked = in_array($conditionField->id, $fieldIds);

            // Verify if both fields have the same value. If they differ, it indicates that one of them is unchecked.
            if ($conditionChecked !== $templateFieldChecked) {
                // If $templateFieldChecked is true, it means that the condition field is not checked.
                if ($templateFieldChecked) {
                    throw new QuestionConditionNotSelectedException(params: [
                        'question' => $templateField->title,
                        'condition' => $conditionField->title,
                    ]);
                } else {
                    throw new QuestionNotSelectedException(params: [
                        'question' => $templateField->title,
                        'condition' => $conditionField->title,
                    ]);
                }
            }
        }
    }

    /**
     * Condition name contains a prefix (section) and return only the last part.
     * @param string $name Example: "foo.bar"
     * @return string Example: "bar"
     */
    private function getKeyFromConditionName(string $name): string
    {
        $pos = strrpos($name, '.');
        if ($pos !== false) {
            $name = substr($name, 0, $pos);
        }
        return $name;
    }
}
