<?php

namespace App\Services\OpenQuestions\Templates;

use App;
use App\Account;
use App\AccountService;
use App\Company;
use App\Events\CustomRule\CustomRuleEvent;
use App\Exceptions\BadRequestException;
use App\Exceptions\ConflictException;
use App\Exceptions\ForbiddenException;
use App\Exceptions\ModelNotSavedException;
use App\Exceptions\Template\TemplateEntryIncompleteException;
use App\Helpers\FilenameHelper;
use App\Interfaces\Services\OpenQuestions\OpenQuestionCustomRuleService;
use App\Interfaces\Services\OpenQuestions\OpenQuestionsServiceInterface;
use App\Jobs\Dms\DmsSendDocumentsJob;
use App\Jobs\TemplateEntry\CreateTemplateEntryZip;
use App\Jobs\TemplateEntry\DeleteTemplateEntryZip;
use App\Models\OpenQuestions\OpenQuestionAuditLog;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\OpenQuestions\Questions\Templates\TemplateAnswer;
use App\Models\OpenQuestions\Questions\Templates\TemplateEntry;
use App\Models\OpenQuestions\Questions\Templates\TemplateFieldType;
use App\Repositories\FileSystem\EncryptedStorageRepository;
use App\Repositories\FileSystem\StorageRepository;
use App\Repositories\OpenQuestions\OpenQuestionAuditLogRepository;
use App\Repositories\Templates\TemplateAnswerRepository;
use App\Repositories\Templates\TemplateEntryRepository;
use App\Services\CustomRule\CustomRuleService;
use App\Services\Generator\Pdf\OpenQuestionTemplatePdf;
use App\Services\OpenQuestions\OpenQuestionService;
use App\User;
use App\ValueObject\AgentData;
use App\ValueObject\Pagination\OpenQuestionsListFilters;
use Bus;
use App\Support\Carbon;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\Eloquent\Collection;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\FileBag;
use ZipArchive;

readonly class TemplateEntryService implements OpenQuestionCustomRuleService, OpenQuestionsServiceInterface
{
    public function __construct(
        private TemplateEntryRepository $templateEntryRepository,
        private TemplateFieldService $templateFieldService,
        private TemplateAnswerService $templateAnswerService,
        private StorageRepository $storageRepository,
        private OpenQuestionAuditLogRepository $openQuestionAuditLogRepository,
        private TemplateAnswerRepository $templateAnswerRepository,
    ) {
    }

    public function createOld(
        OpenQuestion $openQuestion,
        array $attributes,
        Template $template,
        array $fields,
        ?User $user = null,
        array $remarks = []
    ): TemplateEntry {
        $attributes = array_merge($attributes, [
            'open_question_id' => $openQuestion->id
        ]);

        $templateEntry = $this->templateEntryRepository->create($attributes);
        $templateFields = $this->templateFieldService->getByFields($template, $fields);
        $this->templateAnswerService->massCreateOld($templateFields, $templateEntry, $remarks, $user);

        CustomRuleEvent::fire(CustomRuleService::TEMPLATE_CREATED, $openQuestion->getAccount(), $templateEntry);

        $templateEntry->updateTemplateProgress();

        return $templateEntry;
    }

    public function create(
        OpenQuestion $openQuestion,
        array $attributes,
        Template $template,
        array $fields,
        ?User $user = null,
    ): TemplateEntry {
        $attributes = array_merge($attributes, [
            'open_question_id' => $openQuestion->id
        ]);
        $fields = collect($fields)->sortBy('id')->toArray();

        $templateEntry = $this->templateEntryRepository->create($attributes);
        $templateFields = $this->templateFieldService->getByFields($template, array_column($fields, 'id'));

        $this->templateAnswerService->massCreate($templateFields, $templateEntry, $fields, $user);

        CustomRuleEvent::fire(CustomRuleService::TEMPLATE_CREATED, $openQuestion->getAccount(), $templateEntry);

        $templateEntry->updateTemplateProgress();

        return $templateEntry;
    }

    public function getById(int $id, User $user): TemplateEntry
    {
        $templateEntry = $this->templateEntryRepository->getById($id);

        if ($user->account_id !== $templateEntry->openQuestion->company->account_id) {
            throw new ForbiddenException('User account does not match provided template account');
        }

        return $templateEntry;
    }

    public function getByOpenQuestionId(int $openQuestionId, ?User $user): TemplateEntry
    {
        $templateEntry = $this->templateEntryRepository->getByOpenQuestionId($openQuestionId);

        if ($user && $user->account_id !== $templateEntry->template->account_id) {
            throw new ForbiddenException('User account does not match provided template account');
        }

        return $templateEntry;
    }

    public function questionsToAnswer(
        Company $company,
        User $user,
        array $orderBy = ['open_questions.created_at' => 'desc'],
        ?string $alternateCategory = null
    ): Collection {
        if ($alternateCategory === null) {
            throw new App\Exceptions\PreconditionFailedException('Category should be supplied.');
        }
        return $this->templateEntryRepository->questionsToAnswer(
            $company,
            $user,
            $alternateCategory,
            $orderBy
        );
    }

    public function saveChanges(
        User $user,
        Account $account,
        TemplateEntry $templateEntry,
        array $questionData,
        FileBag $files,
        ?AgentData $agentData = null,
    ): TemplateEntry {
        foreach ($templateEntry->templateAnswers as $templateAnswer) {
            $found = false;
            foreach ($questionData as $key => $data) {
                if ($data['id'] == $templateAnswer->id) {
                    $fileData = $files->get('questions')[$key]['value'] ?? null;
                    $this->templateAnswerService->save($user, $account, $templateAnswer, $data, $fileData);
                    $found = true;
                }
            }
            if (!$found) {
                $this->templateAnswerService->save($user, $account, $templateAnswer);
            }
        }

        $this->saveAuditLog($user, $templateEntry, $agentData);

        $templateEntry->updateTemplateProgress();

        return $templateEntry->refresh();
    }

    /**
     * Check if all required questions have received a response.
     * @throws TemplateEntryIncompleteException when a required answer is missing
     * @param TemplateEntry $templateEntry
     * @return void
     */
    public function checkCompletion(TemplateEntry $templateEntry): void
    {
        foreach ($templateEntry->templateAnswers as $templateAnswer) {
            if (
                !$templateAnswer->required
                || isset($templateAnswer->answer)
                || (
                    in_array($templateAnswer->type, TemplateFieldType::FIELDS_FOR_FILE_UPLOAD)
                    && !empty($templateAnswer->attachments)
                )
            ) {
                continue;
            }

            throw new TemplateEntryIncompleteException(
                'Template entry #' . $templateEntry->id .
                ' not completed yet. Missing template answer #' .
                $templateAnswer->id
            );
        }
    }

    private function prepareStoringTempFiles(TemplateEntry $templateEntry): array
    {
        $uniquePath = storage_path('tmp/template_entry/' . random_string(16)) . '/';
        mkdir($uniquePath, 0755, true);
        $zipName = FilenameHelper::sanitize(
            $templateEntry->openQuestion->getDmsDisplayName()
            . ' - '
            . Carbon::now()->format('d-m-Y H-i-s')
            . '.zip'
        );

        $zipPath = $uniquePath . $zipName;

        return [
            $uniquePath,
            $zipPath
        ];
    }

    public function sendToDms(OpenQuestion $openQuestion, ?User $user): void
    {
        $templateEntry = $this->getByOpenQuestionId($openQuestion->id, $user);

        list($uniquePath, $zipPath) = $this->prepareStoringTempFiles($templateEntry);

        Bus::chain([
            new CreateTemplateEntryZip($uniquePath, $zipPath, $templateEntry->id),
            new DmsSendDocumentsJob(
                $openQuestion,
                [$zipPath],
                $templateEntry->openQuestion->company->account_id,
                $user->id ?? null
            ),
            new DeleteTemplateEntryZip($uniquePath, $templateEntry->id),
        ])->dispatch();
    }

    /**
     * Create Zip and return the content, and remove the zip afterwards.
     * @param OpenQuestion $openQuestion
     * @return string
     * @throws FileNotFoundException
     */
    public function getZipFile(OpenQuestion $openQuestion): string
    {
        $templateEntry = $this->templateEntryRepository->getByOpenQuestionId($openQuestion->id);

        list($uniquePath, $zipPath) = $this->prepareStoringTempFiles($templateEntry);
        // Create the zip. A zip will always be stored when creating, to simplify I used the same code as for sending.
        $this->createArchive($templateEntry, $uniquePath, $zipPath);
        // We want to get the content of the file because this will simplify the code.
        $content = resolve(EncryptedStorageRepository::class)->getFile($zipPath);
        // Afterwards delete the archive, because we are done using it.
        $this->deleteArchive($uniquePath);

        return $content;
    }

    public function createArchive(
        TemplateEntry $templateEntry,
        string $uniquePath,
        string $zipPath
    ): void {
        $zip = new ZipArchive();

        if ($zip->open($zipPath, ZipArchive::CREATE) !== true) {
            throw new BadRequestException('Could not create attachment zip, maybe permissions');
        }

        $pdf = new OpenQuestionTemplatePdf($templateEntry);

        $sanitizedPdfName = FilenameHelper::sanitize($pdf->getFilename());
        $pdfPath = $uniquePath . $sanitizedPdfName;
        file_put_contents($pdfPath, $pdf->getContent());
        $zip->addFile($pdfPath, $sanitizedPdfName);

        $encryptedStorageRepository = App::make(EncryptedStorageRepository::class);
        $templateAnswers = $templateEntry->templateAnswers()->orderBy(TemplateAnswer::ORDER)->get();
        /** @var TemplateAnswer $templateAnswer */
        foreach ($templateAnswers as $templateAnswer) {
            if (!empty($templateAnswer->attachments)) {
                foreach ($templateAnswer->attachments as $attachment) {
                    $title = FilenameHelper::sanitizeFolder($templateAnswer->title, 60);
                    $uniqueFilename = $templateAnswer->getQuestionNumber() . ' - ' . $title . ' - ' . $attachment->filename; //phpcs:ignore
                    $uniqueFilename = FilenameHelper::sanitize($uniqueFilename, 120);
                    $path = $uniquePath . $uniqueFilename;
                    $attachmentContent = $encryptedStorageRepository->getFile($attachment->path);
                    file_put_contents($path, $attachmentContent);
                    $zip->addFile($path, $uniqueFilename);
                }
            }
        }

        $zip->close();

        $zipFile = new File($zipPath);

        $encryptedStorageRepository->storeFile($zipFile, $uniquePath);
    }

    public function deleteArchive(string $archiveFolder): void
    {
        $this->storageRepository->deleteAllFilesInFolder($archiveFolder);
    }

    public function getForCompany(
        Company $company,
        OpenQuestionsListFilters $filters,
        ?string $category = null
    ): Collection {
        if ($category === null) {
            throw new App\Exceptions\PreconditionFailedException('Category should be supplied.');
        }
        return $this->templateEntryRepository->getForCompany($category, $company, $filters);
    }

    public function fireCustomRulePending(OpenQuestion $openQuestion): void
    {
        CustomRuleEvent::fire(
            CustomRuleService::TEMPLATE_TO_BE_REVIEWED,
            $openQuestion->getAccount(),
            $openQuestion->templateEntry
        );
    }

    public function fireCustomRuleCompleted(OpenQuestion $openQuestion): void
    {
        CustomRuleEvent::fire(
            CustomRuleService::TEMPLATE_COMPLETED,
            $openQuestion->getAccount(),
            $openQuestion->templateEntry
        );
    }

    public function questionTypes(AccountService $accountService, ?array $data): Collection
    {
        return new Collection();
    }

    /**
     * Register in question audit log that answers were submitted for specific entry
     * @param User $user
     * @param TemplateEntry $templateEntry
     * @param AgentData $agentData
     * @return void
     */
    public function saveSubmitAuditLog(User $user, TemplateEntry $templateEntry, AgentData $agentData): void
    {
        $this->openQuestionAuditLogRepository->create(
            $user,
            $templateEntry->openQuestion,
            OpenQuestionAuditLog::ACTION_TEMPLATE_SUBMITTED,
            ['user_name' => $user->getNameAttribute()],
            $agentData,
        );
    }

    /**
     * Register in question audit log that answers were saved for a specific entry
     * @param User $user
     * @param TemplateEntry $templateEntry
     * @param AgentData|null $agentData
     * @return void
     */
    public function saveAuditLog(User $user, TemplateEntry $templateEntry, ?AgentData $agentData): void
    {
        $this->openQuestionAuditLogRepository->create(
            $user,
            $templateEntry->openQuestion,
            OpenQuestionAuditLog::ACTION_TEMPLATE_ANSWER_SAVED,
            [
                'user_name' => $user->getNameAttribute()
            ],
            $agentData,
        );
    }

    public function update(TemplateEntry $templateEntry, array $schema): void
    {
        $templateEntry->schema = $schema;
        $templateEntry->save();
    }

    /**
     * @param TemplateEntry $entry
     * @param TemplateAnswer[] $answers
     * @return void
     */
    public function saveAnswers(TemplateEntry $entry, array $answers): void
    {
        $fieldIds = $entry->templateAnswers()->pluck('id')->toArray();
        $savedIds = [];

        foreach ($answers as $answer) {
            if (isset($answer->id)) {
                if (!in_array($answer->id, $fieldIds)) {
                    throw new \UnexpectedValueException('Answer ID ' . $answer->id . ' does not belong to template entry #' . $entry->id); // phpcs:ignore
                }
            } else {
                // save as new
                $answer->templateEntry()->associate($entry);
            }
            if ($answer->save(['transaction' => false])) {
                $savedIds[] = $answer->id;
            } else {
                throw new ModelNotSavedException('Save returned false while saving answer to template entry #' . $entry->id); // phpcs:ignore
            }
        }
        $this->templateAnswerRepository->deleteExcluded($entry->id, $savedIds);
    }
}
