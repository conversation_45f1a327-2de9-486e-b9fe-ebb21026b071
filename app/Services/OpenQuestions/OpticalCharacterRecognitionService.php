<?php

namespace App\Services\OpenQuestions;

use App\AccountService;
use App\Company;
use App\Events\CustomRule\CustomRuleEvent;
use App\Exceptions\AccountService\NotEnabledException;
use App\Interfaces\Services\OpenQuestions\OpenQuestionCustomRuleService;
use App\Interfaces\Services\OpenQuestions\OpenQuestionsServiceInterface;
use App\Models\OpenQuestions\OpenQuestionType;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\OpticalCharacterRecognition;
use App\Repositories\OpenQuestions\OpticalCharacterRecognitionRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Service;
use App\User;
use App\ValueObject\Pagination\OpenQuestionsListFilters;
use Illuminate\Database\Eloquent\Collection;

readonly class OpticalCharacterRecognitionService implements OpenQuestionsServiceInterface, OpenQuestionCustomRuleService //phpcs:ignore
{
    public function __construct(
        private OpticalCharacterRecognitionRepository $opticalCharacterRecognitionRepository,
        private AccountServiceRepository $accountServiceRepository,
        private OpenQuestionTypeService $openQuestionTypeService,
    ) {
    }

    public function create(array $attributes, OpenQuestion $openQuestion)
    {
        $attributes['open_question_id'] = $openQuestion->id;
        $question = $this->opticalCharacterRecognitionRepository->create($attributes);
        CustomRuleEvent::fire($question->getCustomRuleCreatedEvent(), $openQuestion->getAccount(), $openQuestion);
        return $question;
    }

    public function questionsToAnswer(
        Company $company,
        User $user,
        array $orderBy = ['open_questions.created_at' => 'desc'],
        ?string $alternateCategory = null
    ): Collection {
        return $this->opticalCharacterRecognitionRepository->questionsToAnswer($company, $user, $orderBy);
    }

    /**
     * Get types for a question
     *
     * @param AccountService $accountService
     * @param array|null $data
     * @return Collection
     */
    public function questionTypes(AccountService $accountService, ?array $data): Collection
    {
        return $this->openQuestionTypeService->getBySubject(
            $accountService->id,
            OpenQuestionType::SUBJECT_COMPANY
        );
    }

    /**
     * @param string $externalId
     * @param string $serviceName
     * @param User $user
     * @return Collection
     */
    public function getQuestionsForExternalCompany(
        string $externalId,
        string $serviceName,
        User $user
    ): Collection {
        $accountService = $this->accountServiceRepository->getByReferenceName(
            $serviceName,
            $user->account,
            true
        );
        if ($accountService->isEmpty()) {
            throw new NotEnabledException(params: ['reference_name' => $serviceName]);
        }

        return $this->opticalCharacterRecognitionRepository->getQuestionsForExternalCompanyAndAccountService(
            $externalId,
            $accountService->first()
        );
    }

    public function fireCustomRulePending(OpenQuestion $openQuestion): void
    {
        $ocr = $this->opticalCharacterRecognitionRepository->getByQuestionId($openQuestion);
        if (!empty($ocr)) {
            CustomRuleEvent::fire($ocr->getCustomRuleToBeReviewedEvent(), $openQuestion->getAccount(), $openQuestion);
        }
    }

    public function fireCustomRuleCompleted(OpenQuestion $openQuestion): void
    {
        $ocr = $this->opticalCharacterRecognitionRepository->getByQuestionId($openQuestion);
        if (!empty($ocr)) {
            CustomRuleEvent::fire($ocr->getCustomRuleCompletedEvent(), $openQuestion->getAccount(), $openQuestion);
        }
    }

    public function getForCompany(
        Company $company,
        OpenQuestionsListFilters $filters,
        ?string $category = null
    ): Collection {
        return $this->opticalCharacterRecognitionRepository->getForCompany($company, $filters);
    }

    public function findQuestion(array $attributes, int $companyId, int $accountServiceId): ?OpticalCharacterRecognition //phpcs:ignore
    {
        return $this->opticalCharacterRecognitionRepository->findQuestion(
            $attributes,
            $companyId,
            $accountServiceId
        );
    }
}
