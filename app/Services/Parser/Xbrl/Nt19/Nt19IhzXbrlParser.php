<?php

namespace App\Services\Parser\Xbrl\Nt19;

use App\Services\Parser\Xbrl\AbstractIhzXbrlParser;

class Nt19IhzXbrlParser extends AbstractIhzXbrlParser
{
    public const TAXONOMY_NT19 = 'http://www.nltaxonomie.nl/nt19/bd/20241211/entrypoints/bd-rpt-ihz-aangifte-2024.xsd';

    protected const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_NT19,
    ];

    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt19/bd/20241211/dictionary/bd-data';
}
