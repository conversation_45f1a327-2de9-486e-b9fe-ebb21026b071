<?php

namespace App\Services\Parser\Xbrl\Nt17;

use App\Services\Parser\Xbrl\Sba\AbstractSbaZvwXbrlParser;

class Nt17SbaZvwXbrlParser extends AbstractSbaZvwXbrlParser
{
    public const TAXONOMY_URL = 'http://www.nltaxonomie.nl/nt17/bd/20230215.a/entrypoints/bd-rpt-zvw-sba-2022.xsd';
    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-data';
    public const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_URL
    ];
    public const FISCAL_YEAR = 2023;
}
