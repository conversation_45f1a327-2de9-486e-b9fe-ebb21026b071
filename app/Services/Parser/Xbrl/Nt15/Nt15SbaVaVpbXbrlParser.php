<?php

namespace App\Services\Parser\Xbrl\Nt15;

use App\Services\Parser\Xbrl\Sba\AbstractSbaVpbXbrlParser;

class Nt15SbaVaVpbXbrlParser extends AbstractSbaVpbXbrlParser
{
    public const TAXONOMY_URL = 'http://www.nltaxonomie.nl/nt15/bd/20201209/entrypoints/bd-rpt-vpb-sba-2021.xsd';
    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt15/bd/20201209/dictionary/bd-data';
    public const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_URL
    ];
    public const FISCAL_YEAR = 2021;
}
