<?php

namespace App\Services\Parser\Xbrl\Nt12;

use App\Services\Parser\Xbrl\AbstractVatXbrlParser;

class Nt12VatXbrlParser extends AbstractVatXbrlParser
{
    public const TAXONOMY_NT12 = 'http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ob-aangifte-2018.xsd';
    public const TAXONOMY_NT12_SUPPLETION =
        'http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ob-suppletie-2018.xsd';

    // We added the keys in order to find if the declaration is a regular VAT or a supplementation
    protected const SUPPORTED_TAXONOMIES = [
        parent::MESSAGE_TYPE_VAT => self::TAXONOMY_NT12,
        parent::MESSAGE_TYPE_VAT_SUPPLETION => self::TAXONOMY_NT12_SUPPLETION,
    ];

    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-data';
}
