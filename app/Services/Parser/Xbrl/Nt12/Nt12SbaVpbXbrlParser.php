<?php

namespace App\Services\Parser\Xbrl\Nt12;

use App\Services\Parser\Xbrl\Sba\AbstractSbaVpbXbrlParser;

class Nt12SbaVpbXbrlParser extends AbstractSbaVpbXbrlParser
{
    public const TAXONOMY_URL = 'http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-vpb-sba-2017.xsd';
    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-data';
    public const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_URL
    ];
    public const FISCAL_YEAR = 2017;
}
