<?php

namespace App\Services\Parser\Pdf;

use App\Company;
use App\Exceptions\Services\InvalidFilenameException;
use App\Helpers\DatetimeHelper;
use App\Models\TaskFile;
use App\ServiceTask;
use App\ValueObject\Declarations\DeclarationData;
use App\ValueObject\ServiceTask\AfasUploadedFileName;
use App\Support\Carbon;

class AfasPdfParser extends AbstractPdfParser implements CanCheckFilename
{
    protected ?string $declarationType = null;
    protected AfasUploadedFileName $fileinfo;

    public function supports(): bool
    {
        //we just assume this is a valid PDF
        //@todo SL-2475 add PDF validation
        return true;
    }

    public function supportsFilename(string $filename): bool
    {
        try {
            $this->fileinfo = new AfasUploadedFileName($filename);
            return true;
        } catch (InvalidFilenameException $e) {
            return false;
        }
    }

    /**
     * @param  string  $type  Declaration type from AFAS filename.
     * @return string Matches a valid task type.
     * @throws \Exception
     */
    public static function getAfasTaskType(string $type): string
    {
        if ($type === AfasUploadedFileName::DECLARATION_TYPE_IB) {
            return ServiceTask::TYPE_IHZ_APPROVAL;
        } elseif ($type === AfasUploadedFileName::DECLARATION_TYPE_VPB) {
            return ServiceTask::TYPE_VPB_APPROVAL;
        }

        throw new \Exception('Unknown AFAS declaration type ' . $type);
    }

    public function getParsedResult(string $filename, string $content): array
    {
        $this->declarationType = self::getAfasTaskType($this->fileinfo->getDeclarationType());
        $result = parent::getParsedResult($filename, $content);
        $result[DeclarationData::TYPE_FIELD] = $this->declarationType;
        $result[DeclarationData::DATE_START_FIELD] = $this->fileinfo->getPeriodStart()->toDateString();
        $result[DeclarationData::DATE_END_FIELD] = $this->fileinfo->getPeriodEnd()->toDateString();
        $result[DeclarationData::YEAR_FIELD] = $this->fileinfo->getYear();
        $result[DeclarationData::SUMMARY_FIELD] = [];

        $identifierType = $this->fileinfo->getIdentifierType();
        if ($identifierType === Company::BSN_NUMBER) {
            $result[DeclarationData::BSNNUMBER_FIELD] = $this->fileinfo->getIdentifier();
        } elseif ($identifierType === Company::FISCAL_NUMBER) {
            $result[DeclarationData::FISCALNUMBER_FIELD] = $this->fileinfo->getIdentifier();
        }

        return $result;
    }

    public function getDeclarationType(): string
    {
        return $this->declarationType;
    }

    public function getTaskFileType(): ?string
    {
        if ($this->declarationType === ServiceTask::TYPE_IHZ_APPROVAL) {
            return TaskFile::TYPE_INCOME_TAX_DECLARATION;
        } elseif ($this->declarationType === ServiceTask::TYPE_VPB_APPROVAL) {
            return TaskFile::TYPE_CORPORATE_TAX_DECLARATION;
        }
        return null;
    }

    public function getFileTitle(array $data, string $language): string
    {
        $date_end = Carbon::parse($data['date_end'])->locale($language);
        $key = 'service_task.' . $data['type'] . '.title-with-year';
        $params['year'] = $date_end->isoFormat('YYYY');
        $title = trans($key, $params, $language);
        $title .= ' ' . DatetimeHelper::getDeclarationPeriod($data['date_start'], $data['date_end'], $language);
        return $title;
    }

    /**
     * Get localized subtitle based on type and presence of identifier in data.
     * Subtitle contains identifier to help user with matching the file to a company.
     * @param array $data
     * @param string $language
     * @return string|null
     */
    public function getFileSubtitle(array $data, string $language): ?string
    {
        if (
            $data[DeclarationData::TYPE_FIELD] === ServiceTask::TYPE_VPB_APPROVAL
            && isset($data[DeclarationData::FISCALNUMBER_FIELD])
        ) {
            return trans(
                'service_task.vpb_approval.file-subtitle',
                ['fiscalnumber' => $data[DeclarationData::FISCALNUMBER_FIELD]],
                $language
            );
        }

        if (
            $data[DeclarationData::TYPE_FIELD] === ServiceTask::TYPE_IHZ_APPROVAL
            && isset($data[DeclarationData::BSNNUMBER_FIELD])
        ) {
            return trans(
                'service_task.ihz_approval.file-subtitle',
                ['bsnnumber' => $data[DeclarationData::BSNNUMBER_FIELD]],
                $language
            );
        }

        return null;
    }
}
