<?php

namespace App\Services\Dms;

use App;
use App\AccountService;
use App\Company;
use App\Exceptions\ForbiddenException;
use App\Models\Dms\OpenQuestionCompanyDmsPreference;
use App\Models\Dms\OpenQuestionDmsPreference;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Repositories\Dms\OpenQuestionPreferencesRepository;
use App\User;
use App\ValueObject\Dms\DmsPreferences;

class OpenQuestionPreferencesService
{
    private OpenQuestionPreferencesRepository $openQuestionPreferencesRepository;

    public function __construct(OpenQuestionPreferencesRepository $openQuestionPreferencesRepository)
    {
        $this->openQuestionPreferencesRepository = $openQuestionPreferencesRepository;
    }

    public function getForCompany(
        Company $company,
        AccountService $accountService,
        string $relationType,
        User $user
    ): ?OpenQuestionCompanyDmsPreference {
        if (!$company->internal_users->contains($user) || $accountService->account_id !== $user->account_id) {
            throw new ForbiddenException('User #' . $user->id . ' is not allowed to access the dms information of company #' . $company->id); //phpcs:ignore
        }
        return $this->openQuestionPreferencesRepository->getForCompany($company, $accountService, $relationType);
    }

    /**
     * @param Company $company
     * @param AccountService $accountService
     * @param DmsPreferences $preferences
     * @param string $relationType
     * @param User $user
     * @return OpenQuestionCompanyDmsPreference
     */
    public function updateForCompany(
        Company $company,
        AccountService $accountService,
        DmsPreferences $preferences,
        string $relationType,
        User $user
    ): OpenQuestionCompanyDmsPreference {
        if (!$company->internal_users->contains($user) || $accountService->account_id !== $user->account_id) {
            throw new ForbiddenException('User #' . $user->id . ' is not allowed to update the dms information of company #' . $company->id); //phpcs:ignore
        }
        return $this->openQuestionPreferencesRepository->updateOrCreateForCompany(
            $company,
            $accountService,
            $preferences->toArray(),
            $relationType
        );
    }

    public function createForOpenQuestion(
        OpenQuestion $openQuestion,
        AccountService $accountService
    ): ?OpenQuestionDmsPreference {
        /** @var ?OpenQuestionCompanyDmsPreference $companyPreferences */
        $companyPreferences = $openQuestion->dmsParentPreferences()
            ->where(OpenQuestionCompanyDmsPreference::ACCOUNT_SERVICE_ID, $accountService->id)
            ->first();
        if (!empty($companyPreferences)) {
            return $this->openQuestionPreferencesRepository->updateOrCreateForOpenQuestion(
                $openQuestion,
                $accountService,
                $companyPreferences->getDmsPreferences()->toArray()
            );
        }
        return null;
    }
}
