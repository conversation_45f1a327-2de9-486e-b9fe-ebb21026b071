<?php

namespace App\Services;

use App\Account;
use App\Events\CustomRule\CustomRuleEvent;
use App\Events\Service\ServiceTask\XbrlDeliveredEvent;
use App\Events\Service\ServiceTask\XbrlErrorEvent;
use App\Exceptions\Api\BadRequestException;
use App\Exceptions\Api\ForbiddenException;
use App\Exceptions\Api\NotFoundException;
use App\Factories\Models\SbrRequestFactory;
use App\Factories\Models\TaskRequestFactory;
use App\Helpers\QuarantineHelper;
use App\Helpers\TaskAuditLogHelper;
use App\Logging\Channels\ServiceLog;
use App\Mail\SbrRequest\SbrRequestCompletedEmail;
use App\Mail\SbrRequest\SbrRequestErrorEmail;
use App\Models\Sbr\SbrRecipient;
use App\Models\ServiceTask\SbrTask;
use App\Jobs\Sbr\SbrStatusRequestJob;
use App\Jobs\Sbr\SbrSupplyRequestJob;
use App\Models\Sbr\SbrRequest;
use App\Models\TaskFile;
use App\Models\TaskFile\Xbrl;
use App\Models\TaskFile\Xml;
use App\Repositories\SbrRequestRepository;
use App\Repositories\Services\ServiceTaskRepository;
use App\Services\Gateway\Sbr\SbrStatus;
use App\Services\Gateway\Sbr\SbrSupplyResponseParser;
use App\Services\Gateway\Sbr\SbrTaskSender;
use App\Services\ServiceTask\WebhookDispatchTrigger;
use App\ValueObject\Declarations\DeclarationData;
use App\Support\Carbon;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use SoapFault;
use Throwable;

readonly class SbrRequestService
{
    public function __construct(private SbrRequestRepository $repository)
    {
    }

    public function dispatchSupplyRequest(SbrTask $task, bool $force = false, int $delay = 1): SbrRequest
    {
        // Delete the last SBR request.
        $task->sbrRequest()->delete();
        $task->state->setPendingRequestingParty(true);
        $request = TaskRequestFactory::create($task);
        $request->save();
        $queueDate = Carbon::now()->addSeconds($delay);
        SbrSupplyRequestJob::dispatch($task->id, $request->id, $force)->delay($queueDate);
        return $request;
    }

    /**
     * @param SbrRequest $request
     * @param string $externalId
     * @throws \Exception
     */
    public function startPolling(SbrRequest $request, string $externalId): void
    {
        $request->external_id = $externalId;
        $request->status = SbrRequest::STATUS_PROCESSING;
        $request->save();
        $this->updateFromStatus($request, new SbrStatus(SbrStatus::STATUS_CODE_RECEIVED));
        $this->addToQueue($request, 5);
    }

    /**
     * Get the amount of seconds that we have to wait between polling the status.
     * The time will increase at every attempt until eventually it will be every 4 hours.
     * @param int $tries
     * @return int Amount of seconds to wait between ever subsequent status check request.
     */
    public static function getIntervalSeconds(int $tries): int
    {
        if ($tries === 0) {
            return 5;
        } elseif ($tries === 1) {
            return 20;
        } elseif ($tries === 2) {
            return 60; // = 1 minute
        } elseif ($tries === 3) {
            return 300; // = 5 minutes
        } elseif ($tries === 4) {
            return 900; // = 15 minutes
        } elseif ($tries < 10) {
            return 3600; // = 1 hour
        }

        return 3600 * 4; // = 4 hours;
    }

    public function updateFromStatus(SbrRequest $request, SbrStatus $status): SbrRequest
    {
        $request->last_checked_date = Carbon::now();
        $request->response_code = $status->code;
        $request->response_description = $status->description;
        $request->error_description = $status->error_description;
        $request->response_timestamp = $status->getTimestamp();

        $account = $request->account;

        if ($status->isProcessing()) {
            $request->status = SbrRequest::STATUS_PROCESSING;
        } elseif ($status->isError()) {
            $request->status = SbrRequest::STATUS_ERROR;
            if (isset($request->service_task_id)) {
                $request->task->state->setErrorRequestingParty(true);
                TaskAuditLogHelper::errorXbrl($request->task);
                XbrlErrorEvent::fire($request, $status);
            }
            CustomRuleEvent::fire('sbr_request.error', $account, new SbrRequestErrorEmail($request));
        } elseif ($status->isSuccess()) {
            $request->status = SbrRequest::STATUS_DELIVERED;

            if (isset($request->service_task_id)) {
                $request->task->state->setCompletedOrSendDms(true);
                TaskAuditLogHelper::xbrlDelivered($request->task);
                XbrlDeliveredEvent::fire($request->task);
            }
            CustomRuleEvent::fire('sbr_request.completed', $account, new SbrRequestCompletedEmail($request));
        }

        if ($status->isDone()) {
            $request->check_after_date = null;
        }

        $request->save();
        WebhookDispatchTrigger::sbrStatusChanged($request);

        return $request;
    }

    /**
     * Add request to queue to check the status.
     * @param SbrRequest $request
     * @param int $delay Number of seconds to wait until executing job on queue.
     * @return SbrRequest
     * @throws \Exception
     */
    public function addToQueue(SbrRequest $request, int $delay = 0): SbrRequest
    {
        $queueDate = Carbon::now()->addSeconds($delay);
        SbrStatusRequestJob::dispatch($request->id)->delay($queueDate);
        return $this->setToQueued($request, $queueDate, $delay);
    }

    public function setToQueued(SbrRequest $request, Carbon $queueDate, int $delay = 0): SbrRequest
    {
        $request->tries++;
        $request->queued_date = $queueDate;
        $request->check_after_date = $request->getNextCheckDate()->addSeconds($delay);
        $request->status = SbrRequest::STATUS_QUEUED;
        $request->save();
        return $request;
    }

    /**
     * If a supply response returns an error, handle it here. This happens before even a status request can be sent.
     * Request and response will be quarantined because this type of error is not normal and should be investigated.
     * @param SbrRequest $request
     * @param string $soapResponse
     * @param string|null $soapRequest
     * @return SbrRequest
     * @throws Throwable
     */
    public function handleSupplyErrorResponse(
        SbrRequest $request,
        string $soapResponse,
        ?string $soapRequest = null
    ): SbrRequest {
        if (isset($soapRequest)) {
            QuarantineHelper::quarantineFile('sbr-supply-request-' . time() . '.xml', $soapRequest);
        }
        QuarantineHelper::quarantineFile('sbr-supply-response-' . time() . '.xml', $soapResponse);
        $parser = new SbrSupplyResponseParser($soapResponse);
        $parsed = $parser->parseResponse();
        if ($parsed['type'] === 'error') {
            $request->error_description = $parsed['detail']['descr'];
            $request->status = SbrRequest::STATUS_ERROR;
            $request->save();
        } else {
            throw new \UnexpectedValueException('Unable to parse SBR supply error response');
        }
        return $request;
    }

    /**
     * This function is only used by Caseware api
     *
     * @param Account $account
     * @param SbrRecipient $sbrRecipient
     * @param Xbrl $sbrYearwork
     * @param Xbrl|null $auditReport
     * @param Xml|null $signature
     * @return array
     * @throws BadRequestException
     * @throws Throwable
     */
    public function supplyWithFiles(
        Account $account,
        SbrRecipient $sbrRecipient,
        Xbrl $sbrYearwork,
        ?Xbrl $auditReport,
        ?Xml $signature
    ): array {
        $sbrRequest = SbrRequestFactory::createForAccount($account, $sbrRecipient);
        $sbrRequest->identifier = $sbrYearwork->identifier;
        $sbrRequest->title = $sbrYearwork->title;
        $sbrRequest->entity_name = $sbrYearwork->parsed_data[DeclarationData::ENTITY_NAME] ?? null;
        try {
            DB::beginTransaction();
            $sender = new SbrTaskSender($sbrRequest, $sbrYearwork, $auditReport, $signature);
            $sender->doRequest();
            $this->startPolling($sbrRequest, $sender->getMessageID());
            DB::commit();

            return ['request' => $sbrRequest, 'sender' => $sender];
        } catch (Throwable $e) {
            DB::rollBack();
            ServiceLog::error('Failed to submit SbrRequest #' . $sbrRequest->id . ' with exception message ' . $e->getMessage()); // phpcs:ignore
            if (!isset($sender)) {
                throw new BadRequestException('Could not find sender');
            }
            $soapClient = $sender->soapClient;
            $response = $soapClient->__getLastResponse();
            if (isset($response)) {
                $sbrRequest = $this->handleSupplyErrorResponse($sbrRequest, $response, $soapClient->__getLastRequest());
                throw new BadRequestException('Unable to submit SbrRequest #' . $sbrRequest->id . ' with exception message: ' . $sbrRequest->error_description); // phpcs:ignore
            }

            throw new BadRequestException('Unable to submit SbrRequest #' . $sbrRequest->id . ' with exception message: ' . $e->getMessage());// phpcs:ignore
        }
    }

    /**
     * @param Account $account
     * @param int $id
     * @return SbrRequest
     * @throws ForbiddenException
     */
    public function getById(Account $account, int $id): SbrRequest
    {
        $sbrRequest = $this->repository->getById($id);
        if ($sbrRequest->account_id !== $account->id) {
            throw new ForbiddenException('Requesting SBR request does not belong to host account.');
        }
        return $sbrRequest;
    }

    /**
     * @param Account $account
     * @param int $taskId
     * @return SbrRequest
     * @throws ForbiddenException
     * @throws NotFoundException
     */
    public function getByTaskId(Account $account, int $taskId): SbrRequest
    {
        $task = resolve(ServiceTaskRepository::class)->getById($taskId);
        if ($task === null) {
            throw new NotFoundException('Task with id ' . $taskId . ' not found.');
        }
        $sbrRequest = $this->repository->getForTask($task);
        if ($sbrRequest->account_id !== $account->id) {
            throw new ForbiddenException('Requesting SBR request does not belong to host account.');
        }
        return $sbrRequest;
    }

    public function indexOverviewQuery(Account $account, ?string $search): Builder
    {
        return $this->repository->indexOverviewQuery($account, $search);
    }
}
