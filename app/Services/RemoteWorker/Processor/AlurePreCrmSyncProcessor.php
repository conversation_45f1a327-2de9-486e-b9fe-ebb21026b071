<?php

namespace App\Services\RemoteWorker\Processor;

use App\AccountService;
use App\Services\RemoteWorker\RemoteWorkerInstructionService;

class AlurePreCrmSyncProcessor extends AbstractAlureProcessor
{
    public function __construct(AccountService $accountService)
    {
        $this->accountService = $accountService;
    }

    public function getInstructions(): array
    {
        $provider = $this->accountService->getProvider();
        $replacements = $provider->getReplacements();
        return RemoteWorkerInstructionService::loadTemplate('alure_crm_sync.json', $replacements);
    }

    public function getNextProcessorName(): ?string
    {
        return AlureCrmSyncProcessor::class;
    }
}
