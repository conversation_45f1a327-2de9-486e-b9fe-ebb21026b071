<?php

namespace App\Services\ServiceTask;

use App\AccountService;
use App\Company;
use App\Exceptions\Company\IdentifierConflictException;
use App\Exceptions\Services\ServiceTaskRefreshException;
use App\Repositories\Services\AccountServiceRepository;
use App\Services\CompanyIdentifierService;
use App\Services\Postbode\LetterService;
use App\ServiceTask;
use App\Logging\Channels\ServiceLog;

readonly class TaskRefresher
{
    public function __construct(
        private TaskGenerator $taskGenerator,
        private AccountServiceRepository $accountServiceRepository,
        private CompanyIdentifierService $companyIdentifierService
    ) {
    }

    /**
     * @param  ServiceTask  $task
     * @param  bool|null  $reopenRefreshedTask
     * @return ServiceTask|null NULL if the task cannot be refreshed.
     * @throws \Exception|ServiceTaskRefreshException
     */
    public function refresh(ServiceTask $task, ?bool $reopenRefreshedTask = false): ?ServiceTask
    {
        /** @var AccountService $service */
        $service = $this->accountServiceRepository->getById($task->account_service_id);
        if (!$service->isReady()) {
            throw new \RuntimeException('Service not ready to refresh tasks: ' . $service->notReadyMessage);
        }

        $oldState = $task->status;
        if ($task->shouldRefreshOnReopen()) {
            $refreshedTask = $this->taskGenerator->regenerateTask($task);
        } else {
            $refreshedTask = $task; // Manual task counts as being refreshed so that it will be unlocked.
        }

        if ($refreshedTask === null) {
            if ($oldState === ServiceTask::STATUS_EXPIRED) {
                $message = 'Provider returned no data for ' . $task->refresh_id . ' Task status remains expired.';
                ServiceLog::warning($message);
                throw new ServiceTaskRefreshException($message);
            }

            $task->setExpired(true);
        } else {
            if ($reopenRefreshedTask) {
                if ($refreshedTask->isLocked() && $refreshedTask->isExpired()) {
                    $refreshedTask->unLock();
                }
                resolve(LetterService::class)->cancelForTask($task);
                $refreshedTask->reopen();
            }

            if ($refreshedTask->getIdentifier() && $refreshedTask->getIdentifierKey()) {
                $this->addTaskIdentifierToCompany($refreshedTask);
            }

            $refreshedTask->save();
        }

        return $refreshedTask;
    }

    /**
     * Add task identifier to company if it doesn't exist
     * @param ServiceTask $refreshedTask
     * @return void
     */
    public function addTaskIdentifierToCompany(ServiceTask $refreshedTask): void
    {
        $identifier = $this->companyIdentifierService->findIdentifierForAccount(
            $refreshedTask->account,
            $refreshedTask->getIdentifier(),
            $refreshedTask->getIdentifierKey(),
        );

        if ($identifier && $identifier->company_id !== $refreshedTask->company_id) {
            throw new IdentifierConflictException(
                'Unable to add identifier to company',
                params: [Company::RELATION_IDENTIFIERS => [$identifier]]
            );
        }

        if (!$identifier) {
            $this->companyIdentifierService->store(
                $refreshedTask->company,
                $refreshedTask->getIdentifier(),
                $refreshedTask->getIdentifierKey(),
            );
        }
    }
}
