<?php

namespace App\Services\Api\WhatsApp;

use App\Models\WhatsApp\WhatsAppTemplate;
use App\ValueObject\Services\WhatsApp\Response;
use App\ValueObject\Services\WhatsApp\TemplateMessage;
use App\ValueObject\Services\WhatsApp\TextMessage;
use JsonException;
use App\Models\Mongo\WhatsApp\WhatsAppMessage;
use Config;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

class WhatsAppCloudApiRepository extends WhatsAppClientRepository
{
    public const MESSAGING_PRODUCT = 'whatsapp';

    protected string $messagesUrl = '/messages';

    /**
     * Sends a message template.
     *
     * @param string $businessPhoneNumber Configured phone number from account service (Example: ***************)
     * @param string $to WhatsApp ID or phone number for the person you want to send a message to.
     * @param TemplateMessage $templateMessage Template message containing template name and components needed.
     * @param string $language Language code.
     *
     * @return Response
     * @throws JsonException
     * @link https://developers.facebook.com/docs/whatsapp/api/messages/message-templates
     * @link https://developers.facebook.com/docs/whatsapp/api/messages/message-templates#supported-languages
     */
    public function sendTemplate(
        string $businessPhoneNumber,
        string $to,
        TemplateMessage $templateMessage,
        string $language = WhatsAppTemplate::LANGUAGE_EN
    ): Response {
        $data = [
            'messaging_product' => self::MESSAGING_PRODUCT,
            'type' => 'template',
            'to' => $to,
            'template' => [
                'name' => $templateMessage->getTemplate(),
                'language' => [
                    'code' => $language
                ]
            ]
        ];

        $components = $templateMessage->getComponents();
        if (!empty($components)) {
            $data['template']['components'] = $components;
        }

        $response = $this->post($businessPhoneNumber . $this->messagesUrl, $data);

        return new Response(
            $response['messaging_product'],
            $response['contacts'],
            $response['messages']
        );
    }

    /**
     * Sends a Whatsapp text message.
     *
     * @param string $businessPhoneNumber Configured phone number from account service (Example: ***************)
     * @param string $to WhatsApp ID or phone number for the person you want to send a message to.
     * @param TextMessage $textMessage Containing the Text message
     * @param bool $previewUrl Determines if it shows a preview box for URLs contained in the text message.
     *
     * @return Response
     * @throws JsonException
     * @link https://developers.facebook.com/docs/whatsapp/cloud-api/guides/send-messages#text-messages
     */
    public function sendText(
        string $businessPhoneNumber,
        string $to,
        TextMessage $textMessage,
        bool $previewUrl = false
    ): Response {
        $response = $this->post(
            $businessPhoneNumber . $this->messagesUrl,
            [
                'messaging_product' => self::MESSAGING_PRODUCT,
                'recipient_type' => 'individual',
                'type' => 'text',
                'to' => $to,
                'text' => [
                    'preview_url' => $previewUrl,
                    'body' => $textMessage->getMessage()
                ]
            ]
        );

        return new Response(
            $response['messaging_product'],
            $response['contacts'],
            $response['messages']
        );
    }

    public function getTemplates(): array
    {
        $businessAccountId = Config::get('services.whatsapp.business_account_id');
        $content = $this->get($businessAccountId . '/message_templates?status=APPROVED&category=MARKETING');
        return $content['data'];
    }

    /**
     * Mark a message as read
     *
     * @param string $businessPhoneNumber Configured business number from account service (Example: ***************)
     * @param string $messageId The ID for the message from a customer that you received from a webhook notification
     * @return bool
     * @throws \JsonException
     * @link https://developers.facebook.com/docs/whatsapp/cloud-api/guides/mark-message-as-read
     * @link https://developers.facebook.com/docs/whatsapp/on-premises/webhooks/inbound/
     */
    public function markMessageAsRead(string $businessPhoneNumber, string $messageId): bool
    {
        $response = $this->post(
            $businessPhoneNumber . $this->messagesUrl,
            [
                'messaging_product' => self::MESSAGING_PRODUCT,
                'status' => WhatsAppMessage::STATUS_READ,
                'message_id' => $messageId
            ]
        );

        return $response['success'];
    }

    /**
     * Get the media URL and later use it to download the media file
     * @param string $mediaId
     * @return string Absolute URL from WhatsApp so we can download the file
     * @link https://developers.facebook.com/docs/whatsapp/cloud-api/reference/media#retrieve-media-url
     */
    public function getMediaUrl(string $mediaId): string
    {
        return $this->get($mediaId)['url'];
    }

    /**
     * Download the file from the media URL
     * @param string $url
     * @return ResponseInterface
     * @throws GuzzleException
     * @link https://developers.facebook.com/docs/whatsapp/cloud-api/reference/media#download-media
     */
    public function downloadMedia(string $url): ResponseInterface
    {
        $headers = [
            'Authorization' => 'Bearer ' . Config::get('services.whatsapp.access_token')
        ];
        return $this->client->get($url, ['headers' => $headers]);
    }

    public function activateNumber(string $numberId): void
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'pin' => Config::get('services.whatsapp.pin')
        ];
        $this->post($numberId . '/register', $payload);
    }
}
