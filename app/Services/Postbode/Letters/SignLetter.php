<?php

namespace App\Services\Postbode\Letters;

use App\Services\Generator\Pdf\LetterSignPdf;
use App\ServiceTaskResponse;
use App\ValueObject\Postbode\Document;
use Illuminate\Support\Collection;

class SignLetter extends TaskLetter
{
    public function __construct(ServiceTaskResponse $serviceTaskResponse)
    {
        // handle instructions document
        $documents = new Collection();
        $signLetter = new LetterSignPdf($serviceTaskResponse, $serviceTaskResponse->task->sentBy);
        $filename = 'Instructions.pdf';
        $signDocument = new Document($filename, $signLetter->generatePdf()->Output($filename, 'S'));
        $documents->push($signDocument->toArray());

        parent::__construct(
            $serviceTaskResponse,
            $documents
        );
    }
}
