<?php

namespace App;

use Database\Factories\Company\RoleUserFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class RoleUser
 * @package App
 * @property int $id
 * @property int $user_id
 * @property int $role_id
 * @property User $user
 * @property Role $role
 */
class RoleUser extends Model
{
    protected $table = 'role_user';

    public const USER_ID = 'user_id';
    public const ROLE_ID = 'role_id';

    protected $fillable = [
        self::USER_ID,
        self::ROLE_ID
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return RoleUserFactory::new();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }
}
