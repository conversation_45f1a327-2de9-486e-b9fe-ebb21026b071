<?php

namespace App\Models\OpenQuestions\Questions\Templates;

use App\Model;
use Database\Factories\OpenQuestions\Templates\TemplateFieldFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

/**
 * Class TemplateField
 * @property int $template_id
 * @property string $title
 * @property string $type
 * @property int $order
 * @property Template $template
 * @property string $uuid
 * @property string|null $parent_uuid
 * @property Collection<TemplateField> $children
 * @property array $conditions
 */
class TemplateField extends TemplateElement
{
    protected static $unguarded = true;
    protected $table = 'open_question_template_fields';
    public const TEMPLATE_ID = 'template_id';
    public const CONDITION = 'condition';
    public const PARENT = 'parent';

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return TemplateFieldFactory::new();
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class, self::TEMPLATE_ID);
    }

    public function requiredDisabled(): bool
    {
        if ($this->options !== null && count($this->options)) {
            foreach ($this->options as $option) {
                if (array_key_exists(self::PARENT, $option)) {
                    return true;
                }
                if (array_key_exists(self::CONDITION, $option)) {
                    return false;
                }
            }
        }

        foreach ($this->template->fields as $field) {
            if (is_null($field->options)) {
                continue;
            }
            foreach ($field->options as $option) {
                if (
                    array_key_exists(self::CONDITION, $option)
                    && $option[self::CONDITION] > $this->order
                    && $this->order > $field->order
                ) {
                    return true;
                }
            }
        }
        return false;
    }
}
