<?php

namespace App\Models\OpenQuestions\Questions\Templates;

use App\Http\Resources\Template\TransformedTemplateAnswerResource;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\OpenQuestionRelation;
use Database\Factories\OpenQuestions\Templates\TemplateEntryFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

/**
 * Class TemplateEntry
 * @property int $open_question_id
 * @property int $template_id
 * @property int $total_answers
 * @property int $given_answers
 * @property Template $template
 * @property TemplateAnswer[]|Collection $templateAnswers
 * @method static TemplateEntry findOrFail($id)
 */
class TemplateEntry extends OpenQuestionRelation
{
    protected static $unguarded = true;
    protected $table = 'open_question_template_entries';

    public const TEMPLATE_ID = 'template_id';
    public const OPEN_QUESTION_ID = 'open_question_id';
    public const GIVEN_ANSWERS = 'given_answers';
    public const TOTAL_ANSWERS = 'total_answers';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return TemplateEntryFactory::new();
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class, self::TEMPLATE_ID);
    }

    public function openQuestion(): BelongsTo
    {
        return $this->belongsTo(OpenQuestion::class, self::OPEN_QUESTION_ID);
    }

    public function templateAnswers(): HasMany
    {
        return $this->hasMany(TemplateAnswer::class, 'template_entry_id', 'id')
            ->orderBy(TemplateAnswer::ORDER);
    }

    public function updateTemplateProgress(): bool
    {
        $answers = $this->templateAnswers()
            ->whereIn(TemplateAnswer::TYPE, TemplateFieldType::FORM_FIELDS)
            ->get();


        // In some cases, a template may be submitted to a colleague without being fully completed (non-required fields)
        // We still want to show to the colleague it has been completed
        if ($this->openQuestion->status !== OpenQuestion::STATUS_OPEN) {
            $this->total_answers = $answers->count();
            $this->given_answers = $answers->count();
            return $this->save();
        }

        if (!$answers) {
            $this->total_answers = 0;
            $this->given_answers = 0;
            return $this->save();
        }

        $filteredGivenAnswers = $answers->filter(function (TemplateAnswer $answer) {
            // It’s unclear for us at the moment to see if the client was done filling the template
            // That’s why we don’t count it as not filled in until the user submits the question
            if ($answer->type === TemplateFieldType::CHECKBOX) {
                return $answer->answer === 'true';
            }
            return $answer->attachments->isNotEmpty() || !is_null($answer->answer);
        });

        $this->total_answers = $answers->count();
        $this->given_answers = $filteredGivenAnswers->count();

        return $this->save();
    }

    protected $appends = ['transformed_answers'];

    public function getTransformedAnswersAttribute(): array
    {
        return TransformedTemplateAnswerResource::collection(
            $this->templateAnswers
                ->whereNull(TemplateAnswer::PARENT_UUID)
                ->where(TemplateAnswer::TYPE, '!=', TemplateFieldType::STATIC)
                ->sortBy(TemplateAnswer::ORDER)
                ->values()
        )->toArray(new Request());
    }
}
