<?php

namespace App\Models\OpenQuestions\Questions\Templates;

use App\Models\Mongo\MongoModel;
use App\Traits\Models\Mongo\SetClientDatabaseTrait;

/**
 * @property array $form
 * @property int|null $template_id
 * @property string $user_uuid
 * @oroperty \Carbon\Carbon $created_at
 * @oroperty \Carbon\Carbon $updated_at
 */
class TemplateBackup extends MongoModel
{
    use SetClientDatabaseTrait;

    public const COLLECTION = 'open_questions_templates';
    protected $connection = 'mongodb-dynamic';
    protected $collection = self::COLLECTION;

    protected $fillable = [
        self::FORM,
        self::TEMPLATE_ID,
        self::USER_UUID,
        self::CREATED_AT,
        self::UPDATED_AT
    ];

    protected $casts = [
        self::CREATED_AT => 'datetime',
        self::UPDATED_AT => 'datetime',
    ];

    protected const INDEXES = [
        self::TEMPLATE_ID,
        self::USER_UUID,
        self::CREATED_AT,
    ];

    public const FORM = 'form';
    public const TEMPLATE_ID = 'template_id';
    public const USER_UUID = 'user_uuid';
}
