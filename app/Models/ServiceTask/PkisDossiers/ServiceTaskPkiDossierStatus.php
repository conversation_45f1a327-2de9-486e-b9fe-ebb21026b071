<?php

namespace App\Models\ServiceTask\PkisDossiers;

use App\Model;
use Database\Factories\ServiceTask\PkiDossier\ServiceTaskPkiDossierStatusFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Class ServiceTasksPkisDossiersStatus
 *
 * @package App
 *
 * @property string $pkis_id
 * @property string $status
 * @property string $decline_reason
 */
class ServiceTaskPkiDossierStatus extends Model
{
    protected $table = 'service_tasks_pkis_dossiers_status';

    public const PKIS_ID = 'pkis_id';
    public const STATUS = 'status';
    public const DECLINE_REASON = 'decline_reason';
    public const CREATED_AT = 'created_at';

    // All Possible status in the PkiSigning documentation.
    public const REJECTED = 'Afgewezen';
    public const ERROR = 'Fout';
    public const REVOKED = 'Ingetrokken';
    public const INVITATION_EXPIRED = 'UitnodigingVerlopen';
    public const ACTIVE = 'Actief';
    public const PUBLICATED = 'Publiceren';
    public const CONVERTING = 'Converteren';
    public const PROCESSING_DIGIPOORT = 'InVerwerkingDigipoort';
    public const NEW = 'Nieuw';
    public const WAITING_SIGNING = 'WachtOpOndertekening';
    public const WAITING_PUBLICATION = 'WachtOpPublicatie';
    public const WAITING_ADOPTION = 'WachtOpVaststelling';
    public const DOSSIER_SIGNED = 'Afgerond';
    public const CANCELLED = 'Vervallen';

    public $timestamps = false;

    protected $fillable = [
        self::PKIS_ID,
        self::STATUS,
        self::DECLINE_REASON,
        self::CREATED_AT,
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return ServiceTaskPkiDossierStatusFactory::new();
    }
}
