<?php

namespace App\Models;

use App\AccountService;
use App\Company;
use App\CompanyUser;
use App\Context;
use App\Model;
use App\Support\Carbon;
use Database\Factories\ContextCompanyFactory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * @property int $id
 * @property int $context_id
 * @property int $company_id
 * @property int $account_service_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * Relations
 * @property Collection<CompanyUserContextCompany> $companyUserContextCompanies
 * @property Collection<CompanyUser> $companyUsers
 * @property Context $context
 * @property Company $company
 */
class ContextCompany extends Model
{
    use HasFactory;

    public const ID = 'id';
    public const CONTEXT_ID = 'context_id';
    public const COMPANY_ID = 'company_id';
    public const ACCOUNT_SERVICE_ID = 'account_service_id';

    protected $fillable = [
        self::CONTEXT_ID,
        self::COMPANY_ID,
        self::ACCOUNT_SERVICE_ID
    ];

    protected static function newFactory(): ContextCompanyFactory
    {
        return ContextCompanyFactory::new();
    }

    public function context(): BelongsTo
    {
        return $this->belongsTo(Context::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function companyUserContextCompanies(): HasMany
    {
        return $this->hasMany(CompanyUserContextCompany::class);
    }

    public function companyUsers(): HasManyThrough
    {
        return $this->hasManyThrough(
            CompanyUser::class,
            CompanyUserContextCompany::class,
            CompanyUserContextCompany::CONTEXT_COMPANY_ID,
            CompanyUser::ID,
            self::ID,
            CompanyUserContextCompany::COMPANY_USER_ID
        );
    }

    public function beforeDelete(array $options = []): bool
    {
        foreach ($this->companyUserContextCompanies as $companyUserContextCompany) {
            $companyUserContextCompany->delete();
        }
        return parent::beforeDelete($options);
    }

    public function accountService(): BelongsTo
    {
        return $this->belongsTo(AccountService::class);
    }
}
