<?php

namespace App\Models\WhatsApp;

use App\Account;
use App\Model;
use Database\Factories\WhatsApp\WhatAppTemplateFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @method static WhatsAppTemplate updateOrCreate(array $attributes, array $values = [])
 * @property int $id
 * @property string $name
 * @property array $components
 * @property string $language
 * @property string $category
 */
class WhatsAppTemplate extends Model
{
    protected $table = 'whatsapp_templates';

    public const ID = 'id';
    public const NAME = 'name';
    public const COMPONENTS = 'components';
    public const LANGUAGE = 'language';
    public const CATEGORY = 'category';

    public const LANGUAGE_NL = 'nl';
    public const LANGUAGE_EN = 'en_GB';

    protected $fillable = [
        self::ID,
        self::NAME,
        self::COMPONENTS,
        self::LANGUAGE,
        self::CATEGORY
    ];

    public const LANGUAGE_MAP = [
        Account::LANGUAGE_EN => self::LANGUAGE_EN,
        Account::LANGUAGE_NL => self::LANGUAGE_NL
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return WhatAppTemplateFactory::new();
    }

    public function setComponentsAttribute(array $components): void
    {
        $this->attributes[self::COMPONENTS] = json_encode($components);
    }

    public function getComponentsAttribute(): array
    {
        return $this->attributes[self::COMPONENTS]
            ? json_decode($this->attributes[self::COMPONENTS], true)
            : [];
    }
}
