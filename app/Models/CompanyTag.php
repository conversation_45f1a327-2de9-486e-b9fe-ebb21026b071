<?php

namespace App\Models;

use App\Company;
use App\Model;
use Database\Factories\Company\CompanyTagFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyTag extends Model
{
    public const COMPANY_ID = 'company_id';
    public const NAME = 'name';
    public const MAX_TAGS = 5;
    public const TABLE_NAME = 'company_tags';

    protected $table = self::TABLE_NAME;

    protected $fillable = [self::COMPANY_ID, self::NAME];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, Company::ID, self::COMPANY_ID);
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return CompanyTagFactory::new();
    }
}
