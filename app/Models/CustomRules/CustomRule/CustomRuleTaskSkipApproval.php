<?php

namespace App\Models\CustomRules\CustomRule;

use App\CustomRuleFollower\TaskSkipApprovalFollower;
use App\Models\CustomRules\CustomRule;

class CustomRuleTaskSkipApproval extends CustomRule
{
    public const ALLOWED_PARAMS = [];
    public const FOLLOWER_CLASS = TaskSkipApprovalFollower::class;

    public function getFollowerClass(): ?string
    {
        return self::FOLLOWER_CLASS;
    }
}
