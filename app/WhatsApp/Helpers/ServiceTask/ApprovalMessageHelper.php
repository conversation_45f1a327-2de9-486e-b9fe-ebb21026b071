<?php

namespace App\WhatsApp\Helpers\ServiceTask;

use App\Services\FrontAuthService;
use App\Services\RedirectTokenService;
use App\ServiceTaskResponse;
use App\ValueObject\Services\WhatsApp\Components\Text;
use App\ValueObject\Services\WhatsApp\TemplateMessage;
use App\ValueObject\Services\WhatsApp\TemplateMessageComponents;
use Auth;

class ApprovalMessageHelper
{
    public static function buildSentMessage(ServiceTaskResponse $response): TemplateMessage
    {
        $templateMessage = new TemplateMessage('task_approve');
        return $templateMessage->setComponents(self::buildApprovalComponents($response));
    }

    public static function buildReminderMessage(ServiceTaskResponse $response): TemplateMessage
    {
        $templateMessage = new TemplateMessage('reminder');
        return $templateMessage->setComponents(self::buildApprovalComponents($response));
    }

    private static function buildApprovalComponents(ServiceTaskResponse $response): TemplateMessageComponents
    {
        $task = $response->task;
        $user = $response->user;
        $templateComponents = new TemplateMessageComponents();
        $templateComponents->setHeader([
            (new Text($task->title . ' ' . $task->subtitle))->toArray()
        ]);
        $templateComponents->setBody([
            (new Text($user->trans('service_task.' . $task->type . '.title')))->toArray(),
            (new Text($task->company->name))->toArray(),
            (new Text($user->trans('whatsapp.service_task.permission.' . $response->permission)))->toArray(),
        ]);

        $link = resolve(RedirectTokenService::class)
            ->generateToken($task->account_id, FrontAuthService::url($response));
        $templateComponents->addButton([
            (new Text($link))->toArray()
        ]);

        return $templateComponents;
    }

    public static function buildForceApprovalMessage(ServiceTaskResponse $response): TemplateMessage
    {
        $task = $response->task;
        $templateMessage = new TemplateMessage('force_approval');
        $templateComponents = new TemplateMessageComponents();
        $templateComponents->setBody([
            (new Text(Auth::user()->name))->toArray(),
            (new Text($task->title . ' ' . $task->subtitle))->toArray(),
        ]);
        return $templateMessage->setComponents($templateComponents);
    }

    public static function buildCompletedMessage(ServiceTaskResponse $response): TemplateMessage
    {
        $task = $response->task;
        $templateMessage = new TemplateMessage('task_done');
        $templateComponents = new TemplateMessageComponents();
        $templateComponents->setHeader([
            (new Text($task->title . ' ' . $task->subtitle))->toArray(),
        ]);
        return $templateMessage->setComponents($templateComponents);
    }
}
