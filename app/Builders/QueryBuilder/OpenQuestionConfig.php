<?php

namespace App\Builders\QueryBuilder;

use App\AccountService;
use App\Company;
use App\Models\OpenQuestions\OpenQuestionType;
use App\Models\OpenQuestions\Questions\Bookkeeping;
use App\Interfaces\QueryBuilderConfigInterface;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;

class OpenQuestionConfig implements QueryBuilderConfigInterface
{
    public static function getAllowedColumns(): array
    {
        return [
            OpenQuestion::ID,
            OpenQuestion::TITLE,
            OpenQuestion::STATUS,
            OpenQuestion::CATEGORY,
            OpenQuestion::CREATED_AT,
            OpenQuestion::UPDATED_AT,
            OpenQuestion::TYPE_ID,
            OpenQuestion::ACCOUNT_SERVICE_ID,
            OpenQuestion::RELATION_TYPE,
            OpenQuestion::COMPANY_ID,
            OpenQuestion::LAST_SENT,
            OpenQuestion::NEXT_SEND,
            OpenQuestion::URGENT,
            'questionType.id',
            'questionType.key',
            'questionType.display_name',
            'relatedBookkeeping.id',
            'relatedBookkeeping.open_question_id',
            'relatedBookkeeping.amount',
            'relatedBookkeeping.transaction_date',
            'relatedBookkeeping.invoice_number',
            'relatedBookkeeping.currency',
            'relatedBookkeeping.description',
            'relatedBookkeeping.bookkeeping_number',
            'relatedOcr.open_question_id',
            'relatedOcr.transaction_date',
            'relatedOcr.invoice_number',
            'relatedOcr.amount',
            'relatedClient.open_question_id',
            'relatedClient.message',
            'company.id',
            'company.name',
            'company.internal_client_id',
            'accountService.id',
            'accountService.display_name',
            'accountService.service_id',
            'accountService.service.id',
            'accountService.service.image',
            'templateEntry.id',
            'templateEntry.template_id',
            'templateEntry.open_question_id',
            'templateEntry.given_answers',
            'templateEntry.total_answers',
            'templateEntry.templateAnswers.id',
            'templateEntry.templateAnswers.template_entry_id',
            'templateEntry.templateAnswers.answer',
            'templateEntry.templateAnswers.type',
            'attachments.id',
            'attachments.open_question_id',
            'attachments.path',
            'colleagueAuditLogs.id',
            'colleagueAuditLogs.action',
            'colleagueAuditLogs.text',
            'colleagueAuditLogs.replaces',
            'colleagueAuditLogs.open_question_id',
            'colleagueAuditLogs.recipient_type',
            'colleagueAuditLogs.created_at',
            'colleagueAuditLogs.user_id',
            'colleagueAuditLogs.user.id',
            'colleagueAuditLogs.user.firstname',
            'colleagueAuditLogs.user.lastname',
            'questionType.id',
            'questionType.display_name',
        ];
    }

    public static function getSelect(): array
    {
        $allowed = [];
        foreach (self::getAllowedColumns() as $col) {
            if (!str_contains($col, '.')) {
                $allowed[] = OpenQuestion::TABLE . '.' . $col;
            }
        }
        return $allowed;
    }

    public static function getAllowedRelations(): array
    {
        return [
            'company',
            'accountService',
            'questionType',
            'relatedBookkeeping',
            'relatedWage',
            'relatedFiscal',
            'relatedOcr',
            'relatedClient',
            'relatedYearwork',
            'relatedDeclaration',
            'relatedOther',
            'relatedTemplate',
            'accountService.service',
            'templateEntry.templateAnswers',
            'templateEntry.templateAnswers.attachments',
            'templateEntry.templateAnswers.remark',
            'templateAnswersCount',
            'openQuestionUsers',
            'openQuestionUsers.user',
            'colleagueAuditLogs',
            'colleagueAuditLogs.user',
            'attachments',
        ];
    }

    public static function getAllowedExactFilters(): array
    {
        return [
            'questionType.display_name',
            OpenQuestion::CATEGORY,
            OpenQuestion::STATUS,
            OpenQuestion::COMPANY_ID,
            OpenQuestion::ID,
        ];
    }

    public static function getAllowedScopedFilters(): array
    {
        return [
            'year_equals',
            'month_equals',
            'title_search'
        ];
    }

    public static function getAllowedSearchFilters(): array
    {
        return [
            OpenQuestion::TITLE,
            'company.name'
        ];
    }

    public static function getAllFilters(): array
    {
        $exactFilters = array_map(static function ($filter) {
            return AllowedFilter::exact($filter);
        }, self::getAllowedExactFilters());

        $scopedFilters = array_map(static function ($filter) {
            return AllowedFilter::scope($filter);
        }, self::getAllowedScopedFilters());

        return array_merge($exactFilters, self::getAllowedSearchFilters(), $scopedFilters);
    }

    public static function getAllowedSorts(): array
    {
        $sorts = [
            OpenQuestion::TITLE,
            OpenQuestion::STATUS,
            OpenQuestion::CATEGORY,
            OpenQuestion::LAST_SENT,
            'total_amount'
        ];
        $sorts[] = AllowedSort::field(OpenQuestion::CREATED_AT, OpenQuestion::TABLE . '.' . OpenQuestion::CREATED_AT);
        $sorts[] = AllowedSort::field(Bookkeeping::DESCRIPTION, 'related_bookkeeping_description');
        $sorts[] = AllowedSort::field(Bookkeeping::TRANSACTION_DATE, 'related_bookkeeping_transaction_date');
        $sorts[] = AllowedSort::field(Bookkeeping::CURRENCY, 'related_bookkeeping_currency');
        $sorts[] = AllowedSort::field(Bookkeeping::INVOICE_NUMBER, 'related_bookkeeping_invoice_number');
        $sorts[] = AllowedSort::field('service', 'account_service_display_name');
        $sorts[] = AllowedSort::field('client', 'company_name');
        $sorts[] = AllowedSort::field('type', 'question_type_display_name');
        $sorts[] = AllowedSort::field('amount', 'total_amount');
        return $sorts;
    }

    public static function getDefaultSort(): string
    {
        return '-' . OpenQuestion::TABLE . '.' . OpenQuestion::CREATED_AT;
    }

    /**
     * Get aggregate colum needed for sorting by columns outside the main table.
     * @param string $sort
     * @return array|null Array with two values to use as parameters for withAggregate() method
     */
    public static function getAggregate(string $sort): ?array
    {
        return match ($sort) {
            Bookkeeping::DESCRIPTION => ['relatedBookkeeping', Bookkeeping::DESCRIPTION],
            Bookkeeping::TRANSACTION_DATE => ['relatedBookkeeping', Bookkeeping::TRANSACTION_DATE],
            Bookkeeping::CURRENCY => ['relatedBookkeeping', Bookkeeping::CURRENCY],
            Bookkeeping::INVOICE_NUMBER => ['relatedBookkeeping', Bookkeeping::INVOICE_NUMBER],
            'service' => ['accountService', AccountService::DISPLAY_NAME],
            'client' => ['company', Company::NAME],
            'type' => ['questionType', OpenQuestionType::DISPLAY_NAME],
            default => null
        };
    }
}
