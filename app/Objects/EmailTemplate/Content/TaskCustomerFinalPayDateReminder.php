<?php

namespace App\Objects\EmailTemplate\Content;

use App\Mail\ServiceTask\ServiceTaskReminderNotifierFactory;
use App\Models\EmailTemplate\EmailTemplate;
use App\User;
use Illuminate\Mail\Mailable;

class TaskCustomerFinalPayDateReminder extends AbstractEmailTemplateContent
{
    use TaskContentTrait;

    public const KEY_DETAILS = 'details';

    public const EVENT_TASK_VAT_APPROVAL_REMINDER = 'task_vat_approval_final_pay_date_reminder';
    public const EVENT_TASK_WAGE_TAX_APPROVAL_REMINDER = 'task_wage_tax_approval_final_pay_date_reminder';
    public const EVENT_TASK_DIVIDEND_TAX_APPROVAL_REMINDER = 'task_divided_tax_approval_final_pay_date_reminder';

    public const EVENTS = [
        self::EVENT_TASK_VAT_APPROVAL_REMINDER,
        self::EVENT_TASK_WAGE_TAX_APPROVAL_REMINDER,
        self::EVENT_TASK_DIVIDEND_TAX_APPROVAL_REMINDER
    ];

    public const KEYS = [
        self::KEY_GREETINGS,
        self::KEY_SUBJECT,
        self::KEY_INTRO,
        self::KEY_DETAILS,
        self::KEY_BUTTON,
        self::KEY_REGARDS
    ];

    public const TAG_COMPANY = 'company';
    public const TAG_FINAL_PAY_DATE = 'final_pay_date';
    public const TAG_TITLE = 'title';

    public function getDefaultFields(string $language): array
    {
        return [
            [
                'key' => self::KEY_SUBJECT,
                'label' => trans('email_template.field.subject.label', locale: $language),
                'content' => trans('mail.task.task_approval_reminder.subject', locale: $this->language),
                'component' => self::COMPONENT_INPUT
            ],
            [
                'key' => self::KEY_GREETINGS,
                'label' => trans('email_template.field.greetings.label', locale: $language),
                'content' => trans('email_template.field.greetings.default', locale: $this->language),
                'component' => self::COMPONENT_INPUT
            ],
            [
                'key' => self::KEY_INTRO,
                'label' => trans('email_template.field.intro.label', locale: $language),
                'content' => trans('mail.task.task_approval_reminder.intro', locale: $this->language),
                'component' => self::COMPONENT_TEXTEDITOR
            ],
            [
                'key' => self::KEY_DETAILS,
                'label' => trans('email_template.field.details.label', locale: $language),
                'content' => trans('mail.task.task_approval_reminder.details', locale: $this->language),
                'component' => self::COMPONENT_TEXTEDITOR
            ],
            [
                'key' => self::KEY_BUTTON,
                'label' => '',
                'content' => trans('mail.review_button', locale: $this->language),
                'component' => self::COMPONENT_BUTTON
            ],
            [
                'key' => self::KEY_REGARDS,
                'label' => trans('email_template.field.regards.label', locale: $language),
                'content' => trans('email_template.field.regards.default', locale: $this->language),
                'component' => self::COMPONENT_TEXTEDITOR
            ],
        ];
    }

    /**
     * Get tags
     * @param string $language
     * @return array
     */
    public function getTags(string $language): array
    {
        $tags = parent::getTags($language);
        $tags[] = [
            'name' => ':' . self::TAG_COMPANY,
            'description' => trans('email_template.tags.' . self::TAG_COMPANY, locale: $language)
        ];
        $tags[] = [
            'name' => ':' . self::TAG_TITLE,
            'description' => trans('email_template.tags.task.title', locale: $language)
        ];
        $tags[] = [
            'name' => ':' . self::TAG_FINAL_PAY_DATE,
            'description' => trans('email_template.tags.task.' . self::TAG_FINAL_PAY_DATE, locale: $language)
        ];

        return $tags;
    }

    public function getTestMail(User $user, EmailTemplate $template): ?Mailable
    {
        $response = $this->dummyTask($user);
        $mail = ServiceTaskReminderNotifierFactory::create($response)->buildEmailMessage();
        $mail->applyTemplate($template);
        return $mail;
    }
}
