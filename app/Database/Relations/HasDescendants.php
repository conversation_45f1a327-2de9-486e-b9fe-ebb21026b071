<?php

namespace App\Database\Relations;

class HasDescendants extends HasFamily
{
    public const LOCAL_VALUE_RAW = "CONCAT(`%%local%%`.`path`, `%%local%%`.`name`,'/')";

    protected static function getConditionTemplate(string $local_value): string
    {
      //HasAncestors -> Related FQN = LEFT Local FQN
      //HasDescendants -> Local FQN = LEFT Remote FQN

        return "CONCAT($local_value , '/') = LEFT( CONCAT(`%%related%%`.`path`, `%%related%%`.`name`), LENGTH(CONCAT($local_value , '/')))"; //phpcs:ignore
    }

    protected static function leftOperator()
    {
        return ">";
    }

    protected static function rightOperator()
    {
        return "<";
    }
}
