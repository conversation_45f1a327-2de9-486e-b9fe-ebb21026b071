<?php

namespace App\Notifier\Objects\ServiceTask;

use App\Logging\Channels\ServiceLog;
use App\Mail\AbstractMail;
use App\Models\CommunicationChannels\CommunicationChannel;
use App\Notifier\Factories\NotifierMessageFactory;
use App\Notifier\Factories\NotifierServiceFactory;
use App\Notifier\Interfaces\EmailNotifierInterface;
use App\Notifier\Interfaces\LetterNotifierInterface;
use App\Notifier\Interfaces\WhatsAppNotifierInterface;
use App\Notifier\Objects\ServiceTask\Types\BaseNotifier;
use App\ServiceTaskResponse;

class ServiceTaskNotifierService
{
    public function send(array $channels, BaseNotifier $notifierType, ServiceTaskResponse $response): void
    {
        $notifierMessages = collect();

        foreach ($channels as $channel) {
            if ($this->canBeSentThroughChannel($channel, $notifierType)) {
                $notifierMessage = NotifierMessageFactory::create($notifierType, $channel);
                $notifierMessages->push($notifierMessage);
            }
        }

        if ($notifierMessages->isEmpty()) {
            $notifierMessages->push($notifierType->buildDefaultMessage());
        }

        foreach ($notifierMessages as $notifierMessage) {
            $notifierService = NotifierServiceFactory::create($notifierMessage);
            if ($notifierMessage instanceof AbstractMail) {
                $this->handleEmailEventAndLog($response, $notifierMessage);
            }
            $notifierService->send($notifierMessage);
        }
    }

    private function canBeSentThroughChannel(string $channel, BaseNotifier $notifier): bool
    {
        return match ($channel) {
            CommunicationChannel::EMAIL => $notifier instanceof EmailNotifierInterface,
            CommunicationChannel::WHATSAPP => $notifier instanceof WhatsAppNotifierInterface,
            CommunicationChannel::POSTBODE => $notifier instanceof LetterNotifierInterface,
            default => false
        };
    }

    private function handleEmailEventAndLog(ServiceTaskResponse $response, AbstractMail $notifierMessage): void
    {
        $task = $response->task;
        $task->fireMailSentEvent($notifierMessage);
        $log = 'Sent email "' . $notifierMessage::class . '" to ' . $response->user->email;
        if (!empty($notifierMessage->bcc)) {
            $log .= ' with bcc ' . implode(',', array_column($notifierMessage->bcc, 'address'));
        }
        $log .= ' for task #' . $task->id . ' with status ' . $task->status . ' for response #' . $response->id;
        ServiceLog::debug($log);
    }
}
