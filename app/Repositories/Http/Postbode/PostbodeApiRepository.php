<?php

namespace App\Repositories\Http\Postbode;

use App\Models\LetterRequest\LetterRequest;
use App\Repositories\Http\AbstractHttpRepository;
use App\Exceptions\BadGatewayException;
use App\Exceptions\PreconditionFailedException;
use App\ValueObject\Postbode\Letter;
use App\ValueObject\Postbode\LetterResponse;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use JsonException;

class PostbodeApiRepository extends AbstractHttpRepository
{
    /**
     * Send letter
     * @param Letter $letter
     * @return LetterResponse
     * @throws JsonException
     */
    public function sendLetter(Letter $letter): LetterResponse
    {
        try {
            $response = $this->client->post(
                $this->getUrl('/letters'),
                [
                    'headers' => $this->getHeaders(),
                    'json' => $letter->toArray()
                ]
            );
        } catch (ServerException $exception) {
            throw new BadGatewayException($exception);
        } catch (ClientException | GuzzleException $exception) {
            throw new PreconditionFailedException($exception);
        }

        return new LetterResponse(jsonDecode($response->getBody()->getContents()));
    }

    /**
     * Get letter
     * @param int $id
     * @return LetterResponse
     * @throws JsonException
     */
    public function getLetter(int $id): LetterResponse
    {
        try {
            $response = $this->client->get(
                $this->getUrl('/letter/' . $id),
                [
                    'headers' => $this->getHeaders()
                ]
            );
        } catch (ServerException $exception) {
            throw new BadGatewayException($exception);
        } catch (ClientException | GuzzleException $exception) {
            throw new PreconditionFailedException($exception);
        }

        return new LetterResponse(jsonDecode($response->getBody()->getContents()));
    }

    /**
     * Cancel letter
     * @param LetterRequest $letter
     * @return string
     * @throws \JsonException
     */
    public function cancelLetter(LetterRequest $letter): string
    {
        try {
            $response = $this->client->get(
                $this->getUrl('/letter/' . $letter->external_id . '/cancel'),
                [
                    'headers' => $this->getHeaders()
                ]
            );
            return jsonDecode($response->getBody()->getContents());
        } catch (ServerException $exception) {
            throw new BadGatewayException($exception);
        } catch (ClientException | GuzzleException $exception) {
            throw new PreconditionFailedException($exception);
        }
    }

    protected function getHeaders(): array
    {
        return [
            'X-Authorization' => config('services.postbode.api_key')
        ];
    }

    protected function getUrl(string $url): string
    {
        return config('services.postbode.api_url') . '/' . config('services.postbode.mailbox_id') . $url;
    }
}
