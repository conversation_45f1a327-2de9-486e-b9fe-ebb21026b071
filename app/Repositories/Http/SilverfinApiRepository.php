<?php

namespace App\Repositories\Http;

use App\Token;
use App\ValueObject\OAuth\Token as OauthToken;
use App\ValueObject\Services\Declarations\Silverfin\Button;
use App\ValueObject\Services\Declarations\Silverfin\Xbrl;
use GuzzleHttp\Exception\GuzzleException;

class SilverfinApiRepository extends AbstractHttpRepository
{
    public const BASE_ENDPOINT = 'https://live.getsilverfin.com';
    private const API_BASE_ENDPOINT = 'https://live.getsilverfin.com/api/v4';

    private const FIRM_ID = 'firm_id';

    private const NAME_NL = 'name_nl';
    private const TARGET_URL = 'target_url';
    private const PLACEMENT = 'placement';
    private const PLACEMENT_OPTIONS = 'placement_options';
    private const MARKETPLACE_EXPORT_FILE_IDS = 'marketplace_export_file_ids';

    public function getAccessToken(string $firmId, string $code, string $redirectUri): OauthToken
    {
        $url = self::BASE_ENDPOINT . '/f/' . $firmId . '/oauth/token?' . implode(
            '&',
            [
                    'client_id=' . config('services.silverfin.client_id'),
                    'client_secret=' . config('services.silverfin.client_secret'),
                    'code=' . $code,
                    'redirect_uri=' . urlencode($redirectUri),
                    'grant_type=' . 'authorization_code'
                ]
        );

        $response = $this->client->post($url);

        $data = json_decode($response->getBody()->getContents(), true);

        return new OauthToken(
            $data['access_token'],
            $data['expires_in'],
            $data['token_type'],
            $data['refresh_token'],
            $data['scope'],
        );
    }

    public function getAccessTokenWithRefreshToken(Token $refreshToken, string $firmId, string $redirectUri): OauthToken
    {
        $url = self::BASE_ENDPOINT . '/f/' . $firmId . '/oauth/token?' . implode(
            '&',
            [
                'client_id=' . config('services.silverfin.client_id'),
                'client_secret=' . config('services.silverfin.client_secret'),
                'refresh_token=' . $refreshToken->token,
                'redirect_uri=' . urlencode($redirectUri),
                'grant_type=' . 'refresh_token'
            ]
        );

        $response = $this->client->post($url);

        $data = json_decode($response->getBody()->getContents(), true);

        return new OauthToken(
            $data['access_token'],
            $data['expires_in'],
            $data['token_type'],
            $data['refresh_token'],
            $data['scope'],
        );
    }

    public function addButton(Token $token, Button $button): void
    {
        $url = self::API_BASE_ENDPOINT . '/f/' . $token->getData(self::FIRM_ID) . '/app/links';
        $formParams = [
            self::NAME_NL => $button->name,
            self::TARGET_URL => $button->url,
            self::PLACEMENT => $button->placement,
            self::PLACEMENT_OPTIONS . '[' . self::MARKETPLACE_EXPORT_FILE_IDS . '][]' => $button->placementOptions
        ];

        $this->client->post(
            $url,
            [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token->token,
                ],
                'form_params' => $formParams
            ]
        );
    }

    /**
     * @param Token $token
     * @return Button[]
     * @throws GuzzleException
     */
    public function listButtons(Token $token): array
    {
        $url = self::API_BASE_ENDPOINT . '/f/' . $token->getData(self::FIRM_ID) . '/app/links';
        $response = $this->client->get(
            $url,
            [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token->token,
                ]
            ]
        );

        $buttons = json_decode($response->getBody()->getContents(), true, flags: JSON_THROW_ON_ERROR);
        $result = [];
        foreach ($buttons as $button) {
            $result[] = new Button($button['name'], $button['target_url'], $button['placement'], 2);
        }

        return $result;
    }

    public function fetchXbrl(Token $token, string $companyId, string $periodId, string $fileId): Xbrl
    {
        //phpcs:ignore
        $url = self::API_BASE_ENDPOINT . '/f/' . $token->getData(self::FIRM_ID) . '/companies/' . $companyId . '/periods/' . $periodId . '/export_file_instances/' . $fileId;
        $response = $this->client->get(
            $url,
            [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token->token,
                ]
            ]
        );

        $data = json_decode($response->getBody()->getContents(), true, flags: JSON_THROW_ON_ERROR);
        return new Xbrl($data['file_name'], $data['content']);
    }
}
