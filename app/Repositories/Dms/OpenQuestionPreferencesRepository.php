<?php

namespace App\Repositories\Dms;

use App\AccountService;
use App\Company;
use App\Models\Dms\OpenQuestionCompanyDmsPreference;
use App\Models\Dms\OpenQuestionDmsPreference;
use App\Models\OpenQuestions\Questions\OpenQuestion;

class OpenQuestionPreferencesRepository
{
    public function getForCompany(
        Company $company,
        AccountService $accountService,
        string $relationType
    ): ?OpenQuestionCompanyDmsPreference {
        return OpenQuestionCompanyDmsPreference::query()
            ->where(OpenQuestionCompanyDmsPreference::COMPANY_ID, $company->id)
            ->where(OpenQuestionCompanyDmsPreference::ACCOUNT_SERVICE_ID, $accountService->id)
            ->where(OpenQuestionCompanyDmsPreference::RELATION_TYPE, $relationType)
            ->first();
    }

    public function updateOrCreateForCompany(
        Company $company,
        AccountService $accountService,
        array $preferences,
        string $relationType
    ): OpenQuestionCompanyDmsPreference {
        return OpenQuestionCompanyDmsPreference::query()->updateOrCreate([
            OpenQuestionCompanyDmsPreference::COMPANY_ID => $company->id,
            OpenQuestionCompanyDmsPreference::ACCOUNT_SERVICE_ID => $accountService->id,
            OpenQuestionCompanyDmsPreference::RELATION_TYPE => $relationType
        ], [
            OpenQuestionCompanyDmsPreference::PREFERENCES => $preferences
        ]);
    }

    public function updateOrCreateForOpenQuestion(
        OpenQuestion $openQuestion,
        AccountService $accountService,
        array $preferences
    ): OpenQuestionDmsPreference {
        return OpenQuestionDmsPreference::query()->updateOrCreate([
            OpenQuestionDmsPreference::OPEN_QUESTION_ID => $openQuestion->id,
            OpenQuestionDmsPreference::ACCOUNT_SERVICE_ID => $accountService->id
        ], [
            OpenQuestionDmsPreference::PREFERENCES => $preferences
        ]);
    }
}
