<?php

namespace App\Repositories\EmailTemplate;

use App\Models\EmailTemplate\EmailTemplate;
use Illuminate\Database\Eloquent\Collection;
use App\Exceptions\ConflictException;

class EmailTemplateRepository
{
    public function getById(int $id): ?EmailTemplate
    {
        return EmailTemplate::find($id);
    }

    public function getByType(int $accountId, string $language, string $type): Collection
    {
        return EmailTemplate::query()
            ->where(EmailTemplate::ACCOUNT_ID, $accountId)
            ->where(EmailTemplate::LANGUAGE, $language)
            ->where(EmailTemplate::TYPE, $type)
            ->get();
    }

    public function store(EmailTemplate $emailTemplate): ?EmailTemplate
    {
        if (isset($emailTemplate->id)) {
            $attributes = $emailTemplate->attributesToArray();
            if (EmailTemplate::where(EmailTemplate::ID, $emailTemplate->id)->update($attributes)) {
                return $emailTemplate;
            }
        } elseif ($emailTemplate->save()) {
            return $emailTemplate;
        }
        return null;
    }

    public function count(int $accountId, string $language, string $type): int
    {
        return EmailTemplate::query()
            ->where(EmailTemplate::ACCOUNT_ID, $accountId)
            ->where(EmailTemplate::LANGUAGE, $language)
            ->where(EmailTemplate::TYPE, $type)
            ->count();
    }

    public function delete(EmailTemplate $template): bool
    {
        return $template->delete();
    }
}
