<?php

namespace App\Repositories\Templates;

use App\Account;
use App\Exceptions\ModelNotSavedException;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\User;
use App\Support\Carbon;
use Illuminate\Support\Collection;

class TemplateRepository
{
    public function getById(int $id): Template
    {
        return Template::query()->findOrFail($id);
    }

    public function getByIds(array $ids): Collection
    {
        return Template::query()->whereIn(Template::ID, $ids)->get();
    }

    public function store(Template $template): Template
    {
        if (!$template->save()) {
            throw new ModelNotSavedException('Template save returned false');
        }
        return $template;
    }

    public function update(Template $template, array $attributes): Template
    {
        $template->name = $attributes[Template::NAME];
        $template->intro_text = $attributes[Template::INTRO_TEXT];
        $template->category = $attributes[Template::CATEGORY];
        $template->settings = $attributes[Template::SETTINGS];
        $template->save();
        return $template;
    }

    public function delete(Template $template): bool
    {
        return $template->delete();
    }

    public function archive(Template $template, User $user = null): bool
    {
        $template->archived_at = Carbon::now();
        if (!empty($user)) {
            $template->archived_by = $user->id;
        }
        return $template->save();
    }

    public function getByCategory(string $category, int $account_id): Collection
    {
        return Template::query()
            ->where(Template::ACCOUNT_ID, $account_id)
            ->where(Template::CATEGORY, $category)
            ->whereNull(Template::ARCHIVED_AT)
            ->get();
    }

    public function getByAccountId(int $account_id, array $with = []): Collection
    {
        return Template::query()
            ->where(Template::ACCOUNT_ID, $account_id)
            ->whereNull(Template::ARCHIVED_AT)
            ->with($with)
            ->get();
    }

    public function hasTemplates(int $account_id): bool
    {
        return Template::query()
            ->where(Template::ACCOUNT_ID, $account_id)
            ->whereNull(Template::ARCHIVED_AT)
            ->exists();
    }
}
