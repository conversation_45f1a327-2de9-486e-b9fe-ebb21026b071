<?php

namespace App\Repositories;

use App\AccountService;
use App\ServiceTask;
use App\ServiceTaskGroup;
use Exception;
use Illuminate\Support\Collection;

class ServiceTaskGroupRepository
{
    /**
     * @deprecated Groups have UUID now
     * @param int $id
     * @return null|ServiceTaskGroup
     */
    public static function getById(int $id): ?ServiceTaskGroup
    {
        return ServiceTaskGroup::whereKey($id)->first();
    }

    /**
     * Store group using upsert so that existing one is updated and does not trigger primary key conflict
     * @param ServiceTaskGroup $group New or existing group to save or update.
     * @return int Number of affected rows (0 on failure, 1 on success)
     */
    public function store(ServiceTaskGroup $group): int
    {
        return ServiceTaskGroup::query()->upsert(
            [
                ServiceTaskGroup::ACCOUNT_ID => $group->account_id,
                ServiceTaskGroup::ACCOUNT_SERVICE_ID => $group->account_service_id,
                ServiceTaskGroup::REFERENCE_NAME => $group->reference_name,
                ServiceTaskGroup::UUID => $group->uuid
            ],
            [ServiceTaskGroup::UUID],
            [
                ServiceTaskGroup::REFERENCE_NAME,
                ServiceTaskGroup::UPDATED_AT,
                ServiceTaskGroup::UPDATED_BY
            ]
        );
    }

    /**
     * @param AccountService $accountService
     * @param string $name
     * @return ServiceTaskGroup|null
     */
    public static function getByName(AccountService $accountService, string $name): ?ServiceTaskGroup
    {
        return ServiceTaskGroup::query()
            ->where(ServiceTaskGroup::ACCOUNT_ID, $accountService->account_id)
            ->where(ServiceTaskGroup::ACCOUNT_SERVICE_ID, $accountService->id)
            ->where(ServiceTaskGroup::REFERENCE_NAME, $name)
            ->first();
    }

    /**
     * @deprecated
     * @param AccountService $accountService
     * @param string $referenceName
     * @return ServiceTaskGroup
     */
    public static function getOrCreateByAccountServiceAndReferenceName(
        AccountService $accountService,
        string $referenceName
    ): ServiceTaskGroup {
        /** @var ServiceTaskGroup $serviceTaskGroup */
        $serviceTaskGroup = ServiceTaskGroup::query()->firstOrCreate([
            ServiceTaskGroup::ACCOUNT_ID => $accountService->account_id,
            ServiceTaskGroup::ACCOUNT_SERVICE_ID => $accountService->id,
            ServiceTaskGroup::REFERENCE_NAME => $referenceName
        ]);
        return $serviceTaskGroup;
    }
}
