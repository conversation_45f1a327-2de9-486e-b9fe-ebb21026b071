<?php

namespace App\Repositories\OpenQuestions;

use App\Account;
use App\Models\OpenQuestions\UnverifiedQuestion;
use Illuminate\Database\Eloquent\Collection;

class UnverifiedQuestionsRepository
{
    /**
     * @param string $uuid
     * @return UnverifiedQuestion|null
     */
    public function getByUuid(string $uuid): ?UnverifiedQuestion
    {
        return UnverifiedQuestion::query()->where('uuid', $uuid)->first();
    }

    public function getById(string $id): UnverifiedQuestion
    {
        return UnverifiedQuestion::query()->findOrFail($id);
    }

    /**
     * @param Account $account
     * @return Collection<UnverifiedQuestion>
     */
    public function getUnconnectedQuestions(Account $account): Collection
    {
        return UnverifiedQuestion::query()
            ->where(UnverifiedQuestion::ACCOUNT_ID, $account->id)
            ->whereNotNull(UnverifiedQuestion::VERIFIED_AT)
            ->whereNull(UnverifiedQuestion::COMPANY_ID)
            ->with('files')
            ->get();
    }
}
