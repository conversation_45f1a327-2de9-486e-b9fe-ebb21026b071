<?php

namespace App\Repositories\Account\RibbonNotification;

use App\Models\RibbonNotification\RibbonNotification;
use App\Models\RibbonNotification\RibbonNotificationEmail;
use Illuminate\Database\Eloquent\Collection;

class RibbonNotificationEmailRepository
{
    public function getByType(string $mailType): ?RibbonNotificationEmail
    {
        return RibbonNotificationEmail::query()
            ->join(
                'ribbon_notifications',
                'ribbon_notifications.id',
                '=',
                'ribbon_notification_emails.ribbon_notification_id'
            )
            ->where('ribbon_notification_emails.' . RibbonNotificationEmail::TYPE, $mailType)
            ->where('ribbon_notifications.' . RibbonNotification::START_DATE, '<=', now())
            ->where('ribbon_notifications.' . RibbonNotification::END_DATE, '>=', now())
            ->select('ribbon_notification_emails.*')
            ->first();
    }

    public function deleteByRibbonNotificationId(int $ribbonNotificationId)
    {
        return RibbonNotificationEmail::query()
            ->where(RibbonNotificationEmail::RIBBON_NOTIFICATION_ID, $ribbonNotificationId)
            ->delete();
    }

    public function deleteAll()
    {
        return RibbonNotificationEmail::query()->delete();
    }

    public function create(int $ribbonNotificationId, string $type): RibbonNotificationEmail
    {
        return RibbonNotificationEmail::query()
            ->create([
                RibbonNotificationEmail::RIBBON_NOTIFICATION_ID => $ribbonNotificationId,
                RibbonNotificationEmail::TYPE => $type
            ]);
    }

    public function exists(string $mailType): bool
    {
        return RibbonNotificationEmail::query()
            ->join(
                'ribbon_notifications',
                'ribbon_notifications.id',
                '=',
                'ribbon_notification_emails.ribbon_notification_id'
            )
            ->where('ribbon_notification_emails.' . RibbonNotificationEmail::TYPE, $mailType)
            ->where('ribbon_notifications.' . RibbonNotification::START_DATE, '<=', now())
            ->where('ribbon_notifications.' . RibbonNotification::END_DATE, '>=', now())
            ->select('ribbon_notification_emails.*')
            ->exists();
    }
}
