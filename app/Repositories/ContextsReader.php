<?php

namespace App\Repositories;

use App\Context;
use App\Membership;

class ContextsReader extends RepositoryBase
{
    public function getContextsMapByAccountIds(array $accountIds, array $columns = ['*']): array
    {
        $columns = $this->addColumnsIfMissing(
            $columns,
            [
                Context::ID,
                Context::PARENT_ID,
                Context::ACCOUNT_ID,
                Context::DELETED_AT
            ]
        );

        $contexts = Context::query()->whereIn(Context::ACCOUNT_ID, $accountIds)
            ->select($columns)
            ->get();

        return $this->getMap($contexts, Context::ID);
    }

    public function hydrateContexts(array $contextsMap, array $membershipsMap, array $accountsMap)
    {
        $contextIdToMemberships = $this->getGroup(collect($membershipsMap), Membership::CONTEXT_ID);

        foreach ($contextsMap as $contextId => $context) {
            $parentId = $context->getAttribute(Context::PARENT_ID);
            if (!empty($parentId)) {
                $context->setRelation('parent', $contextsMap[$parentId]);
            }

            $accountId = $context->getAttribute(Context::ACCOUNT_ID);
            if (array_key_exists($accountId, $accountsMap)) {
                $context->setRelation('account', $accountsMap[$accountId]);
            }

            if (array_key_exists($contextId, $contextIdToMemberships)) {
                $context->setRelation('memberships', collect($contextIdToMemberships[$contextId]));
            }
        }

        return $contextsMap;
    }
}
