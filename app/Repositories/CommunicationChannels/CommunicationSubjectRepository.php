<?php

namespace App\Repositories\CommunicationChannels;

use App\Models\CommunicationChannels\CommunicationSubject;
use Illuminate\Support\Collection;

class CommunicationSubjectRepository
{
    /**
     * Get all subjects
     * @return Collection
     */
    public function getAll(): Collection
    {
        return CommunicationSubject::all();
    }

    /**
     * @return string[]
     */
    public function getSubjectTypes(): array
    {
        return CommunicationSubject::query()
            ->select(CommunicationSubject::TYPE)
            ->distinct()
            ->pluck(CommunicationSubject::TYPE)
            ->toArray();
    }
}
