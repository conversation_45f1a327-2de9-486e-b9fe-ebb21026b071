<?php

namespace App\Factories\Models\OpenQuestions\Questions\Templates;

use App\Helpers\HixUuid;
use App\Models\OpenQuestions\Questions\Templates\TemplateAnswer;
use App\User;

class TemplateAnswerFactory
{
    public static function createNew(
        User $user,
        string $title,
        string $type,
        int $order,
        bool $required,
        ?string $schemaKey = null,
        ?array $schemaContent = null
    ): TemplateAnswer {
        $answer = new TemplateAnswer();
        $answer->title = $title;
        $answer->type = $type;
        $answer->order = $order;
        $answer->required = $required;
        $answer->uuid = HixUuid::generate($user->account_id);
        $answer->schema_key = $schemaKey;
        $answer->schema_content = $schemaContent;
        $answer->created_by = $user->id;
        return $answer;
    }
}
