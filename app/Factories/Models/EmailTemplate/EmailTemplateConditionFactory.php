<?php

namespace App\Factories\Models\EmailTemplate;

use App\Models\EmailTemplate\EmailTemplate;
use App\Models\EmailTemplate\EmailTemplateCondition;

class EmailTemplateConditionFactory
{
    public static function createForTemplate(array $payload, EmailTemplate $template): array
    {
        $conditionModels = [];
        foreach ($payload['conditions'] as $condition) {
            if (isset($condition['selected']) && $condition['selected']) {
                $event = $condition['event'];
                $uploadTypeId = null;
                if (str_contains($event, '#')) {
                    list($event, $uploadTypeId) = explode('#', $event);
                }
                $conditionModels[] = self::create($template, $event, $uploadTypeId);
            }
        }
        return $conditionModels;
    }

    public static function create(
        EmailTemplate $template,
        string $event,
        ?int $uploadTypeId = null
    ): EmailTemplateCondition {
        $condition = new EmailTemplateCondition();
        $condition->language = $template->language;
        $condition->account()->associate($template->account);
        $condition->template()->associate($template);
        $condition->event = $event;
        $condition->upload_type_id = $uploadTypeId;
        return $condition;
    }
}
