<?php

namespace App\Http\Controllers\Api\V1\OpenQuestion;

use App\Http\ApiRequests\OpenQuestion\CreateOcrQuestionRequest;
use App\Http\Controllers\Controller;
use App\Http\Resources\OpenQuestions\OpenQuestionApiResource;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Services\OpenQuestions\OpenQuestionService;
use App\Services\OpenQuestions\OpticalCharacterRecognitionService;

class OcrController extends Controller
{
    public function __construct(
        private readonly OpenQuestionService $service,
        private readonly OpticalCharacterRecognitionService $ocrService
    ) {
    }

    public function createQuestion(CreateOcrQuestionRequest $request): OpenQuestionApiResource
    {
        $openQuestion = $this->service->createQuestionApi(
            OpenQuestionCategory::OCR,
            $request->genericAttributes(),
            $request->user(),
            $request->agentData(),
            $request->getFiles()
        );
        $this->ocrService->create($request->specificAttributes(), $openQuestion);
        return OpenQuestionApiResource::make($openQuestion);
    }
}
