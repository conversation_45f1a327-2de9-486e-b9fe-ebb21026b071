<?php

namespace App\Http\Controllers\Api\App;

use App\Auth\SessionKey;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\App\Session\GetCurrentRequest;
use App\Http\Resources\Account\AccountResource;
use App\Http\Resources\User\ProfileResource;
use App\License;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Route;
use Session;

class SessionController extends Controller
{
    public function getAccount(GetCurrentRequest $request): Response|ResponseFactory
    {
        return $request->ok([
            'account' => AccountResource::make($request->account()),
            'ip' => $request->getClientIp()
        ]);
    }

    public function getPortalUser(GetCurrentRequest $request): Response|ResponseFactory
    {
        $user = $request->user();
        $account = $request->account();

        if (
            !$user
            || !$account->hasLicense(License::NEW_HIX_UI)
            || in_array('frontAuth', Route::getCurrentRoute()->middleware())
            || Session::has(SessionKey::FRONT_AUTH_TOKEN)
        ) {
            return $request->ok();
        }

        return $request->ok(ProfileResource::make($user));
    }

    public function getFrontUser(GetCurrentRequest $request): Response|ResponseFactory
    {
        $user = $request->user();
        $account = $request->account();

        // return empty response for the obvious reasons first.
        if (
            !$user
            || !$user->isExternal()
            || !$account->hasLicense(License::NEW_HIX_UI)
            || !$account->hasLicense(License::NEW_HIX_UI_CLIENT)
            || !in_array('frontAuth', Route::getCurrentRoute()->middleware())
        ) {
            return $request->ok();
        }

        // For complexity reasons, I split these checks up from the others.
        // By now we know the user is there, but we have to determine that if it's a front-user the token is there.
        if (
            !auth()->check() && !Session::has(SessionKey::FRONT_AUTH_TOKEN)
        ) {
            return $request->ok();
        }

        return $request->ok([
            'user' => ProfileResource::make($user),
            'token' => Session::get(SessionKey::FRONT_AUTH_TOKEN),
        ]);
    }
}
