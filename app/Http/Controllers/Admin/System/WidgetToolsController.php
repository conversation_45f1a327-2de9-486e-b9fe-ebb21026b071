<?php

namespace App\Http\Controllers\Admin\System;

use App\GenericWidgetCategory;
use App\Http\Controllers\Controller;
use App\Http\Resources\GenericWidget\CustomWidgetResource;
use App\Http\VueRequests\System\GenericWidget\DownloadCustomWidgetRequest;
use App\Http\VueRequests\System\GenericWidget\SearchCustomWidgetRequest;
use App\Http\VueRequests\System\GenericWidget\UpdateCustomWidgetRequest;
use App\Http\VueRequests\System\GenericWidget\UploadRequest;
use App\License;
use App\Services\Widget\CustomWidgetService;
use App\Services\Widget\GenericWidgetService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class WidgetToolsController extends Controller
{
    public function __construct(
        private readonly CustomWidgetService $customWidgetService,
        private readonly GenericWidgetService $genericWidgetService
    ) {
    }

    public function uploadGenericWidget(UploadRequest $request): Response|Application|ResponseFactory
    {
        try {
            $widget = $request->getWidget();
            $this->genericWidgetService->updateFileStorage($widget['reference_name'], $request->getFileContent());

            Artisan::call('db:seed --class=GenericWidgetSeeder --force');
        } catch (\Exception $e) {
            return $request->error(null, null, $e->getMessage());
        }

        return $request->ok();
    }

    public function searchCustomWidget(SearchCustomWidgetRequest $request): Response|ResponseFactory
    {
        $widgets = $this->customWidgetService->searchCustomWidgets(
            $request->id(),
            $request->name(),
            $request->accountId()
        );

        return $request->ok([
            'widgets' => CustomWidgetResource::collection($widgets),
            'categories' => GenericWidgetCategory::ALL,
            'licenses' => License::query()->pluck('reference_name'),
        ]);
    }

    public function updateCustomWidget(
        UpdateCustomWidgetRequest $request
    ): StreamedResponse|Application|ResponseFactory|Response {
        try {
            $widgetData = $request->getWidget();
            $widget = $this->genericWidgetService->getById($widgetData['id']);

            $this->customWidgetService->updateCustomWidget(
                $widgetData['id'],
                $request->displayName(),
                $request->description(),
                $widgetData['categories'],
                $widgetData['licenses']
            );

            $widget->refresh();

            $widgetFileContent = $this->customWidgetService->createTemplate($widget);
            $this->genericWidgetService->updateFileStorage($widget->reference_name, $widgetFileContent);

            if ($request->getImage() instanceof UploadedFile) {
                $this->genericWidgetService->updatePublicImage(
                    $widget->reference_name,
                    $request->getImage()->getContent()
                );
            }

            return $request->success();
        } catch (Throwable $exception) {
            return $request->error(null, null, $exception->getMessage());
        }
    }

    public function downloadCustomWidget(
        DownloadCustomWidgetRequest $request
    ): StreamedResponse|Application|ResponseFactory|Response {
        try {
            $genericWidget = $this->genericWidgetService->getById($request->id);
            $fileContent = $this->genericWidgetService->getWidgetPackage($genericWidget);

            return response()->streamDownload(
                function () use ($fileContent) {
                    echo $fileContent;
                },
                $genericWidget->reference_name . '.zip'
            );
        } catch (Throwable $exception) {
            return $request->error(null, null, $exception->getMessage());
        }
    }
}
