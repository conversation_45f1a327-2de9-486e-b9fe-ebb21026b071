<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\EmptyStates\GetEmptyStateRequest;
use App\Http\Requests\EmptyStates\SaveEmptyStateRequest;
use App\Services\Account\EmptyStateService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;

class EmptyStateController extends Controller
{
    public function __construct(private readonly EmptyStateService $emptyStateService)
    {
    }

    public function getForUser(GetEmptyStateRequest $request): array
    {
        $lang = \Auth::user()?->language;
        return $this->emptyStateService->getByTypeAndLang($request->type, $lang);
    }

    public function getByType(GetEmptyStateRequest $request): array
    {
        return $this->emptyStateService->getAllByType($request->type);
    }

    public function saveForType(SaveEmptyStateRequest $request): Response|ResponseFactory
    {
        $this->emptyStateService->saveForType($request->type, $request->all());
        $message = "Empty state saved for type: " . ucfirst($request->type);
        return $request->success(message: $message);
    }
}
