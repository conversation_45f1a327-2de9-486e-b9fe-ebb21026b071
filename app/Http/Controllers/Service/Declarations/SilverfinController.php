<?php

namespace App\Http\Controllers\Service\Declarations;

use App\Account;
use App\AccountService;
use App\Exceptions\NotFoundException;
use App\Factories\TaskFileFactory;
use App\Http\Controllers\Controller;
use App\Http\VueRequests\AccountService\ConfigureRequest;
use App\Http\VueRequests\AccountService\Silverfin\CallbackRequest;
use App\Http\VueRequests\AccountService\Silverfin\LinkRequest;
use App\Logging\Channels\ServiceLog;
use App\Repositories\Http\SilverfinApiRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Service;
use App\Services\AccountServiceService;
use App\Services\BataviaService;
use App\Services\CompanyIdentifierService;
use App\Services\Declarations\DeclarationsService;
use App\Services\Service\Declarations\SilverfinServiceProvider;
use App\ValueObject\Declarations\DeclarationData;
use App\ValueObject\Identifier\FiscalIdentifierFactory;
use Illuminate\Http\RedirectResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class SilverfinController extends Controller
{
    public function __construct(
        private readonly AccountServiceService $accountServiceService,
        private readonly AccountServiceRepository $accountServiceRepository,
        private readonly CompanyIdentifierService $companyIdentifierService
    ) {
    }

    public function configure(ConfigureRequest $request, AccountService $accountService)
    {
        /** @var SilverfinServiceProvider $provider */
        $provider = $accountService->getProvider();
        return $request->ok($provider->getAuthorizationUrl());
    }

    /**
     * @param CallbackRequest $request
     * @return RedirectResponse
     * @throws Throwable
     */
    public function callback(CallbackRequest $request): RedirectResponse
    {
        $accountService = $this->accountServiceRepository->getById($request->state);
        /** @var SilverfinServiceProvider $provider */
        $provider = $accountService->getProvider();

        try {
            $provider->callback($request->authorized_firm_id, $request->code);
            //phpcs:ignore
            return redirect('https://' . $accountService->account->hostname . '/manage/services/' . $accountService->id . '/overview');
        } catch (Throwable $e) {
            // phpcs:ignore
            ServiceLog::error(
                'Something went wrong handling callback for Silverfin: ' . $e::class . ' - ' . $e->getMessage()
            );
            throw $e;
        }
    }

    private function getAccountServiceForAccount(Account $account): AccountService
    {
        $accountService = $this->accountServiceService->getServiceByAccount($account, Service::SILVERFIN_SERVICE);
        if ($accountService === null) {
            throw new NotFoundException('Account #' . $account->id . ' does not have a valid Silverfin service');
        }
        return $accountService;
    }

    /**
     * @param LinkRequest $request
     * @return RedirectResponse
     * @throws Throwable
     */
    public function createTask(LinkRequest $request): RedirectResponse
    {
        $account = $request->account();
        $accountService = $this->getAccountServiceForAccount($account);

        /** @var SilverfinServiceProvider $provider */
        $provider = $accountService->getProvider();
        $firmId = $request->firm_id;
        $silverfinCompanyId = $request->company_id;

        $xbrl = $provider->fetchXbrl($firmId, $silverfinCompanyId, $request->period_id, $request->object_id);
        $taskFile = TaskFileFactory::create($accountService, $xbrl->content, $xbrl->name);

        $fiscalIdentifier = FiscalIdentifierFactory::create($taskFile->identifier, $taskFile->identifier_type);
        $companyIdentifier = $this->companyIdentifierService->findFiscalIdentifier($account, $fiscalIdentifier);

        if ($companyIdentifier !== null) {
            $company = $companyIdentifier->company;
            $declarationData = new DeclarationData($taskFile, $taskFile->parsed_data);
            resolve(DeclarationsService::class)->generateTask($accountService, $declarationData, $company);
        } else {
            $taskFile->save();
        }

        // phpcs:ignore
        return redirect(
            SilverfinApiRepository::BASE_ENDPOINT . '/f/' . $firmId . '/' . $silverfinCompanyId . '/ledgers'
        );
    }

    /**
     * @param LinkRequest $request
     * @return StreamedResponse
     * @throws Throwable
     */
    public function downloadFile(LinkRequest $request): StreamedResponse
    {
        $account = $request->account();
        $accountService = $this->getAccountServiceForAccount($account);

        /** @var SilverfinServiceProvider $provider */
        $provider = $accountService->getProvider();

        $xbrl = $provider->fetchXbrl($request->firm_id, $request->company_id, $request->period_id, $request->object_id);
        $taskFile = TaskFileFactory::create($accountService, $xbrl->content, $xbrl->name);
        $pdf = resolve(BataviaService::class)->getReport($taskFile->getContent());

        return response()->streamDownload(
            function () use ($pdf) {
                echo $pdf->getContent();
            },
            $pdf->getFilename()
        );
    }
}
