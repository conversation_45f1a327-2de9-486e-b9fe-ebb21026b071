<?php

namespace App\Http\Controllers\Service\OpenQuestions;

use App\Helpers\Http\CsvExportHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\OpenQuestions\StatusOverviewResource;
use App\Http\VueRequests\OpenQuestions\ManagerStatusOverviewCsvRequest;
use App\Http\VueRequests\OpenQuestions\StatusOverviewCsvRequest;
use App\Http\VueRequests\OpenQuestions\StatusOverviewFiltersRequest;
use App\Http\VueRequests\OpenQuestions\StatusOverviewRequest;
use App\Services\OpenQuestions\OpenQuestionService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class StatusOverviewController extends Controller
{
    private OpenQuestionService $openQuestionService;

    public function __construct(
        OpenQuestionService $openQuestionService
    ) {
        $this->openQuestionService = $openQuestionService;
    }

    /**
     * Filters for open questions status overview page
     *
     * @param StatusOverviewFiltersRequest $request
     * @return array
     */
    public function filters(StatusOverviewFiltersRequest $request): array
    {
        return $this->openQuestionService->getStatusOverviewFilters(
            $request->account(),
            $request->user(),
            $request->user()->activeCompanyIds()
        );
    }

    /**
     * Filters for open questions manager status overview page
     *
     * @param StatusOverviewFiltersRequest $request
     * @return array
     */
    public function managerFilters(StatusOverviewFiltersRequest $request): array
    {
        return $this->openQuestionService->getStatusOverviewFilters(
            $request->account(),
            $request->user(),
            $request->account()->activeCompanyIds()
        );
    }

    /**
     * Filtered open questions for status overview page
     *
     * @param StatusOverviewRequest $request
     * @return AnonymousResourceCollection
     */
    public function filteredQuestions(StatusOverviewRequest $request): AnonymousResourceCollection
    {
        return StatusOverviewResource::collection(
            $this->openQuestionService->getFilteredQuestions(
                $request->user(),
                $request->filters()
            )
        );
    }

    /**
     * Filtered open questions for status overview page
     *
     * @param StatusOverviewRequest $request
     * @return AnonymousResourceCollection
     */
    public function managerFilteredQuestions(StatusOverviewRequest $request): AnonymousResourceCollection
    {
        return StatusOverviewResource::collection(
            $this->openQuestionService->getAllFilteredQuestions(
                $request->account(),
                $request->filters()
            )
        );
    }

    /**
     * Get the Open Questions Status Overview in CSV (Download).
     * @param StatusOverviewCsvRequest $request
     * @return Response
     */
    public function userCsv(StatusOverviewCsvRequest $request): Response
    {
        $data = $this->openQuestionService->getFilteredQuestionsCsv(
            $request->user()->activeCompanyIds(),
            $request->filters()
        );
        return CsvExportHelper::respondCsv($data, $request->path());
    }

    /**
     * Get the Open Questions Status Overview in CSV (Download) for managers.
     * @param ManagerStatusOverviewCsvRequest $request
     * @return Response
     */
    public function managerCsv(ManagerStatusOverviewCsvRequest $request): Response
    {
        $data = $this->openQuestionService->getFilteredQuestionsCsv(
            $request->account()->activeCompanyIds(),
            $request->filters()
        );
        return CsvExportHelper::respondCsv($data, $request->path());
    }
}
