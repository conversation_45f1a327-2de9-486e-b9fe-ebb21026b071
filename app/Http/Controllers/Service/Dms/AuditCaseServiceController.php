<?php

namespace App\Http\Controllers\Service\Dms;

use App\AccountService;
use App\Exceptions\Services\Crm\Simplicate\InvalidCredentialsException;
use App\Http\Controllers\Controller;
use App\Http\Resources\AccountService\IndexResource;
use App\Http\VueRequests\Service\GetAuditCaseConnectionRequest;
use App\Http\VueRequests\Service\SetupAuditCaseConnectionRequest;
use App\Services\Gateway\AuditCase\AuditCaseService;

class AuditCaseServiceController extends Controller
{
    public function setupConnection(SetupAuditCaseConnectionRequest $request, AccountService $accountService)
    {
        /** @var AuditCaseService $auditCaseService */
        $auditCaseService = $accountService->getProvider();
        if ($auditCaseService->setupConnection($accountService->account, $request->configuration())) {
            return $request->success(
                IndexResource::make($accountService),
                'service.audit_case.setup_connection.succeed'
            );
        }
        throw new InvalidCredentialsException('Error configuring AuditCase service #' . $accountService->id);
    }

    public function checkConnection(GetAuditCaseConnectionRequest $request, AccountService $accountService)
    {
        /** @var AuditCaseService $auditCaseService */
        $auditCaseService = $accountService->getProvider();
        return $request->ok($auditCaseService->checkConnection());
    }
}
