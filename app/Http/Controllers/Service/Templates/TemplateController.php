<?php

namespace App\Http\Controllers\Service\Templates;

use App\Exceptions\NotFoundException;
use App\Factories\Models\OpenQuestions\Questions\Templates\TemplateFactory;
use App\Http\Controllers\Controller;
use App\Http\Resources\OpenQuestions\QuestionTypesResource;
use App\Http\Resources\OpenQuestions\Template\TemplateEntryResource;
use App\Http\Resources\Template\AvailableTemplateResource;
use App\Http\Resources\Template\DefaultTemplateResource;
use App\Http\Resources\Template\IndexResource;
use App\Http\VueRequests\OpenQuestions\Templates\AvailableTemplatesForCategoryRequest;
use App\Http\VueRequests\OpenQuestions\Templates\CreateEntryRequest;
use App\Http\VueRequests\OpenQuestions\Templates\CreateRequest;
use App\Http\VueRequests\OpenQuestions\Templates\DeleteRequest;
use App\Http\VueRequests\OpenQuestions\Templates\DownloadTemplateEntryRequest;
use App\Http\VueRequests\OpenQuestions\Templates\GetFiltersRequest;
use App\Http\VueRequests\OpenQuestions\Templates\GetTemplateAnswerAttachmentRequest;
use App\Http\VueRequests\OpenQuestions\Templates\GetTemplateAnswerAttachmentsRequest;
use App\Http\VueRequests\OpenQuestions\Templates\GetTemplateAnswersRequest;
use App\Http\VueRequests\OpenQuestions\Templates\GetTemplateTypeIdRequest;
use App\Http\VueRequests\OpenQuestions\Templates\IndexRequest;
use App\Http\VueRequests\OpenQuestions\Templates\UpdateRequest;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Repositories\Templates\DefaultTemplateRepository;
use App\Service;
use App\Services\AccountServiceService;
use App\Services\CompanyService;
use App\Services\OpenQuestions\OpenQuestionService;
use App\Services\OpenQuestions\OpenQuestionTypeService;
use App\Services\OpenQuestions\Templates\TemplateAnswerAttachmentService;
use App\Services\OpenQuestions\Templates\TemplateAnswerService;
use App\Services\OpenQuestions\Templates\TemplateEntryService;
use App\Services\OpenQuestions\Templates\TemplateService;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class TemplateController extends Controller
{
    public function __construct(
        private readonly TemplateService $templateService,
        private readonly CompanyService $companyService,
        private readonly TemplateEntryService $templateEntryService,
        private readonly OpenQuestionService $openQuestionService,
        private readonly TemplateAnswerService $templateAnswerService,
        private readonly TemplateAnswerAttachmentService $templateAnswerAttachmentService,
        private readonly OpenQuestionTypeService $openQuestionTypeService,
        private readonly AccountServiceService $accountServiceService,
        private readonly DefaultTemplateRepository $defaultTemplateRepository
    ) {
    }

    public function index(IndexRequest $request): AnonymousResourceCollection
    {
        $templates = $this->templateService->getByAccountId($request->account());
        return IndexResource::collection($templates);
    }

    public function default(IndexRequest $request): AnonymousResourceCollection
    {
        $templatesIds = $this->defaultTemplateRepository->getDefaultTemplateIds();
        $templates = $this->templateService->getByIds($templatesIds);

        return DefaultTemplateResource::collection($templates);
    }

    public function create(CreateRequest $request)
    {
        $template = TemplateFactory::createFromRequest(
            $request->account(),
            $request->user(),
            $request->name,
            $request->intro_text,
            $request->category,
            $request->settings,
        );

        $storedTemplate = $this->templateService->createWithFields(
            $template,
            $request->user(),
            $request->fields,
            $request->is_default_template
        );
        return $request->success(IndexResource::make($storedTemplate), 'template.create_template.succeed');
    }

    public function update(UpdateRequest $request)
    {
        $template = $this->templateService->getById(
            $request->template_id,
            $request->user()
        );
        $updatedTemplate = $this->templateService->update(
            $template,
            $request->getTemplateAttributes(),
            $request->user(),
            $request->fields,
            $request->is_default_template
        );
        return $request->success(IndexResource::make($updatedTemplate), 'template.update.succeed');
    }

    public function delete(DeleteRequest $request)
    {
        $result = $this->templateService->delete($request->template_id, $request->user());
        if (!$result) {
            return $request->error('template.delete.error');
        }
        return $request->success(null, 'template.delete.succeed');
    }

    public function createEntryOld(CreateEntryRequest $request): Response
    {
        // Gather Template and Company for Policy checks
        $template = $this->templateService->getById($request->template_id, $request->user());
        $this->companyService->getById($request->company_id, $request->user());

        $openQuestion = $this->openQuestionService->createQuestion(
            $request->genericAttributes(),
            $request->service_name,
            $request->user(),
            $request->agentData()
        );

        $templateEntry = $this->templateEntryService->createOld(
            $openQuestion,
            $request->specificAttributes(),
            $template,
            $request->fields,
            $request->user(),
            $request->remarks()
        );

        return $request->success($templateEntry);
    }

    public function createEntry(CreateEntryRequest $request)
    {
        // Gather Template and Company for Policy checks
        $template = $this->templateService->getById($request->template_id, $request->user());
        $this->companyService->getById($request->company_id, $request->user());

        $this->templateService->validateConditions($template, collect($request->fields));

        $openQuestion = $this->openQuestionService->createQuestion(
            $request->genericAttributes(),
            $request->service_name,
            $request->user(),
            $request->agentData()
        );

        $templateEntry = $this->templateEntryService->create(
            $openQuestion,
            $request->specificAttributes(),
            $template,
            $request->fields,
            $request->user(),
        );

        return $request->success($templateEntry);
    }

    public function availableTemplates(AvailableTemplatesForCategoryRequest $request)
    {
        $templates = $this->templateService->getAvailableTemplates($request->account());

        return $request->ok($templates->mapToGroups(function (Template $item) use ($request) {
            return [$item->category => AvailableTemplateResource::make($item)->toArray($request)];
        }));
    }

    public function availableTemplatesForCategory(AvailableTemplatesForCategoryRequest $request)
    {
        return $request->ok(
            AvailableTemplateResource::collection(
                $this->templateService->indexCategory(
                    $request->category,
                    $request->account()
                )
            )->toArray($request)
        );
    }

    public function answers(GetTemplateAnswersRequest $request)
    {
        try {
            $templateEntry = $this
                ->templateAnswerService
                ->getTemplateEntryByOpenQuestionId($request->openQuestionId);
        } catch (Throwable) {
            return $request->error(null, 'exceptions.template_answer.invalid_question');
        }

        return $request->ok(TemplateEntryResource::make($templateEntry));
    }

    public function attachment(GetTemplateAnswerAttachmentRequest $request): StreamedResponse
    {
        try {
            $templateAnswer = $this->templateAnswerService->getById($request->templateAnswerId);
            $content = $this->templateAnswerAttachmentService->get(
                $templateAnswer,
                $request->filename
            );

            // Get mime type of file.
            $file_info = new \finfo(FILEINFO_MIME_TYPE);
            $mime_type = $file_info->buffer($content);

            return response()->stream(
                function () use ($content) {
                    echo $content;
                },
                200,
                [
                    'content-type' => $mime_type
                ]
            );
        } catch (Throwable $exception) {
            throw new NotFoundException('Could not find this file', 0, $exception);
        }
    }

    public function attachments(GetTemplateAnswerAttachmentsRequest $request): StreamedResponse
    {
        try {
            $attachments = $this->templateAnswerAttachmentService->getByIds(
                $request->template_entry_id,
                $request->attachment_ids,
                $request->user()
            );

            $content = $this->templateAnswerAttachmentService->downloadAttachments($attachments);

            return response()->stream(
                function () use ($content) {
                    echo $content;
                },
                200,
                ['content-type' => 'application/zip',]
            );
        } catch (Throwable $exception) {
            throw new NotFoundException('Could not find this file', 0, $exception);
        }
    }

    public function getTemplateType(GetTemplateTypeIdRequest $request): QuestionTypesResource
    {
        $manualOpenQuestionService = $this->accountServiceService->getByService(
            $request->account(),
            $request->user(),
            Service::MANUAL_QUESTIONS_SERVICE
        );

        return QuestionTypesResource::make(
            $this->openQuestionTypeService->getTypeByKey(
                $manualOpenQuestionService->id,
                OpenQuestion::TYPE_TEMPLATE
            )
        );
    }

    /**
     * @throws FileNotFoundException
     */
    public function downloadTemplateEntry(DownloadTemplateEntryRequest $request): StreamedResponse
    {
        $entry = $this->templateEntryService->getById($request->template_entry_id, $request->user());

        // We use the getDmsFile function, because there is no need to duplicate code.
        $file = $entry->openQuestion->getDmsFile();

        return response()->streamDownload(
            function () use ($file) {
                echo $file->getFileContent();
            },
            $file->getFilename(),
            [
                'Content-Type' => $file->getMimeType()
            ]
        );
    }

    public function filters(GetFiltersRequest $request)
    {
        return [
            'categories' => $request->account()
                ->templates()
                ->pluck('category')
                ->unique()
                ->map(function ($category) {
                    return [
                        'value' => $category,
                        'name' => trans('open_question.category.' . $category),
                    ];
                })
        ];
    }
}
