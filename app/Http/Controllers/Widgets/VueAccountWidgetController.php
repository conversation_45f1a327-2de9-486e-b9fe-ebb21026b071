<?php

namespace App\Http\Controllers\Widgets;

use App\Account;
use App\AccountWidget;
use App\Events\AccountWidgetPropertiesChangedEvent;
use App\GenericWidget;
use App\GenericWidgetCategory;
use App\Http\Controllers\Controller;
use App\Http\Resources\AccountWidget\AccountWidgetResource;
use App\Http\Resources\AccountWidget\GroupResourceCollection;
use App\Http\Resources\GenericWidget\GenericWidgetCategoryResource;
use App\Http\Resources\GenericWidget\GenericWidgetResource;
use App\Http\VueRequests\AccountWidget\AvailableRequest;
use App\Http\VueRequests\AccountWidget\AvailableWidgetsRequest;
use App\Http\VueRequests\AccountWidget\DeleteRequest;
use App\Http\VueRequests\AccountWidget\GetSettingsRequest;
use App\Http\VueRequests\AccountWidget\IndexRequest;
use App\Http\VueRequests\AccountWidget\StoreRequest;
use App\Http\VueRequests\AccountWidget\UpdateRequest;
use App\Repositories\GenericWidgetRepository;
use App\Services\WidgetService;
use Illuminate\Support\Collection;

class VueAccountWidgetController extends Controller
{
    private WidgetService $widgetService;

    public function __construct(WidgetService $widgetService)
    {
        $this->widgetService = $widgetService;
    }

    public function index(IndexRequest $request)
    {
        $account_widgets = Account::getHostAccount()->accountWidgets()
            ->with(['generic_parent', 'context_parent', 'generic_widget'])
            ->select('context_widgets.*')->get()->sortByLabel();
        $account_widgets->loadMissing('generic_widget.categories');
        return AccountWidgetResource::collection($account_widgets);
    }

    public function settings(GetSettingsRequest $request, AccountWidget $accountWidget)
    {
        return $this->widgetService->getWidgetSettings($accountWidget, 'account');
    }

    public function availability(AvailableRequest $request, AccountWidget $account_widget)
    {
        return GroupResourceCollection::make($account_widget->context->children()->with('widgets')->get())
            ->account_widget_id($account_widget->id);
    }

    public function availableWidgets(AvailableWidgetsRequest $request)
    {
        $account = $request->account();
        $installedWidgetIds = AccountWidget::query()
            ->where('account_id', $account->id)
            ->distinct()
            ->pluck('generic_widget_id')
            ->toArray();
        $repository = resolve(GenericWidgetRepository::class);
        $widgets = $repository->getAvailableWidgets($account);

        $widgetResource = [];
        foreach ($widgets as $widget) {
            $widget->installed = in_array($widget->id, $installedWidgetIds);
            $widgetResource[] = GenericWidgetResource::make($widget);
        }

        return $widgetResource;
    }

    public function categories()
    {
        return GenericWidgetCategoryResource::collection(GenericWidgetCategory::all());
    }

    public function newStore(StoreRequest $request)
    {
        $generic_widget_ids = array_map(function ($generic_widget) {
            return $generic_widget['id'];
        }, $request->generic_widgets);

        $generic_widgets = GenericWidget::find($generic_widget_ids);

        if (empty($generic_widgets)) {
            return $request->error();
        }

        $account_widgets = new Collection([]);
        foreach ($generic_widgets as $generic_widget) {
            //TODO::need to change the account to the one that is active once we have the switch
            $account_widgets->push(AccountWidget::createFromGenericWidget($generic_widget, Account::getHostAccount()));
        }

        return $request->success(AccountWidgetResource::collection($account_widgets));
    }

    public function newUpdate(AccountWidget $account_widget, UpdateRequest $request)
    {
        $account_widget->settingsSetValuesFromRequest($request, "account");

        if ($account_widget->save()) {
            if ($account_widget->isOneOrMoreAttributesChangedSincePrevious(['properties'])) {
                AccountWidgetPropertiesChangedEvent::fire($account_widget);
            }
            return $request->success(AccountWidgetResource::make($account_widget));
        } else {
            return $request->error();
        }
    }

    public function delete(AccountWidget $account_widget, DeleteRequest $request)
    {
        if ($account_widget->delete()) {
            return $request->success();
        } else {
            return $request->error();
        }
    }
}
