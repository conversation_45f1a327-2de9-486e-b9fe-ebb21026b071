<?php

namespace App\Http\Controllers;

use App\Helpers\TotpHelper;
use Illuminate\Http\Request;

class TotpController
{
    // phpcs:ignore
    public function totp_calc(Request $request, $totp_secret): array
    {
        return TotpHelper::generate($totp_secret, true);
    }

    // phpcs:ignore
    public function totp_calc_query(Request $request): array|string
    {
        if ($request->input('key', false)) {
            return TotpHelper::generate($request->input('key'), true, $request->input('timestamp'));
        }
        return "No totp key supplied";
    }
}
