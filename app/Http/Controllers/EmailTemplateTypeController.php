<?php

namespace App\Http\Controllers;

use App\Factories\Models\EmailTemplate\EmailTemplateContentFactory;
use App\Http\Resources\EmailTemplate\EmailTemplateResource;
use App\Http\VueRequests\EmailTemplate\TemplateTypeRequest;
use App\Http\VueRequests\EmailTemplate\TypeListRequest;
use App\Models\EmailTemplate\EmailTemplate;
use App\Repositories\EmailTemplate\EmailTemplateRepository;
use App\Services\EmailTemplate\EmailTemplateTypeService;

class EmailTemplateTypeController
{
    /**
     * Get list of all available EmailTemplate types for all languages with translated names.
     * @param TypeListRequest $request
     * @return array[]
     */
    public function list(TypeListRequest $request, EmailTemplateTypeService $service): array
    {
        return [
            [
                'language' => 'nl',
                'sections' => $service->getSectionList($request->account(), 'nl')
            ],
            [
                'language' => 'en',
                'sections' => $service->getSectionList($request->account(), 'en')
            ]
        ];
    }

    /**
     * Get all variants of a specific template type.
     * If none exist a default one filled with default texts is returned.
     * If the user s
     * @param TemplateTypeRequest $request
     * @param EmailTemplateRepository $templateRepo
     * @return array
     */
    public function templates(TemplateTypeRequest $request, EmailTemplateRepository $templateRepo): array
    {
        $content = EmailTemplateContentFactory::create($request->account(), $request->type);
        $content->setLanguage($request->language);

        $templateModels = $templateRepo->getByType($request->account()->id, $request->language, $request->type);
        $templates = EmailTemplateResource::collection($templateModels)->toArray($request);
        if ($templateModels->count() === 0 || $request->has('new')) {
            $name = 'Variant ' . $templateModels->count() + 1;
            $templates[] = $this->getDefaultTemplate($request, $name);
        }
        return [
            'type' => $request->type,
            'type_name' => trans('email_template.type.' . $request->type, locale: $request->language),
            'language' => $request->language,
            'tags' => $content->getTags($request->user()->language),
            'templates' => $templates
        ];
    }

    protected function getDefaultTemplate(TemplateTypeRequest $request, $name): array
    {
        $template = new EmailTemplate();
        $template->type = $request->type;
        $template->language = $request->language;
        $template->name = $name;
        $template->account()->associate($request->account());
        return EmailTemplateResource::make($template)->toArray($request);
    }
}
