<?php

namespace App\Http\Controllers;

use App\Exceptions\BadRequestException;
use App\Services\Account\AccountService;
use App\Services\RedirectTokenService;
use Exception;
use Symfony\Component\HttpFoundation\Response;

class RedirectTokenController extends Controller
{
    public function __construct(
        private readonly RedirectTokenService $redirectTokenService,
        private readonly AccountService $accountService
    ) {
    }

    public function redirect(string $token)
    {
        try {
            $redirectToken = $this->redirectTokenService->getRedirectToken($token);
            $targetAccount = $this->accountService->getById($redirectToken->getAccountId());

            if (is_null($targetAccount)) {
                throw new BadRequestException('Target account does not exist');
            }
            if (!str_starts_with($redirectToken->getUrl(), $targetAccount->uri)) {
                // phpcs:ignore
                throw new BadRequestException(
                    'Target account with URL ' . $targetAccount->uri . ' and token URL: ' . $redirectToken->getUrl(
                    ) . ' do not match'
                );
            }
        } catch (Exception $exception) {
            \Log::error('Redirect Token error: ' . $exception->getMessage());
            return response(status: Response::HTTP_NOT_FOUND);
        }
        return redirect($redirectToken->getUrl());
    }
}
