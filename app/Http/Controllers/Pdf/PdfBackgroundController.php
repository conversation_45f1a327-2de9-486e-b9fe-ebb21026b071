<?php

namespace App\Http\Controllers\Pdf;

use App\Exceptions\NotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\GetPdfBackgroundFileRequest;
use App\Http\Requests\GetPdfBackgroundsRequest;
use App\Http\Requests\SavePdfBackgroundRequest;
use App\Http\Resources\Pdf\PdfBackgroundResource;
use App\Repositories\Pdf\PdfBackgroundRepository;
use App\Services\Pdf\PdfBackgroundService;
use finfo;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class PdfBackgroundController extends Controller
{
    public function __construct(
        private readonly PdfBackgroundService $customRulePdfBackgroundService,
        private readonly PdfBackgroundRepository $customRulePdfBackgroundRepository
    ) {
    }

    public function get(GetPdfBackgroundsRequest $request): Response|ResponseFactory
    {
        $pdfBackgrounds = $this->customRulePdfBackgroundRepository->getByAccount($request->account());
        return $request->ok(PdfBackgroundResource::collection($pdfBackgrounds));
    }

    /**
     * @throws FileNotFoundException
     */
    public function getFile(GetPdfBackgroundFileRequest $request): StreamedResponse
    {
        $pdfBackground = $this->customRulePdfBackgroundRepository->getById(
            $request->account(),
            $request->pdf_background_id,
        );

        if (empty($pdfBackground)) {
            throw new NotFoundException('Could not find PDF background #' . $pdfBackground);
        }

        $content = $pdfBackground->getContent();

        $file_info = new finfo(FILEINFO_MIME_TYPE);
        $mime_type = $file_info->buffer($content);
        $headers = [
            'content-type' => $mime_type
        ];

        return response()->stream(
            function () use ($content) {
                echo $content;
            },
            200,
            $headers
        );
    }

    public function save(SavePdfBackgroundRequest $request): Response|Application|ResponseFactory
    {
        try {
            $pdfBackgrounds = $this->customRulePdfBackgroundService->saveBackgrounds(
                $request->backgrounds(),
                $request->account()
            );
            return $request->success(
                PdfBackgroundResource::collection($pdfBackgrounds),
                key: 'custom_rule.save_pdf_background.success'
            );
        } catch (Throwable) {
            return $request->error(key: 'custom_rule.save_pdf_background.error');
        }
    }
}
