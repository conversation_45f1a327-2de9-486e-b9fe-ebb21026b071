<?php

namespace App\Http\VueRequests\App\Front\Question;

use App\Http\VueRequests\PostRequest;

/**
 * @property int $open_question_id
 * @property array files
 */
class SaveUploadRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->isExternal();
    }

    public function rules(): array
    {
        return [
            'open_question_id' => 'required|integer|exists:open_questions,id',
            'files' => 'required|array',
            'files.*.name' => 'required|string',
            'files.*.uuid' => 'required|string',
        ];
    }
}
