<?php

namespace App\Http\VueRequests\TaskFilePlaceholder;

use App\Http\VueRequests\PostRequest;
use App\Models\TaskFile\Placeholder;
use App\Services\TaskFiles\PlaceholderService;
use App\Traits\Requests\RequestInlineValidationTrait;

/**
 * @property int $file
 * @property null|array $placeholders
 * @property null|array $new_one_time_signers
 * @property bool $custom_signing_order_enabled
 * @property array $pages
 * @property array $pagesSelectedToCopyPlaceholder
 * @property null|array $backgrounds
 */
class StoreRequest extends PostRequest
{
    use RequestInlineValidationTrait;

    protected $model_name = Placeholder::class;
    protected $action = 'store';

    public function rules(): array
    {
        return [
            'custom_signing_order_enabled' => 'required|boolean',
            'pages' => 'required|array|min:1|max:200',
            'pages.*.width' => 'required|numeric|min:1|max:5000',
            'pages.*.height' => 'required|numeric|min:1|max:5000',
            'placeholders' => 'array|max:' . PlaceholderService::MAXIMUM_ALLOWED_PLACEHOLDERS,
            'placeholders.*.page' => 'required|int|min:1|max:200',
            'placeholders.*.left' => 'required|numeric|min:0|max:100',
            'placeholders.*.top' => 'required|numeric|min:0|max:100',
            'placeholders.*.w' => 'required|int|min:1|max:1000',
            'placeholders.*.h' => 'required|int|min:1|max:1000',
            'placeholders.*.title' => 'max:40',
            'placeholders.*.type' => 'required|max:40',
            'placeholders.*.filled_by' => 'required_without:placeholders.*.one_time_signer_id',
            'placeholders.*.signing_order' => 'required_if:custom_signing_order_enabled,true',
            'placeholders.*.pagesSelectedToCopyPlaceholder' => 'array|nullable',
            'new_one_time_signers' => 'array',
            'new_one_time_signers.*.unique_id' => 'required',
            'new_one_time_signers.*.firstname' => 'required|max:50|no_url',
            'new_one_time_signers.*.lastname' => 'required|max:50|no_url',
            'new_one_time_signers.*.mobile' => 'required|valid_mobile',
            'new_one_time_signers.*.email' => 'required|email:filter,rfc'
        ];
    }

    public function messages(): array
    {
        // phpcs:disable
        return [
            'pages.*.height.min' => trans('placeholder.store.validation.pages.height'),
            'pages.*.width.min' => trans('placeholder.store.validation.pages.width'),
            'placeholders.*.filled_by.required_without' => trans(
                'placeholder.store.validation.placeholders.filled_by.required'
            ),
            'placeholders.*.signing_order.required_if' => trans(
                'placeholder.store.validation.placeholders.signing_order.required_if'
            ),
            'placeholders.max' => trans('placeholder.store.validation.placeholders.maximum_exceeded'),
            'new_one_time_signers.*.firstname.required' => trans(
                'placeholder.store.validation.new_one_time_signers.firstname.required'
            ),
            'new_one_time_signers.*.firstname.max' => trans(
                'placeholder.store.validation.new_one_time_signers.firstname.max'
            ),
            'new_one_time_signers.*.lastname.required' => trans(
                'placeholder.store.validation.new_one_time_signers.lastname.required'
            ),
            'new_one_time_signers.*.lastname.max' => trans(
                'placeholder.store.validation.new_one_time_signers.lastname.max'
            ),
            'new_one_time_signers.*.mobile.required' => trans(
                'placeholder.store.validation.new_one_time_signers.mobile.required'
            ),
            'new_one_time_signers.*.mobile.valid_mobile' => trans(
                'placeholder.store.validation.new_one_time_signers.mobile.valid_mobile'
            ),
            'new_one_time_signers.*.email.required' => trans(
                'placeholder.store.validation.new_one_time_signers.email.required'
            ),
            'new_one_time_signers.*.email.email' => trans(
                'placeholder.store.validation.new_one_time_signers.email.email'
            ),
        ];
        // phpcs:enable
    }
}
