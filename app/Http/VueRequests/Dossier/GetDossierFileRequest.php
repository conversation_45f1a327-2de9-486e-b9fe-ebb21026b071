<?php

namespace App\Http\VueRequests\Dossier;

use App\Http\VueRequests\GetRequest;
use App\Models\Dossiers\DossierFile;

/**
 * @property string $filename
 * @property string $company_id
 * @property int $dossier_file_uuid
 */
class GetDossierFileRequest extends GetRequest
{
    protected $model_name = DossierFile::class;

    public function authorize(): bool
    {
        return $this->user()->hasCompany($this->company_id);
    }
}
