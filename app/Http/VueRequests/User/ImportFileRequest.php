<?php

namespace App\Http\VueRequests\User;

use App\Http\VueRequests\PostRequest;

class ImportFileRequest extends PostRequest
{
    protected $model_name = 'User';
    protected $action = 'ImportFile';

    public function rules(): array
    {
        return [
            'file' => 'file|required|extensions:csv,xlsx',
            'type' => 'string|nullable|in:internal,external',
            'encoding' => 'string',
            'context_ids' => 'array|min:1|required|exists:contexts,id'
        ];
    }

    /**
     * If no encoding is specified the default is set to "windows-1252" which is used by MS Excel NL.
     * @return string Encoding of CSV file content.
     */
    public function getEncoding(): string
    {
        if (!isset($this->encoding)) {
            return 'windows-1252';
        }
        return $this->encoding;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getContextIds(): array
    {
        return $this->context_ids;
    }

    public function messages(): array
    {
        return [
            'file.extensions' => trans('user.import.validation.extensions'),
            'context_ids.required' => trans('user.import.validation.context_ids'),
        ];
    }
}
