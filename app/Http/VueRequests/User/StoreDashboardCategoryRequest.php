<?php

namespace App\Http\VueRequests\User;

use App\Http\VueRequests\PostRequest;

class StoreDashboardCategoryRequest extends PostRequest
{
    protected $model_name = 'DashboardCategory';
    protected $action = 'storeDashboardCategory';

    public function rules(): array
    {
        return [
            'name' => 'required|max:20',
        ];
    }

    public function beforeInputValidation(): void
    {
        $name = trim($this->input('name'));
        $this->getInputSource()->set('name', $name);
    }

    public function messages(): array
    {
        return [
            'name.required' => trans('user.dashboard_category.field_name.required'),
            'name.max' => trans('user.dashboard_category.field_name.max'),
        ];
    }
}
