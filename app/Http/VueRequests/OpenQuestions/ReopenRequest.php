<?php

namespace App\Http\VueRequests\OpenQuestions;

/**
 * @property int $open_question_id
 * @property bool $delete_attachments
 */
class ReopenRequest extends OpenQuestionsRequest
{
    public function rules(): array
    {
        return [
            'open_question_id' => 'required',
            'delete_attachments' => 'bool',
        ];
    }

    public function messages(): array
    {
        return [
            'open_question_id.required' => trans('validation.required', ['attribute' => 'open_question_id']),
            'delete_attachments.bool' => trans('validation.boolean', ['attribute' => 'delete_attachments']),
        ];
    }
}
