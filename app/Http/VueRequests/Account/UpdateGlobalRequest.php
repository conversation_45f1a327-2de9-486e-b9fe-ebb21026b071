<?php

namespace App\Http\VueRequests\Account;

use App\Http\VueRequests\PostRequest;

class UpdateGlobalRequest extends PostRequest
{
    protected $model_name = 'Account';
    protected $action = 'updateSettings';

    public function rules(): array
    {
        return [
            'language' => 'required',
            'support_email' => 'email_list|max:255|required',
        ];
    }

    public function messages(): array
    {
        return [
            'language.required' => $this->getCommonErrorTranslation(),
            'support_email.email_list' => $this->getCommonErrorTranslation(),
            'support_email.max' => $this->getCommonErrorTranslation(),
            'support_email.required' => $this->getCommonErrorTranslation(),
        ];
    }
}
