<?php

namespace App\Http\VueRequests\AccountService;

use App\AccountService;
use App\Http\VueRequests\GetRequest;

/**
 * Class GetAccountServiceRequest
 *
 * @property AccountService $account_service
 * @package App\Http\VueRequests\Service
 */
class GetAccountServiceRequest extends GetRequest
{
    public function rules(): array
    {
        return [];
    }

    public function authorize(): bool
    {
        return $this->user()->isManagerOfHostAccount();
    }

    public function accountService(): AccountService
    {
        return $this->account_service;
    }
}
