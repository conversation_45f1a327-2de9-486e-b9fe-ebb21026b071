<?php

namespace App\Http\Resources\CustomRules;

use App\Models\CustomRules\CustomRuleUser;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property CustomRuleUser $resource
 */
class RuleUsersResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'val' => $this->resource->user_id,
            'label' => $this->resource->user->name,
            'role' => $this->resource->role
        ];
    }
}
