<?php

namespace App\Http\Resources\UserWidget;

use Illuminate\Http\Resources\Json\JsonResource;

class ApiUserWidgetResource extends JsonResource
{
    protected $external_start_key;

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'label' => $this->showLabel(),
            'reference_name' => $this->reference_name,
            'image' => $this->showImage(),
            'description' => $this->showDescription(),
            'settings_complete' => $this->settingsComplete(),
            'totp_url' => $this->when(
                $this->hasFilledSetting('totp_secret'),
                route('api.user.widget.totp', [$this->id])
            ),
            'requires_browser_extension' => $this->requiresBrowserExtension(),
            'start_url' => $this->getStartUrl(),
            'setup_url' => $this->getSetupUrl(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    public function requiresBrowserExtension(): bool
    {
        return (isset($this->properties) && isset($this->properties['cors_ext']) && $this->properties['cors_ext']);
    }

    public function getStartUrl(): string
    {
        return route('api.user.widget.start', [$this->id]);
    }

    public function getSetupUrl(): string
    {
        return route('api.user.widget.setup', [$this->id]);
    }
}
