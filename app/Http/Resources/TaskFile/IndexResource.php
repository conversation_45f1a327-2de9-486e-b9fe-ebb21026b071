<?php

namespace App\Http\Resources\TaskFile;

use App\GenericService;
use App\Service;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class IndexResource extends JsonResource
{
    public function toArray($request)
    {
        $service = $this->getService();
        return [
            'id' => $this->id,
            'account_service_id' => $this->account_service_id,
            'name' => empty($this->title) ? $this->filename : $this->title,
            'subtitle' => $this->subtitle ?? '',
            'image' => $service->image ?? '/images/services/' . Str::snake($service->display_name) . '.png',
            'can_be_deleted' => $this->canBeDeletedByUser(auth()->user()),
            'group_reference_name' => $this->group_reference_name,
            'related_file_titles' => $this->getRelatedFileTitles()
        ];
    }

    public function getService(): Service
    {
        if (empty($this->accountService)) {
            return Service::where(Service::REFERENCE_NAME, GenericService::MANUAL_TASKS)->firstOrFail();
        }
        return $this->accountService->service;
    }
}
