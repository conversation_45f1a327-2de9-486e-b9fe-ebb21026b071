<?php

namespace App\Http\Resources\Company;

use App\Company;
use App\Helpers\Color\Color;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class CompanyResource
 */
class CompanyInfoResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        /** @var Company $company */
        $company = $this->resource;

        $userCount = $company->companyUsers()->count();

        return [
            'id' => $company->id,
            'title' => $company->name,
            'color' => Color::generateColor($company->id, $company->name),
            'disabled' => $company->isArchived(),
            'subtitle' => $company->archived_at !== null ?
                trans('common.archived') :
                $userCount . ' ' . trans('context.amount_of_memberships'),
            'warning' => !$company->hasInternalUsers(),
        ];
    }
}
