<?php

namespace App\Http\Resources;

use App\User;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ContextWidgetResourceCollection extends ResourceCollection
{
    protected $user;

    public function __construct($resource)
    {
        $this->user = auth()->user();
        parent::__construct($resource);
    }

    public function user(User $user)
    {
        $this->user = $user;
        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return $this->collection->map(function (ContextWidgetResource $resource) use ($request) {
            return $resource->user($this->user)->toArray($request);
        })->all();
    }
}
