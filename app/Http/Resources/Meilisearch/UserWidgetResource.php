<?php

namespace App\Http\Resources\Meilisearch;

use App\Enums\Meilisearch\Filters\Categories;
use App\Enums\Meilisearch\Models\MeilisearchUserWidget;
use Illuminate\Http\Resources\Json\JsonResource;

class UserWidgetResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource[MeilisearchUserWidget::ID],
            'title' => $this->resource[MeilisearchUserWidget::TITLE],
            'key' => Categories::HIX_LOGIN
        ];
    }
}
