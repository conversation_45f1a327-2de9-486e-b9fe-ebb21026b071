<?php

namespace App\Http\Resources\ServiceTask\Api;

use App\ServiceTask;
use Illuminate\Http\Resources\Json\JsonResource;

/** @property ServiceTask $resource */
class TaskResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'external_id' => $this->resource->external_id,
            'type' => $this->resource->type,
            'year' => $this->resource->year,
            'date_start' => $this->resource->date_start?->isoFormat('YYYY-MM-DD'),
            'date_end' => $this->resource->date_end?->isoFormat('YYYY-MM-DD'),
            'created_at' => $this->resource->created_at->toDateTimeString(),
            'updated_at' => $this->resource->updated_at->toDateTimeString(),
            'title' => $this->resource->title,
            'subtitle' => $this->resource->subtitle,
            'status' => $this->resource->status,
        ];
    }
}
