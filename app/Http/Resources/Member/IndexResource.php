<?php

namespace App\Http\Resources\Member;

use App\User;

/**
 * Transform the resource into an array.
 * This resource contains the bare minimum of data required to display the list of users.
 * Be very careful with adding data here, some user lists are very long and will take a long time to load.
 * @property User $resource
 */
class IndexResource extends BaseResource
{
    public function toArray($request)
    {
        return array_merge(parent::toArray($request), [
            'managed_by_current_user' => auth()->user() && $this->resource->isManagedBy(auth()->user()),
        ]);
    }
}
