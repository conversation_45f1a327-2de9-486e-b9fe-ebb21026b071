<?php

namespace App\Http\Resources\Template;

use App\Http\VueRequests\OpenQuestions\Templates\AvailableTemplatesForCategoryRequest;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\OpenQuestions\Questions\Templates\TemplateEntry;
use App\Models\OpenQuestions\Questions\Templates\TemplateField;
use App\Models\OpenQuestions\Questions\Templates\TemplateFieldType;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Template $resource
 */
class AvailableTemplateResource extends JsonResource
{
    /**
     * @param AvailableTemplatesForCategoryRequest $request
     * @return array
     */
    public function toArray($request): array
    {
        $fields = $this->resource->fields
            ->where(TemplateField::PARENT_UUID, null)
            ->where(TemplateField::TYPE, '!=', TemplateFieldType::STATIC)
            ->sortBy(TemplateField::ORDER)
            ->values();

        $latestEntry = $this->resource->latestEntry($request->companyId());

        return [
            'id' => $this->resource->id,
            'title' => $this->getTitle($latestEntry),
            'intro_text' => $this->resource->intro_text,
            'category' => $this->resource->category,
            'fields' => AvailableFieldResource::customCollection($fields, $latestEntry)->toArray($request),

            // Data for the DropdownButton
            'value' => $this->resource->id,
            'name' => trans('template.add_template_label', [
                'name' => $this->resource->name,
            ]),
        ];
    }

    public function getTitle(?TemplateEntry $latestEntry): string
    {
        $useDefaultTitle = $this->resource->settings[Template::SETTING_USE_DEFAULT_TITLE] ?? false;
        $title = $this->resource->name;

        if (!$useDefaultTitle && $latestEntry) {
            $title = $latestEntry?->openQuestion?->title ?? $this->resource->name;
        }

        return $title;
    }
}
