<?php

namespace App\Http\Resources\Template;

use App\Http\VueRequests\OpenQuestions\Templates\AvailableTemplatesForCategoryRequest;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\OpenQuestions\Questions\Templates\TemplateAnswer;
use App\Models\OpenQuestions\Questions\Templates\TemplateEntry;
use App\Models\OpenQuestions\Questions\Templates\TemplateField;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

/**
 * @property TemplateField $resource
 */
class AvailableFieldResource extends FieldResource
{
    private static ?TemplateEntry $latestEntry;

    /**
     * @param AvailableTemplatesForCategoryRequest $request
     * @return array
     */
    public function toArray($request): array
    {
        $templateAnswers = self::$latestEntry?->templateAnswers ?? [];

        $fields = [
            'answer' => null,
            'checked' => false,
            'remark' => null,
            'attachments' => [],
        ];

        /** @var TemplateAnswer $templateAnswer */
        foreach ($templateAnswers as $templateAnswer) {
            if ($templateAnswer->title === $this->resource->title && $templateAnswer->type === $this->resource->type) {
                $fields['checked'] = true;
                $fields['remark'] = $templateAnswer->remark?->remark;
                $fields['answer'] = $templateAnswer->answer;
                $fields['attachments'] = $templateAnswer->attachments;
                break;
            }
        }
        if (empty($templateAnswers)) {
            $fields['checked'] = true;
        }

        $fields['name'] = $this->resource->schema_key;
        $fields['children'] = self::customCollection($this->resource->children, self::$latestEntry)->toArray($request);

        return array_merge(parent::toArray($request), $fields);
    }

    public static function customCollection($resource, ?TemplateEntry $latestEntry): AnonymousResourceCollection
    {
        self::$latestEntry = $latestEntry;
        return parent::collection($resource);
    }
}
