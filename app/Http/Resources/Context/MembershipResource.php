<?php

namespace App\Http\Resources\Context;

use App\Membership;
use Illuminate\Http\Resources\Json\JsonResource;

class MembershipResource extends JsonResource
{
    public function toArray($request): array
    {
        if ($this->type != Membership::TYPE_MANAGER && $this->isManagedBy($this->user, false)) {
            $this->type = 'implicit_manager';
        }

        return [
            'id' => $this->user->id,
            'auth_id' => $this->user->auth_id,
            'email' => $this->user->email,
            'name' => $this->user->showLabel(),
            'status' => $this->user->status,
            'type' => $this->type,
            'image' => $this->user->image,
        ];
    }
}
