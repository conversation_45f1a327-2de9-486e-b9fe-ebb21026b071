<?php

namespace App\Http\Resources\Account;

use App\Account;
use Illuminate\Http\Resources\Json\JsonResource;

class SettingsBrandingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var Account $this */
        return [
            'primary_color' => $this->primary_color->__toString(),
            'logo' => $this->getLogoUrl(),
            'background' => $this->getBackgroundUrl()
        ];
    }
}
