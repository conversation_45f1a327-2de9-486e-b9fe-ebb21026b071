<?php

namespace App\Http\Resources\Account;

use App\Account;
use App\Helpers\CertificateHelper;
use App\Services\Account\AccountService;
use App\Services\OpenQuestions\Templates\TemplateService;
use Config;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Account $resource
 */
class AccountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * Returns browser extension info when the user is also the same one as currently authenticated.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $isManagerOfOwnAccount = auth()->check() && auth()->user()->isManagerOfOwnAccount();
        $accountService = resolve(AccountService::class);

        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'hostname' => $this->resource->hostname,
            'uri' => $this->resource->uri,
            $this->mergeWhen(
                $isManagerOfOwnAccount,
                [
                    'debtor_number' => $this->resource->debtor_number,
                    'relation_manager' => $this->resource->relation_manager,
                    'sales_manager' => $this->resource->sales_manager,
                    'pipedrive_number' => $this->resource->pipedrive_number,
                    'feature_package' => $this->resource->feature_package,
                    'pricing_model' => $this->resource->pricing_model,
                    'cert_status' => CertificateHelper::status(Account::getHostAccount())
                ]
            ),
            'support_email' => $this->resource->support_email,
            'login_options' => $this->resource->login_options,
            'status' => $this->resource->status,
            'type' => $this->resource->type,
            'auth_methods' => $this->resource->auth_methods,
            'licenses' => $this->resource->licenses->map(
                function ($license) {
                    return $license->reference_name;
                }
            ),
            'language' => $this->resource->language,
            'languages' => $this->getLanguages(),
            'primary_color' => $this->resource->primary_color?->__toString(),
            'primary_color_contrast' => $this->resource->primary_color_contrast->__toString(),
            'logo_url' => $this->resource->getLogoUrl(),
            'background_url' => $this->resource->getBackgroundUrl(),
            'usage_types' => $this->resource->usage_types,
            'api_origins' => $this->resource->api_origins,
            'settings' => $this->resource->settings,
            'embed_settings' => $this->resource->embed_settings,
            'main_context_id' => $this->resource->mainContext->id,
            'default_login_enabled' => $this->resource->isDefaultLoginEnabled(),
            'login_autocomplete' => $this->resource->login_autocomplete,
            'showActivation' => $this->resource->showActivation(),
            'is_admin' => $this->resource->isAdminAccount(),
            'environment' => app()->environment(),
            'has_whatsapp_business' => $accountService->hasWhatsAppBusiness($this->resource),
            'enabled_services' => $accountService->enabledServiceList($this->resource),
            'favicon' => $this->resource->route('account.favicon'),
            'has_templates' => resolve(TemplateService::class)->hasTemplates($this->resource),
        ];
    }

    public function getLanguages(): array
    {
        return array_combine(
            Config::get('app.locales'),
            array_map(
                function ($v) {
                    return strtoupper($v) . ' ' . trans('languages.' . $v);
                },
                Config::get('app.locales')
            )
        );
    }
}
