<?php

namespace App\Http\Resources\Share;

use App\Http\Resources\JsonResourceWithoutWrapping;

class DownloadFileResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'url' => $this->resource->getDownloadUrl(),
            'extension' => $this->resource->extension,
            'className' => '' // needed for front end "clicked" indicator
        ];
    }
}
