<?php

namespace App\Http\Resources\AccountService\MicrosoftSharepoint;

use App\ValueObject\Services\Dms\MicrosoftSharepoint\Item;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class FolderResource
 *
 * @property Item $resource
 */
class FolderResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->resource->id(),
            'name' => $this->resource->name(),
        ];
    }
}
