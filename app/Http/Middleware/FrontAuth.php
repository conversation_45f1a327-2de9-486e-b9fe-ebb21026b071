<?php

namespace App\Http\Middleware;

use App\Account;
use App\Auth\SessionKey;
use App\Auth\SessionManager;
use App\License;
use App\User;
use Closure;
use Illuminate\Http\Request;
use Log;
use Psr\Container\ContainerExceptionInterface;
use Session;

class FrontAuth
{
    public const REASON_NO_FRONT_AUTH_TOKEN = 'no_front_auth_token';
    public const REASON_NO_ACTIVE_SESSION = 'no_active_session';
    public const REASON_ACCOUNT_NOT_THE_SAME = 'account_not_the_same';
    public const REASON_INACTIVE_USER = 'user_inactive';

    /**
     * Handle an incoming request.
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws ContainerExceptionInterface
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $account = Account::getHostAccount();
        if (empty($account)) {
            return response()->view('errors.not_found', [], 404);
        }

        SessionManager::useUrlParam($request);

        $user = auth()->user();

        if (!auth()->simpleCheck()) {
            return $this->noSession(self::REASON_NO_ACTIVE_SESSION);
        }

        // user is not in the host account
        if ($user->account_id !== $account->id) {
            return $this->noSession(self::REASON_ACCOUNT_NOT_THE_SAME);
        }

        // if the user is blocked or deleted while logged in
        if (!in_array($user->status, [User::STATUS_NEW, User::STATUS_ACTIVE, User::STATUS_VERIFIED])) {
            return $this->noSession(self::REASON_INACTIVE_USER);
        }

        // front auth token is required in session
        if (
            !(
                $account->hasLicense(License::NEW_HIX_UI)
                && $account->hasLicense(License::NEW_HIX_UI_CLIENT)
                && auth()->check()
                && $user->isExternal()
            )
            && !Session::has(SessionKey::FRONT_AUTH_TOKEN)
        ) {
            return $this->noSession(self::REASON_NO_FRONT_AUTH_TOKEN);
        }

        // Make sure the user's language is set for translations.
        $user->setLanguage();

        return $next($request);
    }

    /**
     * Redirect user to "no session" page when authentication fails.
     */
    private function noSession(string $reason)
    {
        Log::warning('Front auth returned no-session because of reason ' . $reason);

        if (auth()->simpleCheck()) {
            auth()->logout();
        }
        if (Session::has(SessionKey::FRONT_AUTH_TOKEN)) {
            Session::remove(SessionKey::FRONT_AUTH_TOKEN);
        }
        return response()->redirectToRoute('front.auth.no-session', [], 303);
    }
}
