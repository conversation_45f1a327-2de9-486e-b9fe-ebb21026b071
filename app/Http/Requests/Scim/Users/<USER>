<?php

namespace App\Http\Requests\Scim\Users;

use App\Http\Requests\Scim\AbstractQueryScimRequest;

class IndexUsersRequest extends AbstractQueryScimRequest
{
    public function rules(): array
    {
        return array_merge(
            ['filter' => 'nullable|string'],
            parent::rules()
        );
    }

    /**
     * Filter to find users.
     *
     * @return array|null
     */
    public function filter(): ?array
    {
        $filters = $this->get('filter');
        if ($filters === null) {
            return null;
        }

        // Split by eq.
        $filter = explode(' eq ', $filters);

        // Get only the last one.
        $value = str_replace('"', '', $filter[count($filter) - 1]);
        unset($filter[count($filter) - 1]);

        $name = AbstractScimUsersRequest::filterMap(implode(' eq ', $filter));

        if ($name === 'auth_id') {
            $value = strtolower($value);
        }

        return [$name => $value];
    }
}
