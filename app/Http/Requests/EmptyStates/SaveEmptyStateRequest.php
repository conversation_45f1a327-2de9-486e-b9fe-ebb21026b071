<?php

namespace App\Http\Requests\EmptyStates;

use App\Http\VueRequests\PostRequest;
use App\Translation;

class SaveEmptyStateRequest extends PostRequest
{
    protected $model_name = Translation::class;

    public function authorize(): bool
    {
        return $this->account()->isAdminAccount() && $this->user()->isAdminUser();
    }

    public function rules(): array
    {
        return [];
    }
}
