<?php

namespace App\Http\Requests\CustomRules;

use App\Http\VueRequests\GetRequest;
use App\Models\CustomRules\CustomRule;

/**
 * @property string $action
 */
class GetCustomRulesOptionRequest extends GetRequest
{
    protected $model_name = CustomRule::class;

    public function authorize(): bool
    {
        return $this->user()->canManageCompanies();
    }

    public function rules(): array
    {
        return [
            'action' => 'required|string|in:' . implode(',', CustomRule::ACTIONS),
        ];
    }
}
