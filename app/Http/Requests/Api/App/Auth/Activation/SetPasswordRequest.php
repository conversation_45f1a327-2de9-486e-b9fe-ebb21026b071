<?php

namespace App\Http\Requests\Api\App\Auth\Activation;

use App\Http\Requests\Token\AbstractTokenPostRequest;
use App\Token;

/**
 * @property string $token
 * @property string $auth_secret
 * @property string $auth_secret_verify
 */
class SetPasswordRequest extends AbstractTokenPostRequest
{
    public function rules(): array
    {
        return [
            'token' => 'required|string',
            'auth_secret' => 'required|strong_password|limit_password',
            'auth_secret_verify' => 'required|same:auth_secret',
        ];
    }

    public function messages(): array
    {
        return [
            'token.required' => $this->getCommonErrorTranslation(),
            'token.string' => $this->getCommonErrorTranslation(),
            'auth_secret.required' => trans('auth.activation.set_password.validation.auth_secret.required'),
            'auth_secret.string' => $this->getCommonErrorTranslation(),
            'auth_secret_verify.required' => trans(
                'auth.activation.set_password.validation.auth_secret_verify.required'
            ), //phpcs:ignore
            'auth_secret_verify.same' => trans('auth.activation.set_password.validation.auth_secret_verify.same'),
        ];
    }

    public function getTokenType(): string
    {
        return Token::TYPE_SETUP_USER_PASSWORD;
    }
}
