<?php

namespace App\Http\Requests\Caseware;

use App\Exceptions\Api\PreconditionFailedException;
use App\Factories\TaskFileFactory;
use App\Models\Sbr\SbrRecipient;
use App\Models\TaskFile;
use App\Models\TaskFile\Xbrl;
use App\Models\TaskFile\Xml;
use App\Repositories\Services\SbrRecipientRepository;
use Throwable;

/**
 * @property int $recipient_id
 * @property string $recipient_name
 */
class SbrSupplyRequest extends LogiusSupplyRequest
{
    private const ALLOWED_FILE_TYPES = [
        TaskFile::TYPE_SBR_YEARWORK,
        TaskFile::TYPE_AUDIT_REPORT,
        TaskFile::TYPE_SIGNATURE_XML,
    ];

    public function rules(): array
    {
        $rules = [
            'recipient_id' => 'required_without:recipient_name|exists:sbr_recipients,id',
            'recipient_name' => 'required_without:recipient_id|exists:sbr_recipients,name',
            // Overwrite the parent value of 'files.*.type'
            'files.*.type' => 'required|string|in:declaration,' . implode(',', self::ALLOWED_FILE_TYPES),
        ];

        // Be sure to not switch the array_merge values around, so the parent gets overwritten by the child.
        return array_merge(parent::rules(), $rules);
    }

    public function messages(): array
    {
        $messages = [
            'recipient_id.required_without' => 'Attribute recipient_id required without recipient_name',
            'recipient_name.required_without' => 'Attribute recipient_name required without recipient_id',
            'recipient_id.exists' => 'Recipient not found',
            'recipient_name.exists' => 'Recipient not found',
            // Overwrite the parent value of 'files.*.type'
            'files.*.type.in' => 'The type of file should be one of the following: ' . implode(', ', self::ALLOWED_FILE_TYPES), //phpcs:ignore
        ];

        // Be sure to not switch the array_merge values around, so the parent gets overwritten by the child.
        return array_merge(parent::messages(), $messages);
    }

    public function sbrYearwork(): Xbrl
    {
        foreach ($this->input('files') as $file) {
            $type = $file['type'];
            if ($type === TaskFile::TYPE_SBR_YEARWORK) {
                return TaskFileFactory::create(
                    $this->getAccountService(),
                    base64_decode($file['content']),
                    $file['filename'],
                    $type
                );
            }
        }
        throw new PreconditionFailedException('Could not find sbr_yearwork');
    }

    public function auditReport(): ?Xbrl
    {
        foreach ($this->input('files') as $file) {
            $type = $file['type'];
            if ($type === TaskFile::TYPE_AUDIT_REPORT) {
                return TaskFileFactory::create(
                    $this->getAccountService(),
                    base64_decode($file['content']),
                    $file['filename'],
                    $type
                );
            }
        }
        return null;
    }

    public function signature(): ?Xml
    {
        foreach ($this->input('files') as $file) {
            $type = $file['type'];
            if ($type === TaskFile::TYPE_SIGNATURE_XML) {
                return TaskFileFactory::create(
                    $this->getAccountService(),
                    base64_decode($file['content']),
                    $file['filename'],
                    $type
                );
            }
        }
        return null;
    }

    public function sbrRecipient(): SbrRecipient
    {
        try {
            $sbrRecipientRepository = resolve(SbrRecipientRepository::class);
            if ($this->recipient_id) {
                return $sbrRecipientRepository->getById($this->recipient_id);
            } else {
                // According to the rules there should be a recipient_name if the id is not supplied.
                return $sbrRecipientRepository->getByNameAndCategory(
                    $this->recipient_name,
                    SbrRecipient::CATEGORY_YEARWORK
                );
            }
        } catch (Throwable) {
            if ($this->recipient_id) {
                throw new PreconditionFailedException(
                    'Could not find SBR recipient by ID: ' . $this->recipient_id
                ); //phpcs:ignore
            } else {
                throw new PreconditionFailedException(
                    'Could not find SBR recipient by name: ' . $this->recipient_name
                ); //phpcs:ignore
            }
        }
    }
}
