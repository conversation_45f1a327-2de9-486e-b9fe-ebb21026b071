<?php

namespace App\Http\Requests\AccountService\Dms\MicrosoftSharepoint;

use App\Http\VueRequests\Request;

/**
 * Class GetConfigurationsForServiceTaskRequest.
 *
 * @property string $drive_id
 * @property string $item_id
 * @property string $service_task_id
 */
class GetConfigurationsForServiceTaskRequest extends Request
{
    public function authorize(): bool
    {
        return $this->user() !== null;
    }

    public function rules(): array
    {
        return [
            'service_task_id' => 'required|int|min:0',
        ];
    }
}
