<?php

namespace App\Console\Commands\User;

use App\User;
use Illuminate\Console\Command;
use Storage;
use Throwable;

class ApplySignaturesToInternalUsers extends Command
{
    protected $signature = 'user:apply-signatures {account_id} {user_id} {also_external?}';
    protected $description = 'Apply profile signature to internal users or also external';

    public function handle(): int
    {
        $userId = $this->argument('user_id');
        $accountId = $this->argument('account_id');

        // If also_external is set to specifically "1" it will also include external users in the command.

        $alsoExternal = (
            $this->argument('also_external') === "1"
            && $this->confirm('Are you sure you want to also include external users?')
        );

        $appliedUser = User::findOrFail($userId);

        if ($appliedUser->account_id != $accountId) {
            throw new \UnexpectedValueException("Account id and User account id don't match");
        }

        $appliedUserFilePath = 'Account/' . $accountId . '/user/' . $appliedUser->id . '/signature';

        if (Storage::exists($appliedUserFilePath)) {
            $query = User::where(User::ACCOUNT_ID, $accountId)
                ->where(User::ID, '!=', $userId);

            // If the default is false it will only allow internal users (is_external = 0)
            if (!$alsoExternal) {
                $query = $query->where(User::IS_EXTERNAL, 0);
            }

            $users = $query->cursor();

            foreach ($users as $user) {
                try {
                    $filePath = 'Account/' . $accountId . '/user/' . $user->id . '/signature';
                    Storage::copy($appliedUserFilePath, $filePath);
                } catch (Throwable $e) {
                    $this->error($e::class . ' - ' . $e->getMessage());
                }
            }
        } else {
            $this->error('could not find signature in: ' . $appliedUserFilePath);
            return self::FAILURE;
        }

        return self::SUCCESS;
    }
}
