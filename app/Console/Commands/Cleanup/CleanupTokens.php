<?php

namespace App\Console\Commands\Cleanup;

use App\Console\Command;
use App\Repositories\TokenRepository;
use DB;

class CleanupTokens extends Command
{
    protected $signature = 'cleanup:tokens {--force}';
    protected $description = 'Clean the tokens table';

    private TokenRepository $tokenRepository;

    public function __construct(TokenRepository $tokenRepository)
    {
        $this->tokenRepository = $tokenRepository;
        parent::__construct();
    }

    public function handle(): void
    {
        try {
            DB::beginTransaction();
            //Delete tokens expired or soft deleted more than 7 days ago.
            // Using a plain SQL query because it allows us to delete large amount of rows quickly with low memory usage
            $c = $this->tokenRepository->cleanup();

            if ($c == 0) {
                $this->info('No tokens found to delete');
                DB::commit();
            } else {
                $this->info('Found ' . $c . ' tokens to delete.');
                if ($this->option('force') || $this->confirm('Commit these changes?', false)) {
                    DB::commit();
                    $this->info('Deleted ' . $c . ' tokens');
                } else {
                    DB::rollBack();
                    $this->info('Changes reverted.');
                }
            }
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error($e->getMessage());
            throw $e;
        }
    }
}
