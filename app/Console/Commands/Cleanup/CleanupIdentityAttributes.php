<?php

namespace App\Console\Commands\Cleanup;

use App\Console\Command;
use DB;

class CleanupIdentityAttributes extends Command
{
    protected $signature = 'cleanup:identity_attributes {--force} {days=7}';
    protected $description = 'Remove soft deleted identity attributes';

    public function handle()
    {
        $days = (int)$this->argument('days');

        if (!is_int($days) || $days < 7) {
            $this->error('Days must be equal or bigger than 7');
            return ;
        }

        try {
            DB::beginTransaction();
            //Delete identity attributes soft deleted more than 7 days ago.
            // Using a plain SQL query because it allows us to delete large amount of rows quickly with low memory usage
            $c = DB:: affectingStatement(
                'DELETE from identity_attributes WHERE deleted_at < now() - interval :days DAY',
                ['days' => $days]
            );
            if ($c == 0) {
                $this->info('No identity attributes found to delete');
                DB::commit();
            } else {
                $this->info('Found ' . $c . ' identity attributes to delete.');
                if ($this->option('force') || $this->confirm('Commit these changes?', false)) {
                    DB::commit();
                    $this->info('Deleted ' . $c . ' identity attributes');
                } else {
                    DB::rollBack();
                    $this->info('Changes reverted.');
                }
            }
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error($e->getMessage());
            throw $e;
        }
    }
}
