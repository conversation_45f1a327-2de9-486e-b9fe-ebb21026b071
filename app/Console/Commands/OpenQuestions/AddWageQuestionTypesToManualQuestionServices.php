<?php

namespace App\Console\Commands\OpenQuestions;

use App\AccountService;
use App\Models\OpenQuestions\OpenQuestionType;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\Wage;
use App\Service;
use App\Services\OpenQuestions\OpenQuestionTypeService;
use Illuminate\Console\Command;

class AddWageQuestionTypesToManualQuestionServices extends Command
{
    protected $signature = 'open-questions:add-wage-types';

    protected $description = 'Add default types for wages to manual open question services';

    public function handle()
    {
        $openQuestionTypeService = resolve(OpenQuestionTypeService::class);

        /** @var Service $service */
        $service = Service::query()->where(Service::REFERENCE_NAME, Service::MANUAL_QUESTIONS_SERVICE)->first();
        $accountServices = AccountService::query()->where(AccountService::SERVICE_ID, $service->id)->cursor();
        try {
            \DB::beginTransaction();
            /** @var AccountService $service */
            foreach ($accountServices as $service) {
                if (!empty($service->account)) {
                    $openQuestionTypeService->createForService(
                        $service,
                        [
                            OpenQuestionType::SUBJECT_COMPANY => Wage::QUESTION_TYPES_COMPANY,
                            OpenQuestionType::SUBJECT_EMPLOYEE => Wage::QUESTION_TYPES_EMPLOYEE
                        ]
                    );
                }
            }
            \DB::commit();
        } catch (\Exception $e) {
            \DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
