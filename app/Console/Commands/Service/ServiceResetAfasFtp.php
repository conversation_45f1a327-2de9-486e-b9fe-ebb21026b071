<?php

namespace App\Console\Commands\Service;

use Config;
use App\Helpers\PureFtpdHelper;
use App\AccountService;
use App\Console\CommandTrait;
use App\Service;
use Illuminate\Console\Command;

class ServiceResetAfasFtp extends Command
{
    use CommandTrait;

    protected $signature = 'service:reset-afas-ftp';
    protected $description = 'Check AFAS account services and write credentials to PureFTP file.';

    public function handle()
    {
        $id = Service::where('reference_name', 'afas_tasks')->pluck('id');
        $services = AccountService::where('service_id', $id)->get();

        PureFtpdHelper::removeAll();
        $this->info('Cleared users file ' . Config::get('pureftpd.users_file_path'));
        foreach ($services as $service) {
            if (!$service->isEnabled()) {
                $this->warn('account service #' . $service->id . ' is not enabled.');
                continue;
            }
            $provider = $service->getProvider();
            if (isset($service->properties['afas_settings'])) {
                $settings = $service->properties['afas_settings'];
                PureFtpdHelper::addUser(
                    $settings['username'],
                    $settings['password'],
                    $provider->getDirectoryPath(),
                    false
                );
                $this->info('Added user ' . $settings['username'] . ' for account service #' . $service->id);
            } else {
                $this->warn('account service #' . $service->id . ' does not have afas_settings in properties');
            }
        }
        PureFtpdHelper::commit();
        $this->info('Commited users file.');
    }
}
