<?php

namespace App\Console\Commands\Service;

use App\Console\Command;
use App\Services\Gateway\Service\CanObserveTaskStatus;
use App\ServiceTask;

class ServiceStatusUpdate extends Command
{
    protected $signature = 'service:task-status-update {id}';
    protected $description = 'Notify task provider service that task status has been updated';

    public function handle(): int
    {
        $id = $this->argument('id');
        $task = ServiceTask::findOrFail($id);

        $provider = $task->accountService->getProvider();

        if (!$provider instanceof CanObserveTaskStatus) {
            $this->error($provider::class . ' does not implement CanObserveTaskStatus');
            return static::FAILURE;
        }

        $success = $provider->handleTaskStatusUpdate($task, $task->status, $task->status);
        if ($success) {
            $this->info('Notified service # ' . $task->account_service_id . ' that task #' . $id . ' has status ' . $task->status); // phpcs:ignore
            return static::SUCCESS;
        }

        return static::FAILURE;
    }
}
