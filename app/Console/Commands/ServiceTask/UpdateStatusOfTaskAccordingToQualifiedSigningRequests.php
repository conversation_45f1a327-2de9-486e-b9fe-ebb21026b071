<?php

namespace App\Console\Commands\ServiceTask;

use App\ServiceTask;
use Illuminate\Console\Command;

class UpdateStatusOfTaskAccordingToQualifiedSigningRequests extends Command
{
    protected $signature = 'service-task:fix-status-qualified-signing';
    protected $description = 'Will set the correct tasks from pending_to_sign to pending_qualified_signing';
    public function handle(): int
    {
        foreach (ServiceTask::query()->where('status', 'pending_to_sign')->cursor() as $task) {
            /** @var ServiceTask $task */
            if ($task->signingRequest()->where('signed', 0)->exists()) {
                $this->info('Task #' . $task->id . ' updated from pending_to_sign to pending_qualified_signing');
                $task->status = 'pending_qualified_signing';
                $task->save();
            } else {
                // Check if there are any placeholders that need signing for colleagues
                // If we do we should set the signing_round for the task to 1
                $hasInternalSignRequests = false;
                foreach ($task->placeholders as $placeholder) {
                    if (!empty($placeholder->filledBy)) {
                        if ($placeholder->filledBy->isInternal() && $placeholder->status === 'open') {
                            $hasInternalSignRequests = true;
                            break;
                        }
                    } else {
                        $this->warn('Placeholder #' . $placeholder->id . ' is sent but does not have a user');
                    }
                }
                if ($hasInternalSignRequests) {
                    $this->info('Task #' . $task->id . ' changed signing_round to 1 because it still has open placeholders'); // phpcs:ignore

                    $task->signing_round = 1;
                    $task->save();
                }
            }
        }
        return self::SUCCESS;
    }
}
