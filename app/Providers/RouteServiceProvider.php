<?php

namespace App\Providers;

use App\Account;
use App\AccountService;
use App\AccountWidget;
use App\Consumer;
use App\Context;
use App\ContextWidget;
use App\Exceptions\NotFoundException;
use App\GenericService;
use App\GenericWidget;
use App\Http\Controllers\Front\AuthController;
use App\Membership;
use App\Models\Notification;
use App\Service;
use App\Services\TokenService;
use App\ServiceTask;
use App\Token;
use App\User;
use App\UserWidget;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to the controller routes in your routes file.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot(): void
    {
        Route::model('account', Account::class);
        Route::model('user', User::class);
        Route::model('context', Context::class);
        Route::model('widget', GenericWidget::class);
        Route::model('generic_widget', GenericWidget::class);
        Route::model('account_widget', AccountWidget::class);
        Route::model('context_widget', ContextWidget::class);
        Route::model('user_widget', UserWidget::class);
        Route::model('membership', Membership::class);
        Route::model('notification', Notification::class);
        Route::model('consumer', Consumer::class);
        Route::model('token_model', Token::class, function (string $token) {
            $account = Account::getHostAccount();
            if ($account === null) {
                throw new NotFoundException('Account not found');
            }

            $token = resolve(TokenService::class)->getToken(token: $token, account_id: $account->id);
            if ($token === null) {
                if (str_starts_with($this->current()->getName(), 'front.')) {
                    AuthController::echoMessage($account, 'invalid-token');
                }
                throw new ModelNotFoundException('Could not find token with token: ' . $token . ' in Account ' . $account->id); // phpcs:ignore
            }

            return $token;
        });
        Route::model('generic_service', GenericService::class);
        Route::model('account_service', AccountService::class);
        Route::model('service', Service::class);
        Route::model('service_task', ServiceTask::class);

        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map(): void
    {
        $this->mapWebRoutes();
        $this->mapApiRoutes();
        $this->mapGuiRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * These Routes are used by the application outside any GUI or API specific routes.
     *
     * @return void
     */
    protected function mapWebRoutes(): void
    {
        Route::middleware(['web', 'session'])
            ->namespace($this->namespace)
            ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "gui" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapGuiRoutes(): void
    {
        Route::middleware(['web', 'session', 'gui'])
            ->namespace($this->namespace)
            ->group(base_path('routes/gui.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * @return void
     */
    protected function mapApiRoutes(): void
    {
        Route::namespace($this->namespace)
            ->prefix('api')
            ->group(base_path('routes/api.php'));
    }
}
