<?php

namespace App;

use Database\Factories\Widget\GenericWidgetGenericWidgetCategoryFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Class GenericWidgetCategory
 * @package App
 * @property string $category
 */
class GenericWidgetGenericWidgetCategory extends Model
{
    protected $table = 'generic_widget_generic_widget_category';
    public $timestamps = false;

    public const GENERIC_WIDGET_CATEGORY_ID = 'generic_widget_category_id';
    public const GENERIC_WIDGET_ID = 'generic_widget_id';

    protected $fillable = [
        self::GENERIC_WIDGET_CATEGORY_ID,
        self::GENERIC_WIDGET_ID
    ];

    protected static function newFactory(): GenericWidgetGenericWidgetCategoryFactory
    {
        return GenericWidgetGenericWidgetCategoryFactory::new();
    }
}
