<?php

namespace App\Search;

use App\Enums\Meilisearch\Models\MeilisearchUserWidget;
use App\User;

class UserWidgetSearchHandler extends SearchHandler
{
    public static string $index = MeilisearchUserWidget::INDEX;

    protected static function setUserAccessFilter(User $user, array &$filters): void
    {
        $filters[] = "user_id = $user->id";
    }

    public static function isAllowed(User $user): bool
    {
        return $user->isActive();
    }
}
