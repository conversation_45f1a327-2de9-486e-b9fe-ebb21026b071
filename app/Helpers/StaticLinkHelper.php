<?php

namespace App\Helpers;

use App\Account;
use App\AccountWidget;
use App\Events\StaticLinkErrorEvent;
use App\Exceptions\Api\NotFoundException;
use App\Exceptions\StaticLink\Exception;
use App\Logging\Channels\ServiceLog;
use App\User;
use App\UserWidget;
use App\Widget;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;

class StaticLinkHelper
{
    /**
     * @param AccountWidget $accountWidget
     * @param User $user
     * @return UserWidget|Response
     */
    public static function createUserWidget(AccountWidget $accountWidget, User $user)
    {
        $contextWidgets = self::getContextWidgetsForUserOfAccountWidget($accountWidget, $user);

        if ($contextWidgets->isEmpty()) {
            if ($accountWidget->context->hasMembership($user)) {
                return $accountWidget->createUserWidget($user);
            }
            self::error(trans('static_link.errors.no_access'), $accountWidget);
        } else {
            if ($contextWidgets->count() == 1) {
                return $contextWidgets->first()->createUserWidget($user);
            } else {
                if ($contextWidgets->count() > 1) {
                    self::error(trans('static_link.errors.multiple_context_widgets'), $accountWidget);
                }
            }
        }

        self::error(trans('static_link.errors.cannot_create_user_widget'), $accountWidget);
    }

    /**
     * @param AccountWidget $accountWidget
     * @param User $user
     * @return Collection|Response
     */
    protected static function getContextWidgetsForUserOfAccountWidget(AccountWidget $accountWidget, User $user)
    {
        if (is_null($accountWidget)) {
            self::error(trans('static_link.errors.invalid_external_start_key'));
        } else {
            return $accountWidget
                ->getDescendents()
                ->whereIn('context_id', $user->contexts()->pluck('id'))
                ->filter(
                    function ($contextWidget) use ($user) {
                        return $contextWidget->context->hasMembership($user);
                    }
                );
        }
    }

    /**
     * @param int $id
     * @param User $user
     * @return UserWidget
     * @throws NotFoundException
     */
    public static function getUserWidgetWithID(int $id, User $user): UserWidget
    {
        $userWidget = $user->userWidgets()->where('id', $id)->get()->first();
        if (!$userWidget) {
            throw new NotFoundException('The requested user widget with ID "' . $id . '" does not exist.');
        }

        return $userWidget;
    }

    /**
     * This method handles both the old, generated external key and id as a reference to find user widgets.
     * The main reason for this is, we have users using the legacy generated external key.
     *
     * @param $key
     * @param User $user
     * @return UserWidget|Response
     * @throws NotFoundException
     */
    public static function getUserWidgetWithKey($key, User $user)
    {
        if (is_numeric($key)) {
            try {
                return StaticLinkHelper::getUserWidgetWithID((int)$key, $user);
            } catch (NotFoundException $e) {
                ServiceLog::notice(
                    'StaticLinkHelper tried to get user widget with ID "' . $key . '",
                    now checking if "' . $key . '" is an external key'
                );
            }
        }

        $account_widget = StaticLinkHelper::getAccountWidgetWithKey($key, $user->account);

        if (empty($account_widget)) {
            throw new NotFoundException('The requested widget does not exist.');
        }

        $userWidgets = $user->userWidgets()->whereIn(
            'parent_id',
            self::getContextWidgetsForUserOfAccountWidget(
                $account_widget,
                $user
            )->push($account_widget)->pluck('id')
        )->get();

        if ($userWidgets->count() > 1) {
            self::error(trans('static_link.errors.multiple_user_widgets'), $account_widget);
        } else {
            return $userWidgets->first();
        }
    }

    /**
     * @param string $key
     * @param Account $account
     * @return Widget|null
     * @throws Exception
     */
    public static function getAccountWidgetWithKey(string $key, Account $account): ?Widget
    {
        $account_widget = $account->accountWidgets()->where('external_start_key', $key)->where(
            'allow_external_start',
            true
        )->first();

        if (!$account_widget) {
            self::error(trans('static_link.errors.invalid_external_start_url'));
        }
        return $account_widget;
    }

    /**
     * @param array|string $errors
     * @param Widget|null $widget
     * @return RedirectResponse
     */
    public static function error($errors, Widget $widget = null)
    {
        if (is_array($errors)) {
            $errors = implode(',', $errors);
        }

        StaticLinkErrorEvent::fire($widget, $errors);

        throw new Exception($errors);
    }
}
