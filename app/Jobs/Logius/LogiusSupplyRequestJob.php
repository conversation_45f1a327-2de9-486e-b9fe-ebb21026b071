<?php

namespace App\Jobs\Logius;

use App\Factories\Services\TaskSenderFactory;
use App\Helpers\TaskAuditLogHelper;
use App\Jobs\LogiusJob;
use App\Logging\Channels\ServiceLog;
use App\LogiusRequest;
use App\Services\Gateway\Logius\LogiusSupplyResponseParser;
use App\Services\LogiusRequestService;
use App\Services\ServiceTask\TaskGenerator;
use App\Support\Database\Builder;
use DB;
use Throwable;

/**
 * Class SupplyDeclarationRequestJob
 * @package App\Jobs\Logius
 *
 * This job is put a the Horizon queue, connects to Logius and submits an XBRL/XML file in a signed soap request.
 */
class LogiusSupplyRequestJob extends LogiusJob
{
    protected int $taskId;
    protected int $requestId;
    protected bool $forced;

    public function __construct(int $taskId, int $requestId, bool $force = false)
    {
        $this->taskId = $taskId;
        $this->requestId = $requestId;
        $this->forced = $force;
        Builder::clearCache();
    }

    public function tags(): array
    {
        return ['supplyDeclarationRequest', 'logius', 'logius:' . $this->requestId, 'task:' . $this->taskId];
    }

    /**
     * @throws Throwable
     */
    public function handle(TaskGenerator $taskGenerator)
    {
        $logiusRequestService = resolve(LogiusRequestService::class);

        $logiusRequest = LogiusRequest::findOrFail($this->requestId);
        ServiceLog::debug('Preparing to submit LogiusRequest #' . $logiusRequest->id . ' for task #' . $logiusRequest->service_task_id); //phpcs:ignore
        $task = $logiusRequest->task;
        $sender = null;
        try {
            DB::beginTransaction();
            // TRUE if summary of task, file and source data all match.
            // This ensures that we send the same file as the user approved and
            // that there is no newer version at the provider.
            $match = true;
            if ($task->shouldRefresh()) {
                try {
                    $declarationData = $taskGenerator->getFreshTaskData($task);
                    if ($declarationData) {
                        $match = $task->summarySameAs($declarationData->summary());
                    } else {
                        ServiceLog::error('Source of task #' . $task->id . ' did not return data.');
                        $match = false;
                    }
                } catch (Throwable $e) {
                    ServiceLog::error(
                        'Error fetching data for task #' . $task->id . ' with exception ' . $e::class . ': ' . $e->getMessage() //phpcs:ignore
                    );
                    $match = false; // We treat this as hash_mismatch
                }
            }

            $sender = TaskSenderFactory::create($task);
            if ($match && is_bool(TaskSenderFactory::$summaryDataMatch)) {
                $match = TaskSenderFactory::$summaryDataMatch;
            }

            if ($match || $this->forced) {
                $sender->doRequest();
                $logiusRequest->startPolling($sender->getMessageID());
                TaskAuditLogHelper::taskSentToRequestingParty($task);
                ServiceLog::debug('Submitted LogiusRequest #' . $logiusRequest->id . ' for task #' . $logiusRequest->service_task_id); //phpcs:ignore
            } else {
                ServiceLog::warning('Deleting LogiusRequest #' . $logiusRequest->id . ' for task #' . $logiusRequest->service_task_id . ' because the source seems to have changed.'); //phpcs:ignore
                $task->refresh();
                $task->setHashMismatch(true);
                $logiusRequest->delete();
            }

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();
            $error = 'Failed to submit LogiusRequest #' . $logiusRequest->id . ': ' . $e->getMessage();
            if (isset($sender)) {
                $error .= ' - Response: ' . $sender->soapClient->__getLastResponse();
                $error .= ' (Certificate: ' . $sender->soapClient->getCertificatePath() . ')';
                $soapClient = $sender->soapClient;
                $response = $soapClient->__getLastResponse();

                if (isset($response)) {
                    $logiusRequestService->onError($logiusRequest, $response);
                }

                if ($e instanceof \SoapFault) {
                    if ($sender->response !== null) {
                        $parser = new LogiusSupplyResponseParser($sender->response);
                        if ($parser->hasFault()) {
                            $logiusRequestService->storeFaultDetails($logiusRequest, $parser->getFaultDetails());
                        }
                    }

                    if ($logiusRequest->readyToGiveUp()) {
                        $logiusRequestService->finalizeSupplyError($logiusRequest);
                    }
                }
            }
            ServiceLog::error($error);

            throw $e;
        }
    }
}
