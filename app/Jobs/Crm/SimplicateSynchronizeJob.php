<?php

namespace App\Jobs\Crm;

use App\AccountService;
use App\Enums\QueueName;
use App\Jobs\Job;
use App\Services\Service\Crm\SimplicateCrmService;

class SimplicateSynchronizeJob extends Job
{
    public $queue = QueueName::SIMPLICATE;

    public function __construct(
        protected AccountService $accountService,
        protected bool $fullSync,
        protected bool $forceUpdate = false
    ) {
    }

    public function tags(): array
    {
        return ['simplicate', 'crm', 'simplicateService:' . $this->accountService->id];
    }

    public function handle()
    {
        /** @var SimplicateCrmService $provider */
        $provider = $this->accountService->getProvider();
        $provider->forceUpdate = $this->forceUpdate;
        $provider->sync($this->fullSync);
        $this->accountService->updateLastRetrieval();
    }
}
