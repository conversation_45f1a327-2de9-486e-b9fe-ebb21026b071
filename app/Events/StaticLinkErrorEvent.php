<?php

namespace App\Events;

use App\Widget;

class StaticLinkErrorEvent extends Event
{
    public function handle(Widget $widget = null, $errors = [])
    {
        if (!empty($widget)) {
            $this->registerObject($widget);
        }
        $this->registerRelatedUser(auth()->user());
        $this->setVisibleForUser();

        $this->setMessage('An error occurred starting a static link');
        $this->registerData(explode(',', $errors));

        $this->registerSubject(auth()->user());

        $this->register();
    }
}
