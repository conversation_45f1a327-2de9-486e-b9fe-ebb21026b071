<?php

namespace App\Events\User;

use App\Events\Event;
use App\User;

class WidgetsEncryptedWithMasterkey extends Event
{
    public function handle(User $user): void
    {
        $this->registerRelatedAccount($user->account);
        $this->registerRelatedUser($user);
        $this->setVisibleForUser();
        $this->setObjectContent($user->userWidgets()->count());
        $this->register();
    }
}
