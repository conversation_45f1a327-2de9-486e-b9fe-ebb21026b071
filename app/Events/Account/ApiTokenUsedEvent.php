<?php

namespace App\Events\Account;

use App\Account;
use App\Events\Event;
use Laravel\Sanctum\PersonalAccessToken;

class ApiTokenUsedEvent extends Event
{
    public function handle(Account $account, PersonalAccessToken $token): void
    {
        $this->registerRelatedAccount($account);
        $this->registerObject($token);
        $this->setMessage('Account #' . $account->id . ' accessed api using PersonalToken #' . $token->id); //phpcs:ignore
        $this->register();
    }
}
