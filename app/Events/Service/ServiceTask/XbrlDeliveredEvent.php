<?php

namespace App\Events\Service\ServiceTask;

use App\Events\Event;
use App\ServiceTask;

class XbrlDeliveredEvent extends Event
{
    public function handle(ServiceTask $task)
    {
        $this->registerObject($task);
        $this->registerRelatedCompany($task->company);
        $this->registerRelatedTask($task);
        $this->setVisibleForUser();
        $this->setObjectLabel($task->title . ' - ' . $task->subtitle);

        $this->setMessage('Delivered XBRL for document #' . $task->refresh_id);

        $this->register();
    }
}
