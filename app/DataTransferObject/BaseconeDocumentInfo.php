<?php

namespace App\DataTransferObject;

class BaseconeDocumentInfo
{
    public ?int $status = null; // HTTP status code
    public ?string $publicUrl = null; // Absolute URL to public Basecone document
    public ?string $name = null; // Name returned byu Basecone API
    public ?string $createdOn = null; // Created date returned by Basecone API

    public function toArray(): array
    {
        return [
               'name' => $this->name,
               'createdOn' => $this->createdOn,
               'publicUrl' => $this->publicUrl
        ];
    }
}
