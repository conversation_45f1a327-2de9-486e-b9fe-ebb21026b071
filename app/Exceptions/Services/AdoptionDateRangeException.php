<?php

namespace App\Exceptions\Services;

use App\Exceptions\BadRequestException;

/**
 * This exception is thrown when a valid date is supplied as adoption date for teh annual report of a yearwork task
 * and the date is out of range:
 * - The date is before the end date of the reporting period.
 * - The date is before the preparation date.
 * - The date is later than today (in the future)
 * Class AdoptionDateRangeException
 * @package App\Exceptions\Services
 */
class AdoptionDateRangeException extends BadRequestException
{
    public function getLocalizedTitle(): string
    {
        return trans('common.400_invalid_adoption_date');
    }
}
