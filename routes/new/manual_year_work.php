<?php

use App\Http\Controllers\Service\Declarations\ManualYearWorkServiceController;

Route::prefix('manual_year_work')->group(function () {
    Route::post(
        'publication_document_information',
        [ManualYearWorkServiceController::class, 'getPublicationDocumentInformation']
    )->name('manual_year_work.get_publication_document_information');

    Route::post(
        'create_task_micro_small_companies',
        [ManualYearWorkServiceController::class, 'createTaskForMicroAndSmallCompanies']
    )->name('manual_year_work.create_task_micro_small_companies');

    Route::post(
        'create_task_medium_companies',
        [ManualYearWorkServiceController::class, 'createTaskForMediumCompanies']
    )->name('manual_year_work.create_task_medium_companies');

    Route::post('set_signing_user', [ManualYearWorkServiceController::class, 'setYearworkTaskSigningUser'])
        ->name('new.manual_year_work.set_signing_user');

    Route::post('create_task_sbr', [ManualYearWorkServiceController::class, 'createTaskForSbr'])
        ->name('new.manual_year_work.create_task_sbr');
});
