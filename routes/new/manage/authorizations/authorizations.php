<?php

use App\Http\Controllers\Service\Authorizations\LogiusAuthorizationController;

Route::prefix('authorizations')->group(function () {
    Route::prefix('status_overview')->group(function () {
        Route::get('filters', [LogiusAuthorizationController::class, 'managerFilters'])
            ->name('new.manage.authorizations.status_overview.filters');

        Route::get('index', [LogiusAuthorizationController::class, 'managerFilteredAuthorizations'])
            ->name('new.manage.authorizations.status_overview.index');

        Route::get('csvExport', [LogiusAuthorizationController::class, 'managerFilteredAuthorizationsCsvExport'])
            ->name('new.manage.authorizations.status_overview.csv-export');
    });
});
