{"$schema": "https://json-schema.org/draft/2019-09/schema", "$defs": {"screen": {"$anchor": "ScreenSchema", "properties": {"sidebar": {"type": "object", "properties": {"fixed": {"type": "boolean"}}, "additionalProperties": false}, "tabs": {"type": "array", "items": {"$ref": "#TabSchema"}}}, "additionalProperties": false}, "tab": {"$anchor": "TabSchema", "type": "object", "properties": {"id": {"type": "string", "pattern": "^[a-z0-9-]+$"}, "key": {"type": "string"}, "title": {"type": "string"}, "columns": {"type": "array", "items": {"$ref": "#ColumnSchema", "minItems": 1, "maxItems": 100}}, "sort": {"$ref": "#SortSchema"}, "filters": {"type": "array", "items": {"$ref": "#FilterSchema", "maxItems": 100}}, "pagination": {"$ref": "#PaginationSchema"}, "edit": {"type": "boolean"}}, "additionalProperties": false}, "column": {"$anchor": "ColumnSchema", "type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-z-_]{1,100}$"}, "label": {"type": ["string", "null"]}, "width": {"type": ["string", "null"]}, "checked": {"type": "boolean"}}, "additionalProperties": false}, "pagination": {"$anchor": "PaginationSchema", "type": "object", "properties": {"limit": {"type": "integer", "minimum": 1, "maximum": 1000, "pattern": "^[0-9]{1,4}$"}}, "additionalProperties": false}, "sort": {"$anchor": "SortSchema", "type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-zA-Z-_]{1,100}$"}, "direction": {"type": "string", "enum": ["asc", "desc", "custom"]}}, "additionalProperties": false}, "filter": {"$anchor": "FilterSchema", "type": "object", "properties": {"key": {"type": "string", "pattern": "^\\d{1,2}(-\\d{1,2})?$"}, "checked": {"type": "boolean"}, "partialChecked": {"type": "boolean"}, "data": {"type": "string"}, "field": {"type": ["string", "null"]}}, "additionalProperties": false}}, "type": "object", "propertyNames": {"pattern": "^[a-z-]{1,100}$"}, "patternProperties": {".*": {"$ref": "#ScreenSchema"}}, "minProperties": 1}