{"urlFilter": [{"hostSuffix": "start.exactonline.nl"}], "selectors": {"iframe": {"selector": "iframe#MainWindow", "title": ".HdrTitle .HdrTitle", "allowed_sources": {"general_ledger_table": "/docs/FinTransactions.aspx?Mode=1", "sale_outstanding_items_table": "/docs/FinARAPOutstanding.aspx", "purchase_general_ledger_table": "/docs/FinARAPOutstanding.aspx?AP=1"}, "allowed_sources_new": {"general_ledger_table": ["/docs/FinTransactions.aspx", "/docs/Fintransactions.aspx", "/docs/FinBalanceSheet.aspx", "/docs/SysSearch.aspx", "WebSite.aspx?Webpage=MenuFinance"], "sale_outstanding_items_table": ["/docs/FinARAPOutstanding.aspx?_"], "purchase_general_ledger_table": ["/docs/FinARAPOutstanding.aspx?AP=1"], "cfl_statements_table": ["/docs/CflStatements.aspx", "/docs/CflStatementsToBeCompleted.aspx"], "sales_ageing_analysis_table": ["/docs/FinARAPAging.aspx"]}, "administration_id": {"regex": "[^=]*$"}, "administration_names": ["#AdminName"], "general_ledger_table": {"headers_nl": {"question_name": "<PERSON><PERSON><PERSON>", "description": "Omschrijving", "credit_amount": "Credit", "debit_amount": "Debet", "bookkeeping_number": "Bkst.nr.", "transaction_date": "Datum"}, "headers_en": {"question_name": "Account", "description": "Description", "credit_amount": "Credit", "debit_amount": "Debit", "bookkeeping_number": "Entry no.", "transaction_date": "Date"}, "required_columns_nl": ["Datum", "Bkst.nr.", "Debet", "Credit", "Omschrijving", "<PERSON><PERSON><PERSON>"], "required_columns_en": ["Date", "Entry no.", "Debit", "Credit", "Description", "Account"], "selector": "iframe#MainWindow table#List", "classes_to_ignore": ["Total", "SectionHeader"], "name": {"selector": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(8)", "regex": "[^-]*$"}, "amount": {"selector1": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(9)", "selector2": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(10)"}, "booking_number": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(5)", "bookkeeping_number": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(5)", "description": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(6)", "transaction_date": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(4)"}, "sale_outstanding_items_table": {"selector": "iframe#MainWindow table#Details_lv", "headers_nl": {"description": "Omschrijving", "amount": "Te vorderen", "bookkeeping_number": "<PERSON><PERSON>", "transaction_date": "Datum", "invoice_number": "<PERSON><PERSON><PERSON><PERSON>", "currency": "Valuta"}, "headers_en": {"description": "Description", "amount": "A/R", "bookkeeping_number": "Entry", "transaction_date": "Date", "invoice_number": "Invoice", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "required_columns_nl": ["Omschrijving", "Te vorderen", "<PERSON><PERSON>", "Datum", "<PERSON><PERSON><PERSON><PERSON>"], "required_columns_en": ["Description", "A/R", "Entry", "Date", "Invoice"], "classes_to_ignore": ["DataOutline0", "SectionHeader"], "attributes_class": "DataDark", "name": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:).DataOutline2 td:nth-child(1)", "amount": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(11)", "booking_number": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(3)", "bookkeeping_number": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(3)", "invoice_number": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(1)", "currency": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(10)", "description": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(6)", "transaction_date": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(2)", "name_relation_sort": "iframe#MainWindow table.tab tr #Selector_tabs_Details\\3AtGroupedByName"}, "purchase_general_ledger_table": {"selector": "iframe#MainWindow table#Details_lv", "headers_nl": {"description": "Omschrijving", "amount": ["<PERSON>len (V.V.)", "Te betalen"], "bookkeeping_number": "<PERSON><PERSON>", "transaction_date": "Datum", "invoice_number": "<PERSON><PERSON><PERSON><PERSON>", "currency": "Valuta"}, "headers_en": {"description": "Description", "amount": ["A/P (FC)", "A/P"], "bookkeeping_number": "Entry", "transaction_date": "Date", "invoice_number": "Invoice", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "required_columns_nl": ["Omschrijving", ["<PERSON>len (V.V.)", "Te betalen"], "<PERSON><PERSON>", "Datum", "<PERSON><PERSON><PERSON><PERSON>"], "required_columns_en": ["Description", ["A/P (FC)", "A/P"], "Entry", "Date", "Invoice"], "classes_to_ignore": ["DataOutline0", "SectionHeader"], "name": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:).DataOutline2 td:nth-child(1)", "amount": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(11)", "booking_number": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(3)", "bookkeeping_number": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(3)", "invoice_number": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(1)", "currency": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(9)", "description": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(6)", "transaction_date": "iframe#MainWindow table#Details_lv tr:nth-child(:replace:) td:nth-child(2)", "name_relation_sort": "iframe#MainWindow table.tab tr #Selector_tabs_Details\\3AtGroupedByName"}, "cfl_statements_table": {"selector": "iframe#MainWindow table#List", "headers_nl": {"payment_reference": "Betalingsreferentie", "description": "Omschrijving", "currency": "Val.", "bookkeeping_number": "Boekstuknummer", "transaction_date": "Datum", "amount": "Bedrag (Incl. btw)"}, "headers_en": {"payment_reference": "Payment reference", "description": "Description", "currency": "<PERSON><PERSON><PERSON><PERSON>", "bookkeeping_number": "Entry number", "transaction_date": "Date", "amount": "Amount (Incl. VAT)"}, "required_columns_nl": ["Datum", "Bankrekening", "Boekstuknummer", "Omschrijving", "Bedrag (Incl. btw)"], "required_columns_en": ["Date", "Bank account", "Entry number", "Description", "Amount (Incl. VAT)"], "classes_to_ignore": ["DataOutline0", "DataOutline1", "SectionHeader"], "amount": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(11)", "bookkeeping_number": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(3)", "currency": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(9)", "description": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(6)", "transaction_date": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(2)", "payment_reference": "iframe#MainWindow table#List tr:nth-child(:replace:) td:nth-child(6)", "gesplitst_row_type_1": {"amount": "td:nth-child(9)", "bookkeeping_number": "td:nth-child(10)", "currency": "td:nth-child(8)", "description": "td:nth-child(7)"}, "gesplitst_row_type_2": {"amount": "td:nth-child(7)", "bookkeeping_number": "td:nth-child(8)", "currency": "td:nth-child(6)", "description": "td:nth-child(5)"}, "gesplitst_row_type_3": {"amount": "td:nth-child(8)", "bookkeeping_number": "td:nth-child(9)", "currency": "td:nth-child(7)", "description": "td:nth-child(6)"}, "minimum_amount_columns": 10}, "sales_ageing_analysis_table": {"selector": "iframe#MainWindow table#List_lv", "headers_nl": {"amount": "Openstaand", "transaction_date": "Datum", "invoice_number": "<PERSON><PERSON><PERSON><PERSON>"}, "headers_en": {"amount": "Outstanding", "transaction_date": "Date", "invoice_number": "Invoice"}, "required_columns_nl": ["Openstaand", "Datum", "<PERSON><PERSON><PERSON><PERSON>"], "required_columns_en": ["Outstanding", "Date", "Invoice"], "classes_to_ignore": ["DataOutline0", "SectionHeader"], "name": "iframe#MainWindow table#List_lv tr:nth-child(:replace:).DataOutline2 td:nth-child(1)", "amount": "iframe#MainWindow table#List_lv tr:nth-child(:replace:) td:nth-child(11)", "invoice_number": "iframe#MainWindow table#List_lv tr:nth-child(:replace:) td:nth-child(1)", "currency": "iframe#MainWindow table#List_lv tr:nth-child(:replace:) td:nth-child(9)", "transaction_date": "iframe#MainWindow table#List_lv tr:nth-child(:replace:) td:nth-child(2)", "name_relation_sort": "iframe#MainWindow table.tab tr #Selector_tabs_List\\3AtDetailsByName"}}}}