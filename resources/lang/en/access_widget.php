<?php

return [
    'database' => 'Database',
    'username' => 'Username',
    'email' => 'Email',
    'email_as_username' => 'Email',
    'employer_number' => 'Employer Number',
    'sub_username' => 'Subusername',
    'password' => 'Password',
    'pincode' => 'Pincode',
    'officecode' => 'Office code',
    'office' => 'Office',
    'client' => 'Client ID',
    'organisation' => 'Organisation',
    'access_code' => 'Access code',
    'relation_id' => 'Relation ID',
    'agreement_nr' => 'Agreement number',
    'domain' => 'Domain name or login URL',
    'company_id' => 'Company ID',
    'company' => 'Company',
    'company_code' => 'Company code',
    'domain_+' => 'Login URL',
    'subdomain' => 'Subdomain',
    'domain_ip' => 'Domain (e.g. xyz.com) or IP (e.g. ***********)',
    'domain_ip_ports' => 'Domain (e.g. xyz.com) or IP (e.g. ***********:1234)',
    'ip_address' => 'IP (e.g. ***********)',
    'environment' => 'Environment',
    'environment_number' => 'Environment number',
    'realm' => 'Environment',
    'redirect' => 'Redirect path',
    'api_user_key' => 'API key',
    'hostname' => 'Hostname (e.g. xyz.comandi.nl)',
    'application_id' => 'Application identifier',
    'url' => 'URL (e.g. https://www.nu.nl), Please start with "https://"',
    'application_token' => 'Application token',
    'public_user_token' => 'Public user token',
    'private_user_token' => 'Private user token',
    'video_code' => 'Enter a YouTube or Vimeo URL',
    'video_id' => 'Enter the videocode of a Wistia video',
    'height_in_px' => 'Height in pixels, default value is 525px',
    'embed_code' => 'Embed code',
    'feed_uri' => 'Enter the url of a RSS, Atom or DC feed',
    'list_size' => 'Enter how many messages will be shown',
    'may_not_work' => 'Unfortunately there are known issues with this integration, so this widget may not work correctly. Together with this party we are working on a solution.',
    'field_domain_optional_note' => 'If you don’t have your own domain, you can leave this field empty..',
    'field_use_advanced_api' => "Use advanced connection",
    'field_use_advanced_api_note' => "The advanced connection offers more control regarding the security of the online access. It needs to be configured before this option can be used.",
    'field_environment_twinfield_note' => '<br><div class="bgm-orange c-black" style="padding: 3px;"><b style="font-size: 14px">Pay attention!</b><br>Follow the steps on <a href="https://support.hellohix.com/hc/nl/articles/4496720049053-Twinfield-SecureLogin-Single-Sign-on-EN-">https://support.hellohix.com/hc/nl/articles/4496720049053-Twinfield-SecureLogin-Single-Sign-on-EN-</a> to setup Twinfield via SecureLogin </div>',
    'field_fiscaalgemak_portal' => 'Select your portal',
    'field_fiscaalgemak_new_login_app' => 'Select your environment',
    'field_fiscaalgemak_new_login_app_note' => 'If you have to select your app on the Fiscaal Gemak login page, please select your desired app here.',
    'user_type' => 'User type',
    'field_comandi_apptoken_note' => 'Can be requested at Comandi',
    'field_redirect_note' => 'Please choose your langing page (redirect)',
    'field_totp_secret_verification_code_is' => 'The code below can be used to complete the authenticator setup.',
    'fb_page_url' => 'Facebook page url e.g. https://facebook.com/yourpage',
    'member_id' => 'Member ID',
    'generate_key' => 'Press `Generate code` for a new code',
    'generate_button' => 'Generate code',
    'activation_code' => 'Activation code',
    'totp_secret' => 'Authenticator secret',
    'totp_secret_optional' => 'Authenticator secret (optional)',
    'login_url' => 'Login URL (e.g. https://your.application.com/Login), Please start with "https://"',
    'language' => 'Language',
    'unsafe_login_url' => 'Login URL (e.g. https://start.exactonline.com/docs/Login.aspx), Please start with "https://"',
    'custom_link' => 'Login URL (e.g. https://start.exactonline.com/docs/Login.aspx), Please start with "https://"',
    'password_to_clipboard' => 'Password',
    'relation_number' => 'Relation number',
    'relation_policy_number' => 'Relation or Policy number',
    'workplace' => 'Workplace',
    'code' => 'Code',
    'url_no_example' => 'URL',
    'vat_number' => 'VAT number',
    'vat_number_optional' => 'VAT number (optional)',
    'api_key' => 'API key',
    'portal' => 'Portal',
    'appoint_number' => 'Appointment number',
    'identifier' => 'ID',
    'store_address' => 'Store address',
    'partner_code' => 'Partner code',
    'user_widget' => 'Select your widget',
    'number' => 'Number',
    'private_window' => 'Open in a private window',
    'account' => 'Account',
    'client_id' => 'Client ID',
    'client_name' => 'Client name',
    'year' => 'Year',
    'quarter' => 'Quarter',
    'office_code' => 'Office code',
    'license_number' => 'License number',
    'ip_whitelisting' => 'Ip whitelisting',
    'case_number' => 'Case number',
    'postal_code' => 'Postal Code',
    'customer_number' => 'Customer number',
    'security_code' => 'Security code',
    'administration' => 'Administration',
    'client_code' => 'Client code',
    'sub_number' => 'Sub number',
    'site' => 'Site',
    'use_microsoft' => 'Use Microsoft for login',
    'use_azure' => 'Use Azure for login',
    'contract_code' => 'Contract code',
    'login_method' => 'Login method',
    'login_with' => 'Log in with',
    'user_id' => 'User ID'
];
