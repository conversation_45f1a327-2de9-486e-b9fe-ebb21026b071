<?php

return [
	'delete_identity_attribute_confirmation' => 'Are you sure you want to unlink the external user :identity_attribute?',
	'no_identity_attributes_found' => 'This user has no linked external users yet.',
	'delete_identity_attribute_success' => 'External connection with this user has successfully been removed!',
	'identity_attribute_already_exists' => 'This user was already linked. Please login again to start using you account.',
  'delete_identity_attribute_error_no_other_login_methods' => 'This link could not be removed because this is the only authentication method for this user.',
  'edit_title' => 'Edit external user',
  'update_success' => 'External user was successfully changed',
  'nothing_changed' => 'Nothing changed',
  'delete' => [
    'delete_identity_attribute_error_no_other_login_methods' => 'This link could not be removed because this is the only authentication method for this user.',
    'succeed' => 'External connection with this user has successfully been removed!'
  ]
];
