<?php

return [
    'action' => [
        'start_user_widget' => 'Widget started (:object_label)',
        'login' => 'Logged in',
        'logout' => 'Logged out',
        'user_security_changed' => 'Changed security settings',
        'user_activated' => 'Activated',
        'user_activation_requested' => 'Activation requested',
        'user_reactivation_requested' => 'Reactivation requested',
        'request_declined' => 'Reactivation declined',
        'request_approved' => 'Reactivation approved',
        'request_created' => 'Reactivation without masterkey requested',
        'user_reactivated' => 'Reactivated',
        'login_attempt' => 'Failed login attempt',
        'unknown' => 'Unknown action',
        'membership_created' => 'Membership added (:object_label)',
        'membership_deleted' => 'Membership removed (:object_label)',
        'membership_changed' => 'Membership changed (:object_label)',
        'identity_attribute_deleted' => 'External user (:object_label) unlinked',
        'authentication_recovery_requested' => 'Authentication recovery link requested',
        'user_blocked' => 'Blocked',
        'user_unblocked' => 'Unblocked',
        'user_secret_changed' => 'Password changed',
        'started_static_link' => 'Static link started (:object_label)',
        'static_link_error' => 'Static link error occurred',
        'created' => 'Created (:object_label)',
        'changed' => 'Changed (:object_label)',
        'deleted' => 'Deleted (:object_label)',
        'context_created' => 'Created',
        'context_deleted' => 'Deleted',
        'context_widget_created' => 'Group widget created (:object_label)',
        'context_widget_deleted' => 'Group widget deleted (:object_label)',
        'context_widget_properties_changed' => 'Group widget changed (:object_label)',
        'context_security_settings_changed' => 'Security settings changed',
        'user_added' => 'User added: :object_label',
        'user_removed' => 'User removed: :object_label',
        'reopened' => 'Reopened (:object_label)',
        'sent' => 'Sent to :object_label',
        'approved' => 'Approved',
        'declined' => 'Declined',
        'completed' => 'Completed',
        'link_opened' => 'Link opened',
        'link_requested' => 'Link requested',
        'sms_link_requested' => 'Sms link requested',
        'service_connected' => 'Connected service with a company',
        'service_disconnected' => 'Disconnected service from a company',
        'account_service_connected' => 'Connected service with company :object_content',
        'account_service_disconnected' => 'Disconnected service from a company :object_content',
        'account_service_enabled' => 'Enabled service: :object_label',
        'account_service_disabled' => 'Disabled service: :object_label',
        'account_service_created' => 'Created service: :object_label',
        'account_service_preferences_updated' => 'Service preferences updated',
        'account_service_logius_preference_updated' => 'Belastingdienst connection changed',
        'account_service_connected_all_companies' => 'Connected service to all companies',
        'fetched_xbrl' => 'Declaration retrieved to submit to tax authority',
        'forced_approved' => 'Forced approval of :object_label',
        'revoked' => 'Revoked :object_label',
        'task_file_fetched' => 'Fetched declaration data for document :object_label',
        'xbrl_delivered' => 'Delivered XBRL for document :object_label',
        'xbrl_error' => 'XBRL error status received for document :object_label',
        'restored' => 'Restored :object_label',
        'archived' => 'Archived :object_label',
        'send_otp' => '2FA SMS sent to number ending in :object_label',
        'company_identifier_created' => ':object_content :object_label added',
        'company_identifier_deleted' => ':object_content :object_label removed',
        'account_service_sync' => 'Service synchronised',
        'logius_authorization_request' => 'Logius authorization requested for :object_label',
        'logius_authorization_retract' => 'Logius authorization retracted for :object_label',
        'logius_authorization_activate' => 'Logius authorization activated for :object_label',
        'logius_authorization_condition' => 'Logius authorization condition updated for :object_label',
        'qualified_signing_user_add' => 'Added as qualified signing user',
        'qualified_signing_user_delete' => 'Removed as qualified signing user',
        'widgets_encrypted_with_masterkey' => ':object_content widgets encrypted with new master key'
    ],
    'filters' => [
        'widget_started' => 'Only widgets started',
        'login' => 'Only logins',
        'logout' => 'Only logouts',
        'loginAttempt' => 'Only login attempts',
        'security_setting_changed' => 'Only changed security settings',
        'user_activated' => 'Only (re)activations',
        'user_activation_requested' => 'Only activation requests',
        'user_reactivation_requested' => 'Only reactivations requests',
        'membership_created' => 'Only added memberships',
        'membership_deleted' => 'Only deleted memberships',
        'membership_changed' => 'Only changed memberships',
        'identity_attribute_deleted' => 'Only unlinked external users',
        'authentication_recovery_requested' => 'Only authentication recovery requests',
        'user_blocked' => 'Only blocking users',
        'user_unblocked' => 'Only unblocking users',
        'user_secret_changed' => 'Only password changed',
    ],
    'created_at_title' => 'Date/Time',
    'action_title' => 'Event',
    'agent_type_title' => 'Device',
    'agent_name_title' => 'Browser',
    'ip_address_title' => 'IP-address',
    'no_events_found' => 'There have been no events for the past month.',
    'event_note' => 'The events above are from the last 30 days',
];
