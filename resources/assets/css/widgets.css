.widget {
  min-height:auto;
}

.dashboard-widget {
  margin-bottom: 30px;
}

#access-widget-grid{
  z-index:8;
  transition: all 250ms;
  margin-bottom:30px;
}

#communication-widget-grid{
  z-index:6;
}

.communication-widget .list-group{
  margin-bottom: 0;
}

.access-widget, .communication-widget{
  border-radius: 2px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
}

.access-widget.is-dragging{
  box-shadow: 0 1px 40px rgba(0, 0, 0, 0.15);
}

.placeholder .card{
  background: #00ABEF;
  opacity: 0.8;
}

#access-widget-grid .widget-sizer,
.access-widget { width: calc(25% - 20px); margin:0 10px 20px;}

@media screen and (max-width: 991px) {
  #access-widget-grid .widget-sizer,
  .access-widget { width: calc(33.333333% - 20px); margin:0 10px 20px;}
}

@media screen and (max-width: 600px) {
  #access-widget-grid .widget-sizer,
  .access-widget { width: calc(50% - 20px); margin:0 10px 20px;}
}

@media screen and (max-width: 480px) {
  #access-widget-grid .widget-sizer,
  .access-widget { width: calc(100% - 20px); margin:0 10px 20px;}
}

#communication-widget-grid .widget-sizer,
.communication-widget { width: calc(33.33333% - 20px); margin:0 10px 20px;}

@media screen and (max-width: 991px) {
  #communication-widget-grid .widget-sizer,
  .communication-widget { width: calc(50% - 20px); margin:0 10px 20px;}
}

@media screen and (max-width: 600px) {
  #communication-widget-grid .widget-sizer,
  .communication-widget {  width: calc(100% - 20px); margin:0 10px 20px;}
}

.access-widget, .communication-widget{
  opacity: 0;
  transition: opacity 250ms;
}

.access-widget .card{
  padding:25px 0;
}

.access-widget table{
  table-layout:fixed!important;
  width:100%!important;
}

.access-widget .start-button{
  margin: 0 10px;
  width:30px;
  height:30px;
  border-radius:50%;
  line-height:10px !important;
  padding-left:10px !important;
}

.access-widget .widget-image{
  text-align: center;
  padding-right:7px;
}

.access-widget .widget-image img{
  max-height: 30px;
  max-width: 30px;
  vertical-align: middle;
}

.access-widget .widget-description{
  padding-right:10px;
}

.access-widget .widget-description h2{
  margin:5px 0 0;
  font-size:15px;
}

.access-widget .actions{
  position:absolute;
  right:3px;
  top:3px;
}

.access-widget .actions.actions-alt{
  right: 25px;
}

.is-pointer-down {
  cursor: grabbing;
  cursor: -moz-grabbing;
  cursor: -webkit-grabbing;
}

/** Widget Filter **/
.widget-filters {
  width: 100%;
  padding:0 0 40px 0;
  text-align:right;
}

.widget-filters .toolbar-action {
  position: relative;
  display: inline-block;
  background: transparent;
  min-height: 20px;
  -webkit-transition: width 150ms;
  transition: width 150ms;
  border-radius:3px;
}

.widget-filters .toolbar-action input,
.widget-filters .toolbar-action input:focus {
  width:100%;
  padding:1px 0 1px 25px !important;
  border:0;
  background:transparent;
}

.widget-filters .toolbar-action {
  padding:10px;
}

.widget-filters .toolbar-action-search {
  background:#fff;
  width: 200px;
}

.widget-filters .toolbar-action-search i {
  position: absolute;
  left: 10px;
  top: 7px;
  font-size: 24px;
}

.widget-filters .toolbar-action-add {
  margin-left: 0;
  margin-right: 10px;
}

.widget-filters .toolbar-action-add:hover {
  cursor: pointer;
}

#dashboard-add-widget-button .plus {
  width: 32px;
  cursor: pointer;
  transition: all 0.3s ease 0s;
  height: 32px;
  border-radius: 50%;
  display: flex;
  position: relative;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.12);
}
#dashboard-add-widget-button .plus__line {
  width: 2px;
  height: 16px;
  border-radius: 10px;
  position: absolute;
  left: calc(50% - 1px);
  top: calc(50% - 8px);
}
#dashboard-add-widget-button .plus__line--h {
  transform: rotate(90deg);
}
#dashboard-add-widget-button .plus__line--v {
  display: flex;
  align-items: center;
  justify-content: space-around;
  overflow: hidden;
  transition: all 0.4s ease 0s;
}

#dashboard-searchbar {
  display: flex;
  justify-content: flex-end;
  align-items: center
}

#dashboard-searchbar .toolbar-action-search {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.12);
  cursor: text;
}