<template>
    <div class="app-intro-panel" v-if="canShowAppIntro">
        <div class="description columns content-columns">
            <div class="column is-half left-column ">
                <img v-if="this.add_button_descr && this.add_button_descr.length > 0" src="/images/intro-panel/left-arrow.svg" alt="click + on the left"/>
                <div v-if="this.add_button_descr && this.add_button_descr.length > 0" class="description-content"
                     v-html="add_button_descr">
                </div>
            </div>
            <div class="column is-half has-text-centered description-video ">
                <iframe v-if="this.video_url && this.video_url.length > 0" title="vimeo-player" :src="video_url"
                        width="640" frameborder="0" allowfullscreen></iframe>
                <div class="description-video-caption" v-html="video_caption"></div>
            </div>
        </div>
    </div>
</template>

<script>

import {mapGetters, mapMutations, mapState, mapActions} from 'vuex';
import store from '../store/index';

export default {
    store: store,
    name: 'AppIntroPanel',
    components: {},
    computed: {
        canShowAppIntro() {
            if (this.type === 'service') {
                return this.user && this.user.can.is_manager;
            }
            return true;
        },
        ...mapState({
            user: state => state.user.profile,
        }),
    },
    data() {
        return {
            type: null,
            add_button_descr: null,
            video_url: null,
            video_caption: null
        };
    },
    mounted() {
        this.getData();
    },
    created() {
        if (!this.type) {
            this.determineType();
        }
    },
    methods: {
        getData() {
            let url = '/manage/empty_state/type/' + this.type;
            this.axios.get(url).then((response) => {
                let data = response.data;
                this.add_button_descr = data.add_button_descr ? data.add_button_descr : '';
                this.video_url = data.video_url ? data.video_url : '';
                this.video_caption = data.video_caption ? data.video_caption : '';
            });
        },
        determineType() {
            if (this.$route.path.endsWith('services/intro')) {
                this.type = 'service';
            } else if (this.$route.path.endsWith('companies/intro')) {
                this.type = 'company';
            } else if (this.$route.path.endsWith('templates/intro')) {
                this.type = 'template';
            } else if (this.$route.path.endsWith('widgets/intro')) {
                this.type = 'widget';
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../sass/initial-variables';

.app-intro-panel {
    font-size: 18px;
    position: absolute;
    height: 100vh;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    /* webpackIgnore: true */
    background-image: url('/images/intro-panel/pen-and-pencil.svg'), url('/images/intro-panel/background-white.png');
    background-position: right 100px bottom 60px, left top;
    background-repeat: no-repeat, repeat;
    background-size: 600px auto, auto;

    .description {
        margin-top: 137px;
        color: darkgray;
        min-height: 500px;
        max-width: 1400px;

        img {
            height: 100px;
        }

        .left-column {
            padding-left: 5px;
        }

        .description-content {
            margin-left: 150px;
        }

        iframe {
            height: 100%
        }
    }
}
</style>