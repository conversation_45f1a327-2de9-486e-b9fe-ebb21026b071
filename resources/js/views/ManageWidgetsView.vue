<template>
    <div class="app-main-panel" :class="{'new-ui-menu': account.licenses.includes('new_ui_2024')}">
        <ListColumn
            :title="title"
            :add-route="newWidgetRoute"
            :add-button-title="this.$t('widget.add_new_widget')"
            :items="items"
            :selected-item="widget"
            :search="search"
            @searchChange="searchChange"
            @select-item="selectWidget"
        />
        <div class="content-container">
            <Loader :is-loading="loading" :text="this.$t('common.loading')">
                <RouterView v-slot="{ Component }" key="widgets_view" @widget-created="selectWidget"/>
            </Loader>
        </div>
    </div>
</template>

<script>
    import ListColumn from '../components/ListColumn';
    import store from '../store/index';
    import Loader from "../components/Loader";
    import {mapState, mapActions} from "vuex";

    export default {
        store: store,
        name: 'ManageWidgetsView',
        components: {
            ListColumn,
            Loader
        },
        computed: {
            ...mapState({
                loading: state => state.widgets.loading,
                items: state => state.widgets.all,
                isMobile: state => state.isMobile,
                account: state => state.account.current_account
            }),
            newWidgetRoute() {
                return this.$router.resolve({name: 'add-account-widgets'}).path;
            }
        },
        data() {
            return {
                title: this.$t('menu.widgets'),
                selectedId: null,
                widget: null,
                toDashboard: false,
                search: '',
                active: true
            };
        },
        methods: {
            ...mapActions({
                selectById: 'widgets/selectById',
                loadData: 'widgets/loadData'
            }),
            selectWidget(widget, initial) {
                if (!initial) {
                    return this.selectWidgetById(widget && widget.id, 'ListColumn');
                }
            },
            selectWidgetById(id, source) {
                let all = this.items;
                if (!all || all.length === 0) {
                    return;
                }

                let widget = all.find(w => String(w.id) === String(id)) || all[0];

                if(!widget) {
                    return;
                }

                this.selectedId = widget.id;
                this.widget = widget;

                let name = 'widget-overview';

                if(this.$route.name == 'widgets' || this.$route.name === 'widget-overview' || this.$route.name === 'edit-account-widget' || this.$route.name === 'add-account-widgets') {
                    //replace the route when we are just selecting the first item in the list.
                    this.$router.replace({name: name, params: {account_widget_id: this.selectedId}});
                } else if(this.$route.name == 'context-widget-edit') {
                    this.$router.push({name: name, params: {account_widget_id: this.selectedId}});
                }
                this.selectById(this.selectedId);
            },
            clearSelection() {
                this.widget = null;
            },
            selectFirstItem() {
                this.selectWidgetById(this.items[0].id, 'selectFirstItem');
            },
            async searchChange(search) {
                this.search = search;
            },
        },
        async created() {
            if(!this.selectedId && this.$route.params.account_widget_id) {
                this.selectedId = this.$route.params.account_widget_id;
            }
            return await this.loadData('/account_widget/index').then(() => {
                if (this.items.length === 0) {
                    this.$router.replace({name: 'widgets-intro'});
                } else {
                    this.selectWidgetById(this.selectedId, 'created');
                }
            });
        },
        watch: {
            '$route.name'() {
                if(this.$route.name == 'add-account-widgets') {
                    this.clearSelection();
                }
            },
        },
        beforeRouteEnter(to, from, next) {
            next(vm => {
                if (!vm._inactive) {
                    if(to.hash === 'dashboard'){
                        vm.toDashboard = true;
                    }
                    if(typeof to.params.account_widget_id !== 'undefined') {
                        vm.selectWidgetById(to.params.account_widget_id, 'beforeRouteEnter');
                    }
                }
            });
        },

        beforeRouteUpdate(to, from, next) {
            next(vm => {
                if (!vm._inactive) {
                    if(typeof to.params.account_widget_id !== 'undefined') {
                        vm.selectWidgetById(to.params.account_widget_id, 'beforeRouteUpdate');
                    }
                }
            });
        },
        activated() {
            this.active = true;
        },
        deactivated() {
            this.active = false;
        }
    };
</script>

<style lang="scss" scoped>
    @import '../../sass/initial-variables';

    table.group-information {
        td.key {
            width: 110px;
            min-width: 110px;
            padding-right: 10px;
        }

        tbody tr:first-child td {
            padding-top: 20px;
        }
    }

    table.plain {
        border-collapse: collapse;
        margin-bottom: 40px;
        width: 100%;

        thead td {
            position: relative;
            border-bottom: solid 1px #d8d8d8;
            height: 40px;

            h3 {
                font-size: 14px;
                font-weight: bold;
            }

            .table-actions {
                position: absolute;
                top: 0px;
                right: 0px;
                text-align: right;
                font-size: 14px;
            }
        }

        td:hover {
            .hover-hint {
                opacity: 0.5;
                font-size: 10px;
            }
        }

        .hover-hint {
            opacity: 0;
        }

    }
</style>
