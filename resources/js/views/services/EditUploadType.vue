<template>
    <div class="edit-upload-type-panel content-panel">
        <div class="inner-content-panel">
            <ContentHeader
                :title="uploadType.name"
                :subtitle="$t('service.upload_types.subtitle')"
                :showBack="true"
                :sendBackEvent="true"
                @back="sendBack"
                image="/images/services/manual_task.png"
            />
            
            <form class="inner-edit-panel" method="post" @submit="save">

                <div class="introduction">
                    {{ $t('service.upload_types.fields.intro') }}
                </div>

                <div class="edit-fields-table">
                    <span class="header-title">{{$t('service.upload_types.fields.title')}}</span>
                    <span class="header-title">{{$t('service.upload_types.fields.required')}}</span>
                    <div class="centered-icon">
                        <i
                            class="has-text-info sl-icon-plus icon text-align-right"
                            @click="addField"
                            :title="$t('common.add')"
                            v-show="showAddButton"
                        />
                    </div>
                    <template v-for="(field, i) in uploadType.fields">
                        <div>
                            <Input v-model:value="field.title" :maxlength="60" required="required" />
                        </div>
                        <div class="required-checkbox">
                            <Checkbox tab-index="-1" class="checkbox-select" :checked="field.required" :name="i" @updateCheck="handleCheckRequired"/>
                        </div>
                        <div class="actions">
                            <i class="has-text-danger sl-icon-trash-can icon" @click="remove(i)" :title="$t('common.delete')"/>
                        </div>
                    </template>
                </div>
                <div v-show="uploadType.fields.length === 0" class="empty-message">
                    {{$t('service.upload_types.fields.empty')}}
                </div>
                <div class="button-container">
                    <button type="submit" class="button is-info">{{ $t('common.save') }}</button>
                    <a class="button is-text" @click="sendBack">{{ $t('common.cancel') }}</a>
                </div>
            </form>
        </div>
    </div>
</template>

<script>

import ContentHeader from "../../components/ContentHeader.vue";
import UploadTypeApi from "../../api/services/UploadTypeApi.js";
import Checkbox from "../../components/Checkbox";
import Input from "../../components/Forms/Input";
import {mapGetters} from 'vuex';

export default {
    name: 'EditUploadType',
    components: {
        Checkbox,
        ContentHeader,
        Input
    },
    data() {
        return {
            uploadType: {
                title: '',
                fields: []
            }
        }
    },
    computed: {
        ...mapGetters({
            accountService: 'services/selected'
        }),
        showAddButton() {
            return this.uploadType.fields.length < 6;
        }
    },
    methods: {
        sendBack() {
            this.$router.push({name: 'service-edit', params: {account_service_id: this.accountService.id}});
        },
        loadUploadType(id) {
            UploadTypeApi.get(id).then((data) => {
                this.uploadType = data;
            });
        },
        handleCheckRequired(i, val) {
            this.uploadType.fields[i].required = val;
        },
        addField() {
            let field = {
                uuid: null,
                title: '',
                required: false,
                position: this.uploadType.fields.length + 1
            };
            this.uploadType.fields.push(field);
        },
        remove(i) {
            this.uploadType.fields.splice(i, 1);
            this.applyFieldPositions();
        },
        applyFieldPositions() {
            for(let i = 0; i < this.uploadType.fields.length; i++) {
                this.uploadType.fields[i].position = i + 1;
            }
        },
        save(e) {
            e.preventDefault();
            UploadTypeApi.saveFields(this.uploadType).then((data) => {
                this.sendBack();
            });
        }
    },
    created() {
        if (this.$route.params.upload_type_id && !isNaN(this.$route.params.upload_type_id)) {
            let id = parseInt(this.$route.params.upload_type_id);
            this.loadUploadType(id);
        }
    }
}

</script>

<style scoped lang="scss">
@import '../../../sass/initial-variables';

.edit-upload-type-panel {
    max-width: 850px;

    .introduction {
        background: #f5f6f5;
        border-radius: 4px;
        padding: 20px;
        margin: 20px 0;
    }

    .edit-fields-table {
        display: grid;
        align-items: center;
        grid-template-columns: 10fr 1fr 1fr;
        grid-gap: 10px;

        .header-title {
            font-weight: bold;
        }

        .required-checkbox {
            display: flex;
            justify-content: center;
        }

        .centered-icon {
            display: flex;
            align-items: center;
        }
    }

    .empty-message {
        padding-top: 20px;
        color: $black-40;
        text-align: center;
    }

    .button-container {
        margin-top: 20px;
    }
}

</style>