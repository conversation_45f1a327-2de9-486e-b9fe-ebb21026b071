<template>
    <div class="app-main-panel" :class="{'new-ui-menu': account.licenses.includes('new_ui_2024')}">
        <ListColumn
            :title="$t('templates.title')"
            :add-route="'/manage/templates/new'"
            :add-button-title="$t('templates.add')"
            :items="templates"
            :selected-item="selectedTemplate"
            :search="search"
            @searchChange="searchChange"
            @select-item="selectTemplateInList"
            :loading="loading"
        />
        <div v-if="active" class="content-container">
            <div class="content-container-spacing">
                <Loader :is-loading="loading" :text="$t('common.loading')">
                    <RouterView v-slot="{ Component }"/>
                </Loader>
            </div>
        </div>
    </div>
</template>

<script>
import ListColumn from '../components/ListColumn';
import {mapState, mapActions, mapGetters, mapMutations} from 'vuex';
import store from '../store/index';
import Loader from "../components/Loader";

export default {
    store: store,
    name: "ManageTemplatesView",
    components: {
        ListColumn,
        Loader
    },
    created() {
        return this.loadData().then(() => {
            this.loadDefaultTemplates();
            if (this.templates.length === 0) {
                this.$router.replace({name: 'templates-intro'});
            }
        });
    },
    computed: {
        ...mapState({
            loading: state => state.managed_templates.loading,
            templates: state => state.managed_templates.templates,
            selectedTemplateId: state => state.managed_templates.selectedTemplateId,
            isMobile: state => state.isMobile,
            user: state => state.user.profile,
            account: state => state.account.current_account
        }),
        ...mapGetters({
            selectedTemplate: 'managed_templates/selectedTemplate',
        }),
    },
    data() {
        return {
            search: '',
            active: true
        };
    },
    methods: {
        ...mapActions({
            loadData: 'managed_templates/loadData',
            loadDefaultTemplates: 'managed_templates/loadDefaultTemplates',
            selectFirstTemplate: 'managed_templates/selectFirstTemplate',
            selectTemplate: 'managed_templates/selectTemplate'
        }),
        ...mapMutations({
            setSelectedTemplateId: 'managed_templates/setSelectedTemplateId'
        }),
        selectTemplateInList(template, initial) {
            if (template) {
                if (initial && this.selectedTemplate) {
                    this.$router.push({
                        name: 'template-edit',
                        params: {
                            template_id: this.selectedTemplate.id
                        }
                    });
                } else {
                    this.selectTemplate(template);
                    this.$router.push({
                        name: 'template-edit',
                        params: {
                            template_id: template.id
                        }
                    });
                }
            }
        },
        searchChange(search) {
            this.search = search;
        }
    },
    watch: {
        selectedTemplateId(newId, oldId) {
            // Check if this view is active before doing any routing.
            if (!this._inactive) {
                // Check if we need to do anything when newId turned to null.
                if (newId === null && oldId) {
                    // If there are still templates select the first.
                    // If not, route back to the ManageTemplatesView to show the empty page.
                    if (this.templates && this.templates.length) {
                        this.selectFirstTemplate();
                    } else {
                        this.$router.push({
                            name: 'manage-templates',
                        });
                    }
                }
            }
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if (!vm.user || !vm.user.can.manual_questions || (!vm.user.can.is_account_manager && !vm.user.can.is_admin)) {
                if (!vm.user || !vm.user.can.is_admin) {
                    vm.$router.push('start-user');
                } else {
                    vm.$router.push('manage-groups');
                }
            } else if (typeof to.params.template_id !== "undefined") {
                vm.setSelectedTemplateId(_.parseInt(to.params.template_id));
            }
        });
    },
    activated() {
        this.active = true;
    },
    deactivated() {
        this.active = false;
    }
}
</script>
