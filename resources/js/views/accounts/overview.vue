-<template>
    <div v-if="account" class="account-overview content-panel">
        <div class="inner-content-panel">
            <ContentHeader :logo-text="account.title"
                           :logo-background-color="account.color" :title="account.title" :subtitle="account.subtitle"/>
            <div class="content-columns">
                <div class="content-column column is-half">
                    <Information :items="personalInfoRows" :header="$t('accounts.overview_info_title')" :edit_link="edit_link"
                                 @action="doAction($event)"></Information>
                    <div class="demo-mode" v-if="showDemoMode">
                        <h2>{{ $t('accounts.demo_mode.title') }}</h2>
                        <Switch
                            :id="'enable_demo_mode'"
                            :name="'enable_demo_mode'"
                            :label="$t('accounts.demo_mode.toggle')"
                            :checked="(account.settings && account.settings.hasOwnProperty('be_demo')) ? account.settings.be_demo : true"
                            @input="(value) => toggleDemoMode(value)"
                        />
                    </div>
                    <br/>
                    <ConsumersTable :loading="loginConsumersLoading" :consumers="loginConsumers"/>
                    <br/>
                    <div class="buttons">
                        <a :href="loginRoute" class="button is-success admin-login" target="_blank">Log in</a>
                        <button @click="goToBilling()" class="button is-info">Account Billing</button>
                    </div>
                </div>
                <div class="content-column column is-half">
                    <AccountStatistics :account="account"/>
                    <div v-if="account.services.whatsapp_service">
                        <h2>WhatsApp Business</h2>
                        <Input v-model:value="whatsapp_mobile_id" :label="$t('whatsapp.mobile_id')"/>
                        <Input v-model:value="whatsapp_mobile" :label="$t('common.mobile')"/>
                        <Input
                            v-if="account.services.whatsapp_service.properties.status === 'pending'"
                            v-model:value="whatsapp_email" :label="$t('common.email')"
                            type="email"
                        />
                        <br>
                        <button class="button is-info" @click="activateWhatsApp">
                            {{$t('common.activate')}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import ContentHeader from "../../components/ContentHeader";
    import Input from "../../components/Forms/Input.vue";
    import Information from '../../components/Information';
    import AccountStatistics from '../../components/Account/Statistics';
    import ConsumersTable from "../../components/ConsumersTable";
    import {mapActions, mapMutations, mapState} from "vuex";
    import Switch from "../../components/Forms/Switch";
    import AccountAdminApi from "../../api/AccountAdminApi";
    import WhatsAppBusinessApi from "../../api/services/WhatsAppBusinessApi";
    import dayjs from "dayjs";

    export default {
        name: "AccountOverview",
        components: {
          ConsumersTable,
            Information,
            ContentHeader,
            AccountStatistics,
            Switch,
            Input
        },
        data() {
            return {
                consumers: null,
                edit_link: 'edit',
                whatsapp_mobile_id: '',
                whatsapp_mobile: '',
                whatsapp_email: ''
            }
        },
        props: {
            account: null
        },
        created() {
           if (this.account) {
               this.loadLoginConsumers(this.account.id);
               if (this.account.services.whatsapp_service && this.account.services.whatsapp_service.properties.status === 'active') {
                   this.whatsapp_mobile = this.account.services.whatsapp_service.properties.mobile;
                   this.whatsapp_mobile_id = this.account.services.whatsapp_service.properties.mobile_id;
               }
           }
        },
        computed: {
            ...mapState({
                loginConsumersLoading: state => state.managed_accounts.loginConsumersLoading,
                loginConsumers: state => state.managed_accounts.loginConsumers,
                permissions: state => state.user.profile.can
            }),
            loginRoute() {
                return '/system/admin/account/' + this.account.id + '/login';
            },
            personalInfoRows() {
                let a = this.account;

                let rows = [
                    {label: 'ID', title: a.id},
                    {label: this.$t('accounts.name'), title: a.title},
                ];

                let hostnameList = a.hostname.split(' ');
                let hostnameRows = [];
                for (let i = 0; i < hostnameList.length; i++) {
                    hostnameRows.push({
                        'text': hostnameList[i],
                        'event': 'ClickHostname'
                    });
                }

                let emailList = a.support_email.split(';');
                let emailRows = [];
                for (let i = 0; i < emailList.length; i++) {
                    emailRows.push({
                        'text': emailList[i],
                        'event': 'ClickEmail'
                    });
                }

                rows.push({label: this.$t('accounts.hostname'), title: '', 'actions': hostnameRows});
                rows.push({label: this.$t('accounts.support_email'), title: '', 'actions': emailRows});

                rows.push.apply(
                    rows,
                    [
                        {label: this.$t('accounts.debtor_number'), title: a.debtor_number},
                        {label: this.$t('accounts.relation_manager'), title: a.relation_manager},
                        {label: this.$t('accounts.sales_manager'), title: a.sales_manager},
                        {label: this.$t('accounts.pricing_model'), title: a.pricing_model},
                        {label: this.$t('accounts.status'), title: a.status},
                        {label: this.$t('accounts.type'), title: a.type},
                        {label: this.$t('accounts.created_at'), title: a.created_at},
                        {label: this.$t('accounts.contract_end_date'), title: this.parseDate(a.contract_end_date)},
                        {label: this.$t('accounts.delete_after'), title: this.parseDate(a.delete_after)},
                    ]
                );

                return rows;
            }
        },
        methods: {
            ...mapActions({
                loadLoginConsumers: 'managed_accounts/loadLoginConsumers',
            }),
            ...mapMutations({
                updateAccount: 'managed_accounts/updateList'
            }),
            parseDate(isoString) {
                if (!isoString) {
                    return '';
                }
                let date = dayjs(isoString);
                return date.format('DD-MM-YYYY');
            },
            goToBilling() {
                this.$router.push({name: 'account-billing', params: {account_id: this.account.id}});
            },
            doAction(action) {
                if (action.event) {
                    this['action' + action.event](action);
                } else {
                    this['action' + action](action);
                }
            },
            actionClickHostname(action) {
                window.open('https://' + action.text, '_blank');
            },
            actionClickEmail(action) {
                window.open('mailto:' + action.text);
            },
            showDemoMode() {
                let mode = true;
                if (this.account.settings && this.account.settings.hasOwnProperty('be_demo')) {
                    mode = this.account.settings.be_demo;
                }

                return this.permissions.is_admin && mode;
            },
            toggleDemoMode(value) {
                AccountAdminApi.updateSettings({
                    account_id: this.account.id,
                    be_demo: Boolean(value)
                });
            },
            activateWhatsApp() {
                let payload = {
                    mobile_id: this.whatsapp_mobile_id,
                    mobile: this.whatsapp_mobile,
                    status: this.account.services.whatsapp_service.properties.status
                };
                if (this.account.services.whatsapp_service.properties.status === 'pending') {
                    payload.email = this.whatsapp_email;
                }
                WhatsAppBusinessApi.activate(this.account.services.whatsapp_service.id, payload).then((response) => {
                    this.updateAccount(response.data.data)
                })
            }
        },
        watch: {
            account() {
                this.loadLoginConsumers(this.account.id);
            }
        }
    }
</script>

<style scoped>
    .admin-login {
        min-width: 120px;
    }

    h2 {
        font-size: 14px;
        font-weight: bold;
        border-bottom: solid 1px #d8d8d8;
        padding-bottom: 20px;
    }

    .demo-mode {
        padding-bottom: 20px;
    }
</style>