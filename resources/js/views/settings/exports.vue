<template>
    <div class="content-panel">
        <div class="inner-content-panel">
            <div class="inner-edit-panel">
                <h1>{{ $t('settings.titles.exports') }}</h1>
                <br>
                <p>{{ $t('settings.exports.subtitle') }}</p>
                <div class="create-export-container">
                    <div class="fields">
                        <SingleSelect
                            class="select-export"
                            name="type"
                            :label="$t('settings.exports.fields.type')"
                            v-model:value="type"
                            :options="types"
                        />
                        <SingleSelect
                            class="select-year"
                            name="type"
                            :label="$t('settings.exports.fields.year')"
                            v-model:value="year"
                            :options="years"
                        />
                    </div>
                    <button class="button is-info" @click="startExport">
                        {{$t('settings.exports.start_export')}}
                    </button>
                </div>
                <div class="export-list-container">
                    <div class="header">
                        <Label class="export-title" :text="$t('settings.exports.table.title')"/>
                        <Label class="field-item" :text="$t('settings.exports.table.requested_at')"/>
                        <Label class="field-item" :text="$t('settings.exports.table.expires_at')"/>
                        <div class="field-download"></div>
                    </div>
                    <div class="field" v-for="exportItem in exports">
                        <div class="export-title" :title="exportItem.title">
                            {{ exportItem.title }}
                        </div>
                        <div class="field-item">
                            {{ exportItem.requested_at }}
                        </div>
                        <div class="field-item">
                            {{ exportItem.expire_at }}
                        </div>
                        <DownloadButton class="field-download" :url="exportItem.download_url" :title="exportItem.title" />
                    </div>
                    <div v-if="exports && exports.length === 0">
                        <p class="empty-placeholder">{{ $t('settings.exports.table.empty_list') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import SingleSelect from "../../components/Forms/SingleSelect.vue";
import ExportApi from "../../api/ExportApi";
import Label from "../../components/Forms/Label.vue";
import FileInput from "../../components/Forms/FileInput.vue";
import DownloadButton from "../../components/Forms/DownloadButton.vue";

export default {
    name: 'Exports',
    components: {DownloadButton, FileInput, Label, SingleSelect},
    computed: {
        types() {
            return {
                open_question: this.$t('settings.exports.export_types.open_questions')
            };
        },
        years() {
            const currentYear = new Date().getFullYear();
            const years = [];

            for (let year = currentYear; year >= 2021; year--) {
                years.push(year);
            }

            return years;
        }
    },
    data() {
        return {
            exports: [],
            type: null,
            year: null,
        }
    },
    created() {
        ExportApi.index().then((res) => {
            this.exports = res.data.data;
        })
    },
    methods: {
        startExport(){
            ExportApi.start({type: this.type, year: this.year});
        }
    }
}

</script>

<style lang="scss" scoped>
@import '../../../sass/initial-variables';

.create-export-container {
    margin-bottom: 100px;

    .fields {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .select-export {
            width: 400px;
        }

        .select-year {
            width: 175px;
        }
    }
}

.export-list-container {
    display: flex;
    flex-direction: column;
}

.field {
    padding: 8px 0;
}

.header,
.field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    border-bottom: 1px solid whitesmoke;

    .export-title {
        flex: 1 1 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis
    }

    .field-item {
        display: flex;
        align-items: center;
        flex: 0 1 200px;
    }

    .field-download {
        flex: 0 1 50px;
    }

    .remove {
        flex-shrink: 0;
        margin-top: 11px;
    }

    .hide-icon {
        visibility: hidden;
    }
}
</style>