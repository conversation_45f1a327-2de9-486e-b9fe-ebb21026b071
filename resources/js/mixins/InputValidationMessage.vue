<script>
export default {
    data() {
        return {
            innerValidation: {}
        }
    },
    computed: {
        message() {

            let status = this.innerValidation && this.innerValidation.status || 'error';
            let element = this.$el;
            if (element && element.classList) {
                let classes = element.classList;
                classes.remove('ok');
                classes.remove('warning');
                classes.remove('error');
                classes.add(status);
            }

            return (typeof this.innerValidation === 'string' ?
                this.innerValidation :
                this.innerValidation && this.innerValidation.message) || '';
        }
    },
    watch: {
        validation: {
            handler(val) {
                this.updateValidationMessage(val);
            },
            immediate: true
        }
    },
    methods: {
        updateValidationMessage(validation) {
            this.innerValidation = validation || {};
        },
        clearValidationMessage() {
            this.innerValidation.message = '';
        }
    }
}
</script>

<style lang="scss" scoped>
    @import '../../sass/initial-variables';

    .message {
        background: none;
        margin-top: 4px;
        min-height: 1.5rem;
        padding: 4px 0 0 0;
        font-size: 14px;
        color: $red;
    }
</style>