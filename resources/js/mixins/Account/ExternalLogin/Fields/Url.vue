<template>
    <div v-if="visibility">
        <a :href="url">
            <i class="sl-icon-link-horizontal"></i>
            <span>{{ this.consumer.url }}</span>
        </a>
    </div>
</template>

<script>
import store from '../../../../store';

export default {
    name: 'ConsumerUrl',
    props:{
        consumer: null
    },
    data() {
        return {
            url: this.consumer ? this.consumer.url : null,
            visibility: store.state.user.profile.can.is_admin && (this.consumer && this.consumer.url) ? true : false
        }
    }
}
</script>

<style scoped lang="scss">

@import '../../../../../sass/initial-variables';

a {
    position: relative;
    bottom: 10px;

    i {
        color: #707070;
        font-size: 26px;
        position: relative;
        top: 7px;
    }
}

</style>