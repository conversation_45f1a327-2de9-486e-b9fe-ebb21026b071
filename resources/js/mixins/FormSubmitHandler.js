import FormGenerator from '../components/Forms/FormGenerator';

export default {
    components: {
        FormGenerator,
    },
    props: {},
    data() {
        return {
            submitting: false
        }
    },
    methods: {
        /**
         * Check the submit by cancelling the default event, doing the validations and checking if the form is already being submitted.
         * @param event
         * @param saveFunction
         * @param resubmittingDelay
         * @param onSuccess - optional promise to execute after successful submission and before re-enabling new submissions
         * @returns {Promise<void | never>}
         */
        checkSubmit(event, saveFunction, resubmittingDelay, onSuccess) {
            event.preventDefault();
            if (this.submitting) { //prevents double submit
                throw new Error('Ignored submit event. We are still waiting for a response of an earlier one.');
            }

            this.submitting = true;
            let form = this.$refs.form;
            if (typeof form !== 'undefined' && !form.validate()) {
                this.submitting = false;
                throw new Error('Ignored submit event. Form validation failed.');
            }

            const delay = (ms, response) => new Promise(resolve => setTimeout(() => resolve(response), ms));
            return Promise.resolve()
                .then(saveFunction)
                // delay only if successful and optionally call the onSuccess promise
                .then((response)=>{
                    resubmittingDelay = resubmittingDelay || 0;
                    let successPromise = delay(resubmittingDelay, response);
                    if (onSuccess) {
                        successPromise = successPromise.then(onSuccess);
                    }
                    return successPromise;
                })
                // re-enable form submission
                .finally(() => this.submitting = false);
        }
    }
}