import BaseApi from "../BaseApi";

class MicrosoftSharepointDmsApi extends BaseApi {
    authorize(accountServiceId, data) {
        return this._post(accountServiceId + '/microsoft_sharepoint_dms/authorize', data)
    }

    listSites(search = '') {
        return this._get(
            'microsoft_sharepoint_dms/list_sites',
            {
                params: {
                    search: search
                }
            }
        )
    }

    listDrives(siteId)
    {
        return this._get(
            'microsoft_sharepoint_dms/list_drives',
            {
                params: {
                    site_id: siteId
                }
            }
        )
    }

    listDriveChildren(driveId)
    {
        return this._get(
            'microsoft_sharepoint_dms/list_drive_children',
            {
                params: {
                    drive_id: driveId
                }
            }
        )
    }

    listItemChildren(driveId, itemId) {
        return this._get(
            'microsoft_sharepoint_dms/list_item_children',
            {
                params: {
                    drive_id: driveId,
                    item_id: itemId
                }
            }
        )
    }

    saveConfiguration(service_task_id, site = null, drive = null, item = null) {
        let data = {
            'service_task_id': service_task_id
        };

        if (site !== null) {
            data.site_id = site.id;
        }

        if (drive !== null) {
            data.drive_id = drive.id;
        }

        if (item !== null) {
            data.item_id = item.id;
        }

        return this._post('microsoft_sharepoint_dms/save_configuration', data);
    }

    getConfigurationsPathForTask(service_task_id) {
        return this._get(
            'microsoft_sharepoint_dms/get_configuration_path_for_task',
            {
                params: {
                    service_task_id: service_task_id
                }
            }
        )
    }
}

export default new MicrosoftSharepointDmsApi('/services/')