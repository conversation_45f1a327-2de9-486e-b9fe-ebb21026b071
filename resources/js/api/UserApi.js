import BaseApi from "./BaseApi";

class UserApi extends BaseApi {
    sendVerifyRequest() {
        return this._post('verify_request');
    }

    updateSignatureImage(data) {
        return this._post('updateSignature', data);
    }

    getSignatureImage() {
        return this._get('signature');
    }

    deleteSignature() {
        return this._post('deleteSignature');
    }

    colleagues() {
        return this._get('colleagues');
    }

    updateAuthSecret(payload) {
        return this._post('update_auth_secret', payload);
    }

    createSecureShareToken() {
        return this._post('create_secure_share_token');
    }

    getSecureShareToken() {
        return this._post('get_secure_share_token');
    }

    deleteSecureShareToken(payload) {
        return this._post('delete_secure_share_token');
    }

    updateCommunicationChannel(payload) {
        return this._post('communication_channel/update', payload);
    }
}

export default new UserApi('/user/')