{"settings": {"global_intro": "Edit your global settings for all members", "files": {"file1": {"url_text": "General terms & conditions and SLA, ", "url": "https://www.hellohix.com/algemene-voorwaarden"}, "file2": {"url": "https://www.hellohix.com/verwerkersovereenkomst", "url_text": "Processing agreement"}}, "certkeys": {"file_label": "Certificate file", "expiration_warning": "Warning: Within Hix you are using your own PKI certificate. This certificate expires within one month.", "expiration_warning_tooltip": "PKI certificate expires within one month", "intro": "When using your own certificate you are responsible for updating it before it expires. Hix will not send you a reminder, the responsibility for this lies with the certificate supplier.", "name": "Name", "modified": "Modified", "expiration_date": "Expiration date", "password_label": "Optional password"}, "company_name_label": "Company name", "support_email_label": "Support email address", "support_email_help": "Use ; to separate the emails. Example: first{'@'}email.com;second{'@'}email.com", "default_language_label": "Default language", "embed_link_label": "Embedded login form", "no_new_tab_label": "Do not open a new browser tab when using the embed login", "global_security_notification": "Attention: The settings below relate to the whole environment, unless it is set differently in another group.", "auth_methods_label": "Available authentication method(s) for this environment", "auth_method_enforcement_explanation": "This date is related to the selected authentication methods. Until this date users are asked to set up an alternative authentication method when needed. After this date user's are enforced to do so.", "remember_me_label": "Allow the browser to remember Hix password", "remember_me_explanation": "When this option is turned OFF, then the passwords on the Hix login page are NOT automatically filled in by the browser.", "enable_password_validity_label": "Limit Password Validity", "password_validity_label": "Password validity", "password_validity_placeholder": "Number of days", "enable_ip_whitelist_label": "Enable location restrictions (IP whitelisting)", "ip_whitelist_label": "Whitelist for IP address restriction", "ip_whitelist_placeholder": "eg. ************", "ip_whitelist_explanation": "Use ; (semicolon) to separate IP addresses", "ip_whitelist_mode_label": "Whitelist mode", "ip_whitelist_mode_explanation": "Maximum duration of login sessions (hours)", "session_lifetime_max_label": "Maximum duration of login sessions (hours)", "session_lifetime_max_explanation": "This setting represents the maximum session duration that a user can set. Use the option below to apply this value to users directly.", "session_lifetime_apply_directly_label": "Apply the above session duration to users directly", "single_session_label": "Allow only one active login session for users", "primary_color_label": "Primary color", "logo_label": "Logo", "background_image_label": "Background image", "add_account_manager": "Add account manager", "delete_account_manager": "Delete membership", "account_manager": {"create_title": "Create account manager", "delete_button": "Delete membership", "add_membership_button": "Add membership", "delete_message": "Are you sure you want to delete the account manager membership of this user?", "delete_no_memberships_title": "You are unable to delete this account manager membership", "delete_no_memberships_message": "This user has no other memberships, if you want to delete this user completely, do this via the user menu.", "add_existing_account_manager": "Add existing user as account manager", "current_account_managers": "Current account managers", "this_is_you": "This is you", "select_existing_user": "Select existing users to become account manager", "add_existing_button": "Add account managers", "existing_user_placeholder": "Select or search...", "add_users_as_account_manager": "Are you sure you want to add these users as account managers?"}, "company_manager": {"create_title": "Create company manager", "delete_button": "Delete membership", "current_company_managers": "Current company managers", "add_existing_company_manager": "Add existing user as company manager", "add_company_managers_button": "Add company managers", "add_users_as_company_manager": "Are you sure you want to add these users as company managers?", "delete_message": "Are you sure you want to delete the company manager membership of this user?"}, "titles": {"global": "Global", "branding": "Branding", "managers": "Managers", "account_managers": "Account Managers", "company_managers": "Company Managers", "security": "Security", "communication": "Communication", "message_configurator": "Message Configurator", "sms": "SMS", "channels": "Channels", "embedded_login": "Embedded login", "certkeys": "Certificates and keys", "api": "API", "access": "Access", "user_reactivation_requests": "Reactivation requests", "webhooks": "Webhooks", "exports": "Exports"}, "communication": {"emails_template": {"introduction": "On this page you can configure your own mail texts. In case you don't change the texts, we will send our default mails. Based on the green checkmarks you can see which mails have a custom text.", "tags": "Tags", "apply_to": "Apply to", "apply_to_warning": "Are you sure you don’t want to apply this mail to anything?", "variant": "<PERSON><PERSON><PERSON>", "test": "Test E-mail", "sections": {"general": "General", "tasks": "Declarations & documents", "questions": "Open questions"}, "editor": {"bold": "Bold", "italic": "Italic", "link": "Link", "unlink": "Remove link", "undo": "Undo", "redo": "Redo"}}, "sms": {"sender_name": "Sender name"}, "channels": {"notification": "Below you can set up which communication channels the client users can choose from.", "default": "<PERSON><PERSON><PERSON>", "channel_types": {"service_task": "Hix Docs", "open_question": "Hix Questions"}, "channel": {"email": "Email", "whatsapp": "WhatsApp", "postbode": "Letter"}, "disable_swal_text": "This channel is used by users, are you sure you want to disable this channel?"}}, "allow_copy_password": {"title": "Allow administrators to copy passwords for widgets on group- and account level", "warning": "This functionality allows administrators to copy password from the widget on group- and account level. Only use with clear intent. We advise you to switch this off again when access to the password is no longer needed."}, "webhooks": {"empty_list": "There are no webhooks yet, press \"+\" to add them.", "token": {"title": "Verification token", "message": "Every webhook message contains a digest header which is generated using the token below. Use this token to verify the authenticity of the message. This token is not be shared with others."}}, "exports": {"subtitle": "Select the type of export and apply your filters. Click export when you're done. You will receive an email as soon as your export is ready to download.", "start_export": "Start export", "fields": {"type": "Select export", "year": "Year"}, "export_types": {"open_questions": "Open questions"}, "table": {"title": "Title", "requested_at": "Requested at", "expires_at": "Expires at", "empty_list": "You have not yet created an export. You can add them above."}}, "allow_change_preferred_communication_channel": "Allow colleagues to change preferred communication channel"}, "accounts": {"title": "Accounts", "add_account": "Add account", "login_consumer_empty": "There are no external login options for this environment", "login_consumer_title": "External login options", "overview_info_title": "Account information", "add_title": "Add account", "demo_mode": {"title": "Demo mode configuration", "toggle": "Enable demo mode"}, "name": "Name", "subtitle": "Subtitle", "color": "Color", "support_email": "Support email", "support_email_help": "Semicolon separated list of Support emails", "debtor_number": "Debtor number", "relation_manager": "Relation manager", "sales_manager": "Sales Manager", "pricing_model": "Price model", "status": "Status", "trial_end_date": "Trial end date", "contract_end_date": "Contract end date", "delete_after": "Delete environment after", "type": "Type", "hostname": "Hostname", "hostname_help": "Hostname (e.g. xyz.comandi.nl)", "created_at": "Created at", "account_type": "Type", "category": "Category", "interfaces": "Interfaces", "api_origins": "Allowed API origins", "licenses": "Licenses", "pipedrive_number": "Pipedrive number", "feature_package": "Feature package", "login_autocomplete": "Allow autocomplete login", "unlimited_trial_users": "Unlimited number of of trial users", "disable_login_screen": "Disable login screen", "redirect_emails_to_mailtrap": "Redirect emails to mailtrap", "external_login": {"name": "Name", "title": "Edit external login option", "title_add": "Add external login option", "delete": "Do you want to delete this external option?", "options": {"default": {"title": "<PERSON><PERSON><PERSON>", "description": "Add an external login in account"}, "azure": {"title": "Azure", "description": "Add an external login in account"}, "microsoft": {"title": "Microsoft Azure", "description": "Add an external login in account"}, "saml2": {"title": "SAML2", "description": "Add an external login in account"}, "jwt": {"title": "JWT", "description": "Add an external login in account"}, "openidconnect": {"title": "OpenID Connect", "description": "Add an external login in account"}}, "metadata_url": "Metadata URL", "metadata_label": "If the metadata URL is not available you can upload the metadata file", "idp": "<PERSON><PERSON> acts as IDP", "name_id_format": "Name ID Format", "name_id_format_label": "When empty %%auth_id%% is used. Values %%email%%, %%user_id%%, %%account_id%%, %%attr[subject]%% can also be used.", "login_url": "Login Endpoint", "login_title": "Login title", "login_icon": "Login button icon", "auth_id": "Auth ID / Client ID", "auth_secret": "Auth Secret / Client Secret", "tenant": "Tenant", "provider": "Provider", "provisioning_id": "Provisioning ID", "login_button": "Enable login screen button", "logout_enabled": "Logout at external application", "notify_on_create": "Notify on create", "identity_linking": "Identity linking", "two_step_authentication": "Two step authentication", "specific_external_login": "External login for specific users", "login_endpoints": "Login endpoints", "logout_endpoints": "Logout endpoints", "select_generic_widget": "Select Generic Widget", "onboard_context_id": "Select Group", "hostname": "Hostname", "max_validity": "Token validity in minutes", "certificate": "Verification certificate", "verification_keys": "Get verification keys from JWKS URI"}, "statistics": {"title": "Account statistics", "internal_users": {"total": "Internal users", "active_last_month": "Active last month"}, "external_users": {"total": "External users", "active_last_month": "Active last month"}, "companies": "Companies", "billable_companies_declarations": "Billable companies declarations", "billable_companies_questions": "Billable companies for questions"}}, "security_settings": {"change_password": "Change password", "current_password": "Current password", "new_password": "New password", "new_password_verify": "New password (for verification)", "auth_method": "Authentication method", "front_auth_method": "Authentication method for task login (email)", "front_auth_method_warning": "Please be aware that this allows users to gain access to documents and declarations directly via the link in the mail. Using this option is at your own risk, and should only be used when truly necessary.", "front_auth_method_warning_user": "Please be aware that this user now has access to documents and declarations directly via the link in the mail. Using this option is at your own risk, and should only be used when truly necessary.", "email_front_auth": "Allow to send code for task login by email", "qr_info": "Scan the QR code using an authenticator app like Microsoft Authenticator or Google Authenticator. Next to that, enter the verification code that the app generates below to confirm this authentication method. In the future, a fresh generated code is needed to login.", "code": "Verification code", "max_life": "Maximum duration of login sessions (hours)", "one_session": "Allow only one active session", "password_validation_weak": "The password is weak.", "password_validation_medium": "The password is acceptable.", "password_validation_strong": "The password is strong.", "password_mismatch": "The passwords do not match.", "password_match": "The passwords match.", "ip_whitelist_add_current": "Add current IP address"}, "api_tokens": {"title": "API tokens", "headers": {"token": "Token", "created_at": "Created at"}, "new_token": {"title": "<PERSON><PERSON> created", "message": "Here is your new API token, copy and save it somewhere safe. <br><strong>Be aware</strong> you will not be able to copy this token again."}, "delete": {"message": "Are you sure you want to delete token <strong>{token}</strong>?"}, "no_tokens": "There are no API tokens yet, press \"+\" to add them."}, "security": {"field_enforcement_date": "Enforcement date"}}