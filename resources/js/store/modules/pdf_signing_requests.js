import axios from 'axios';

const state = {
    all: [],
    selectedId: null,
    loading: true,
    openPlaceholderCountForSelectedTask: 0,
    signedConfirmation: null
};

const getters = {
    getAllTasksWithSignRequests(state) {
        return state.all;
    },
    selected(state) {
        if (state.selectedId) {
            return state.all.find(s => s.id === state.selectedId);
        }
        return null;
    },
    getCount(state) {
        return state.all.length;
    }
};

const actions = {
    loadRequests({commit, dispatch}) {
        let url = '/task/pdf_sign_requests';
        return axios.get(url).then(response => {
            commit('setData', response.data.data);
            commit('setLoadingState', false);
        }).finally(() => {
            state.loading = false;
        });
    },
    approve({commit}, data) {
        let url = '/task/sign_placeholders';
        return axios.post(url, {task_id: data.taskId, filled_placeholders: data.filledPlaceholders}).then(() => {
            let index = state.all.findIndex(task => task.id === data.taskId);
            if (index > -1) {
                state.all.splice(index, 1);
            }
        });
    },
    setSigningConfirmation({state}, data) {
        state.signedConfirmation = data
    },

    selectById({commit}, id) {
        commit('selectById', id)
    }
};

const mutations = {
    setData(state, data) {
        state.all = data;
    },
    selectById(state, id) {
        state.selectedId = id;
    },
    setLoadingState(state, loading) {
        state.loading = loading
    },
};

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
}

