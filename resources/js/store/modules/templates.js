import OpenQuestionTemplateApi from "../../api/OpenQuestionTemplateApi";

const state = {
    availableTemplates: {},
    creatingTemplateQuestion: false,
    templateInfo: {},
    templateType: null,
};

const getters = {
    availableTemplatesForCategory: (state) => (category) => {
        if (state.availableTemplates[category] !== undefined) {
            return state.availableTemplates[category];
        }
        return [];
    },
    selectedFieldIds(state) {
        if (state.templateInfo && state.templateInfo.fields){
            let filteredFields = state.templateInfo.fields.filter((field) => {
                return field.checked
            });

            return  filteredFields.map((field) => {
                return field.id
            });
        }
        return [];
    },
    filledRemarks(state) {
        let fields = {};

        if (state.templateInfo && state.templateInfo.fields) {
            let filteredFields = state.templateInfo.fields.filter((field) => {
                return field.remark
            });

            filteredFields.forEach(field => {
                fields[field.id] = field.remark;
            });
        }
        return fields;
    }
};

const actions = {
    loadAvailableTemplatesForCategory({commit, state}, [category, companyId]) {
        return OpenQuestionTemplateApi.loadAvailableTemplatesForCategory(category, companyId).then((response) => {
            commit('setAvailableTemplatesForCategory', [category, response.data.data]);
        });
    },
    createTemplateEntry({commit, state}, [template_id, fields, category, title, company_id, remarks]) {
        return OpenQuestionTemplateApi.createTemplateEntry(
            template_id,
            fields,
            category,
            title,
            company_id,
            remarks,
            state.templateType.id
        );
    },
    checkTemplate({commit, state}, [template_id, checked]) {
        let index = state.templateInfo.fields.findIndex(field => field.id === template_id);
        let field = state.templateInfo.fields[index];
        field.checked = checked;
        if (field.options && field.options.length) {
            for (let option of field.options) {
                if (option.condition) {
                    state.templateInfo.fields[option.condition].checked = checked;
                }
            }
        }
    },
    loadTemplateType({commit, state}) {
        if (!state.templateType) {
            return OpenQuestionTemplateApi.loadType().then(response => {
                commit('setTemplateType', response.data);
            });
        }
    }
};

const mutations = {
    setAvailableTemplatesForCategory(state, [category, data]) {
        state.availableTemplates[category] = data;
    },
    setCreatingTemplateQuestion(state, value) {
        state.creatingTemplateQuestion = value;
    },
    setTemplateInfo(state, data) {
        state.templateInfo = data;
    },
    setTemplateType(state, type) {
        state.templateType = type;
    }
};

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
};