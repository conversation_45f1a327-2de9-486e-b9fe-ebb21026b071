<template>
    <div class="slider-container">
        <div class="slider">
            <div class="user-account-menu slide">
                <div class="user">

                    <Logo class="list-item-icon" :text="user.label" :background-color="user.color" :width="44" :height="44"/>
                    <div class="name">{{user.label}}</div>
                </div>
                <ul class="options">
                    <li @click="showProfileSettings"><div>{{ $t('profile_menu.settings') }}</div></li>
                    <li @click="showSecuritySettings"><div>{{ $t('profile_menu.security_settings') }}</div></li>
                    <li @click="showBrowserExtension"><div>{{ $t('profile_menu.install_be') }}</div></li>
                    <li @click="showSecureShare" v-if="user.can.secure_share"><div>{{ $t('service.other.secure_share.title') }}</div></li>
                    <li @click="logoutUser" id="btn_logout" class="logout"><div>{{ $t('profile_menu.logout') }}</div></li>
                </ul>

            </div>
            <div class="user-account-sub-panel slide">
                <component :is="subPanel" @back="showIndexPanel()"/>
            </div>
        </div>
    </div>
</template>

<script>

import store from '../../store/index';
import ProfileSettings from './ProfileSettings';
import SecuritySettings from './SecuritySettings';
import BrowserExtension from './BrowserExtension';
import SlidingPanels from '../../mixins/SlidingPanels';
import Logo from '../Logo'
import SecureShare from './SecureShare';
import {mapState} from "vuex";

export default {
    mixins: [SlidingPanels],
    store: store,
    name: 'ProfilePanel',
    components: {
        ProfileSettings,
        SecuritySettings,
        BrowserExtension,
        Logo,
        SecureShare
    },
    props: {
        activePanel: {
            type: String,
            default: ''
        },
        width: {
            type: Number,
            default: 600
        }
    },
    data() {
        return {
            subPanel: null,
        }
    },
    computed: {
        ...mapState({
            user: state => state.user.profile
        }),
    },
    mounted() {
        this.handleActivePanel();
    },
    methods: {
        handleActivePanel() {
            if (this.activePanel === 'ProfileSettings') {
                this.showProfileSettings();
            } else if (this.activePanel === 'SecuritySettings') {
                this.showSecuritySettings();
            } else if (this.activePanel === 'BrowserExtension') {
                this.showBrowserExtension();
            } else if (this.activePanel === 'SecureShare') {
                this.showSecureShare();
            }
        },
        showIndexPanel() {
            this.slideTo(0);
            this.subPanel = null;
        },
        showProfileSettings() {
            this.subPanel = ProfileSettings;
            this.slideTo(1);
        },
        showSecuritySettings() {
            this.subPanel = SecuritySettings;
            this.slideTo(1);
        },
        showBrowserExtension() {
            this.subPanel = BrowserExtension;
            this.slideTo(1);
        },
        showSecureShare() {
            this.subPanel = SecureShare;
            this.slideTo(1);
        },
        logoutUser() {
          if (this.user.using_remember_me) {
            this.$swal({
              title: this.$t('common.are_you_sure'),
              text: this.$t('profile_menu.confirm_logout_remember_me'),
              icon: 'warning',
              showCancelButton: true,
              confirmButtonColor: '#0F9BF3',
              cancelButtonColor: '#999999',
              cancelButtonText: this.$t('common.cancel'),
              confirmButtonText: this.$t('common.yes')
            }).then((result) => {
              if (result.value) {
                window.location = '/auth/logout';
              }
            })
          } else {
            window.location = '/auth/logout';
          }
        }
    }
}

</script>

<style lang="scss" scoped>

    @import "../../../sass/initial-variables";

    .slider-container {
        position: absolute;
        top: 88px;
        left: 0px;
        right: 0px;
        bottom: 0px;
        overflow-x: hidden;

        .slider {
            position: absolute;
            top: 0px;
            left: 0px;
            bottom: 0px;
            right: 0px;
            transition: 0.3s left;

            .slide {
                position: absolute;
                top: 0px;
                left: 0px;
                bottom: 0px;
            }
        }
    }

    .user-account-menu {

        .user {
            background-color: $black-03;
            height: 100px;
            padding: 28px 30px;
            border-bottom: solid 1px $black-03;

            .image {
                display: inline-block;
                vertical-align: middle;
                width: 44px;
                height: 44px;
                border-radius: 4px;
                margin-right: 16px;
                overflow: hidden;

                img {
                    width: 44px;
                    height: 44px;
                }
            }

            .name {
                display: inline-block;
                vertical-align: middle;
                font-size: 16px;
                font-weight: 600;
                margin-left: 16px;
            }
        }

        .options {
            display: table;
            width: 100%;
            li {
                display: table-row;
                cursor: pointer;
                position: relative;

                div {
                    padding: 18px 28px;
                    line-height: 24px;
                }

                div.separator {
                    border-top: solid 1px $black-10;
                    padding-top: 31px;
                    margin-top: 0;
                }

                &.logout {
                    font-weight: 500;
                }

                &:hover {
                    background-color: $black-03;
                }
            }
            hr {
                margin: 0;
            }
        }

    }

</style>