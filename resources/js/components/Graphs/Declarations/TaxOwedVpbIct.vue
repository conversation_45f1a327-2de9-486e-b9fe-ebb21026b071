<template>
    <TaxChartWrapper>
        <template v-slot:title>
            {{ title }}
        </template>
        <template v-slot:content>
            <ZeroCenteredBar :legend="$t('tasks.graphs.indebted_taxes')"
                             :bar-size="barSize" :value="indebtedTax" />
            <ZeroCenteredBar :legend="$t('tasks.graphs.withholding_taxes')"
                             :bar-size="barSize" :value="withholdingTax" />
            <ZeroCenteredBar :legend="$t('tasks.graphs.provisional_assessments')"
                             :bar-size="barSize" :value="provisionalAssessments" />
            <hr>
            <ZeroCenteredBar :legend="$t('tasks.graphs.total')"
                             :bar-size="barSize" :value="total" :show-values="false"/>
        </template>
        <template v-slot:total>
            {{$t('tasks.graphs.amount_payable')}} {{totalString}}
        </template>
    </TaxChartWrapper>
</template>

<script>
import ZeroCenteredBar from "../ZeroCenteredBar";
import TaxChartWrapper from "./TaxChartWrapper";

export default {
    name: "TaxOwedVpbIct",
    components: {TaxChartWrapper, ZeroCenteredBar},
    props: {
        indebtedTax: {
            type: Number,
            required: true
        },
        withholdingTax: {
            type: Number,
            required: true
        },
        provisionalAssessments: {
            type: Number,
            required: true
        },
        total: {
            type: Number,
            required: true
        },
        title: {
            type: String,
            required: true
        }
    },
    computed: {
        barSize() {
            return Math.max(this.indebtedTax, this.withholdingTax, this.total);
        },
        totalString() {
            return new Intl.NumberFormat(
                'nl-NL',
                { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }
            ).format(this.total);
        }
    }
}
</script>

<style scoped>

</style>