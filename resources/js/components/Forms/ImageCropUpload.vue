<template>
    <div class="image-input" :class="className">
        <div class="label">{{ label }}</div>

        <div class="checkerboard" @click="clickImage">
            <img v-if="imageValue" :src="imageValue" alt="">
        </div>
        <avatar-cropper
            v-model="showCropper"
            :upload-handler="uploadHandler"
            :class="{'cropper-round': isRound}"
            :cropper-options="cropperOptions"
            :labels="{ submit: $t('common.crop'), cancel: $t('common.cancel')}"
        />
        <div class="file-selector">
            <div class="wrapper">
                <button type="button" class="button is-info browse pull-left" @click="clickImage">
                    {{ $t('common.browse_button') }}
                </button>
                <button @click="reset" type="button" class="button is-danger is-inverted pull-right">
                    {{ $t('common.branding_remove') }}
                </button>
            </div>
        </div>
        <div class="info">{{ message }}</div>
    </div>
</template>

<script>
import AvatarCropper from "vue-avatar-cropper"
import canvasToBlob from "canvas-toBlob"

export default {
    components: {AvatarCropper},
    props: {
        isRound: {
            type: Boolean,
            default: true
        },
        value: '',
        label: '',
        className: '',
        cropperOptions: {
            type: Object,
            default() {
                return {
                    aspectRatio: 1,
                    autoCropArea: 1,
                    viewMode: 0,
                    movable: false,
                    zoomable: true
                }
            }
        }
    },
    data() {
        return {
            showCropper: false,
            message: this.$t('common.max_file_size_2mb'),
            imageValue: null
        }
    },
    watch: {
        value() {
            this.imageValue = this.value;
        },
    },
    mounted() {
        this.imageValue = this.value;
    },
    methods: {
        clickImage() {
            this.showCropper = true;
        },
        uploadHandler(event) {
            console.log(event)
            let canvas = event.getCroppedCanvas();
            if (this.isRound) {
                canvas = this.convertSquareToCircle(canvas);
            }
            this.imageValue = canvas.toDataURL();
            canvas.toBlob((blob) => {
                this.$emit('input', blob);
            });
        },
        convertSquareToCircle(squareCanvas) {
            const size = squareCanvas.width; // assuming width == height
            const circularCanvas = document.createElement('canvas');
            circularCanvas.width = size;
            circularCanvas.height = size;

            const ctx = circularCanvas.getContext('2d');

            // Make everything transparent
            ctx.clearRect(0, 0, size, size);

            // Create a circular clipping path
            ctx.save();
            ctx.beginPath();
            ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.clip();

            // Draw the square image into the circular clipping region
            ctx.drawImage(squareCanvas, 0, 0, size, size);
            ctx.restore();

            return circularCanvas; // returns base64 PNG with transparency
        },
        reset() {
            this.imageValue = null;
            this.$emit('input', new Blob());
        },
    }
}
</script>

<style lang="scss" scoped>

.image-input {
    margin-bottom: 32px;
    display: grid;

    .checkerboard {
        cursor: pointer;
        display: inline-block;
        border-radius: 50%;
        margin: auto;
        background-image: /* tint image */
            linear-gradient(to right, rgba(255, 255, 255, 0.90), rgba(255, 255, 255, 0.90)),
                /* checkered effect */
            linear-gradient(to right, black 50%, white 50%),
            linear-gradient(to bottom, black 50%, white 50%);
        background-blend-mode: normal, difference, normal;
        background-size: 2em 2em;
        max-width: 250px;
        max-height: 250px;
        min-height: 70px;
        min-width: 70px;
        text-align: center;
        line-height: 0;
        margin-bottom: 16px;

        img {
            border-radius: 50%;
        }
    }


    .file-selector {
        position: relative;
        height: 46px;
        text-align: center;

        .wrapper {
            position: absolute;
            top: 0px;
            left: 0px;
            right: 0px;
            bottom: 0px;
            text-align: center;
        }

        button.browse {
            min-width: 140px;
        }

        button {
            cursor: pointer;
        }

        input[type="file"] {
            opacity: 0;
            width: 200px;
            cursor: pointer;
        }
    }

    .info {
        margin-top: 10px;
        text-align: center;
        padding-left: 0;
    }

    &.narrow {
        width: 250px;
    }
}

</style>