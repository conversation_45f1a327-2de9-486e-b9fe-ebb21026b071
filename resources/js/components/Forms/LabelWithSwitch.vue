<template>
    <div class="container" :class="type">
        <label class="label">{{ text }}</label>
        <Switch
            id="id"
            :name="id"
            :checked="checked"
            @input="$emit('input', $event)"
        />
    </div>
</template>

<script>
import Switch from "./Switch";

export default {
    name: "LabelWithSwitch",
    components: {Switch},
    props: {
        text: String,
        checked: Boolean,
        id: String,
        type: String
    },
}
</script>

<style scoped lang="scss">
@import '../../../sass/initial-variables';

.container {
    max-height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.normal {
    font-weight: normal;
}

</style>