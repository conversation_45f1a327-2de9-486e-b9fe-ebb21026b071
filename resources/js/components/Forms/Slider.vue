<template>
    <div v-if="visibility" class="slider-input">
        <label>
            <div class="label">{{ label }}</div>
            <VueSlider
                :modelValue="value"
                @change="update"
                v-bind="settings"
                speed="0.1"
                :marks="updatedMarks"
            />
        </label>
        <br>
    </div>
</template>
<script>

import VueSlider from 'vue-slider-component';
import 'vue-slider-component/theme/default.css';

export default {
    name: 'Slider',
    components: {
        'VueSlider': VueSlider
    },
    props: {
        settings: Object,
        label: String,
        name: String,
        value: '',
        visibility: {
            default: true
        },
        disabled: {
            default: false
        },
    },
    data() {
        return {
            updatedMarks: {}
        }
    },
    mounted() {
        if (typeof this.settings.marks != 'undefined') {
            this.$el.classList.add('has-marks');
            this.trimMarks();
        }
    },
    methods: {
        update(value) {
            this.$parent.updateForm(this.name, value);
        },
        /**
         * Remove any marks that are higher than the maximum because the VueSlider component has a bug that displays them at the start.
         */
        trimMarks() {
            this.updatedMarks = {};
            if (typeof this.settings.marks != 'undefined') {
                for (let i in this.settings.marks) {
                    if (parseFloat(i) <= this.settings.max) {
                        this.updatedMarks[i] = this.settings.marks[i];
                    }
                }
            }
        }
    },
    watch: {
        'settings.max'() {
            this.trimMarks();
        }
    }
}

</script>

<style lang="scss" scoped>

.slider-input {

    &.has-marks {
        .vue-slider {
            margin-bottom: 18px;
        }
    }
}

</style>