<template>
    <div :name="name">
        <div class="spinner" v-if="loading"/>
        <Multiselect
            v-else
            ref="multiselect"
            v-model="innerValue"
            :name="name"
            :options="options"
            :label="label"
            :multiple="multiple"
            :trackBy="trackBy"
            :closeOnSelect="closeOnSelect"
            :disabled="disabled"
            :clearOnSelect="clearOnSelect"
            :allowEmpty="allowEmpty"
            @update:modelValue="emitFormValue"
            @remove="$emit('remove', $event)"
            @select="$emit('add', $event)"
            @search-change="$emit('search-change', $event)"
            :placeholder="placeholder ? placeholder : $t('form.multi_select_default_placeholder')"
            :deselectLabel="multiple ? $t('multi_select.click_to_remove') : ''"
            :selectLabel="selectLabel ? selectLabel : $t('multi_select.click_to_select')"
            :selectedLabel="selectedLabel ? selectedLabel : $t('multi_select.selected')"
            :group-select="groupSelect"
            :group-values="groupValues"
            :group-label="groupLabel"
            :selectGroupLabel="selectGroupLabel"
            :style="cssProps"
            :loading="searchLoading"
            :optionsLimit="10_000"
        >
            <span slot="noResult">{{ $t('multi_select.no_items_found') }}</span>
            <span slot="noOptions">{{ $t('multi_select.no_items_found') }}</span>
        </Multiselect>
        <div v-if="message" class="message">{{ message }}</div>
    </div>
</template>

<script>

import Multiselect from 'vue-multiselect'
import InputValidationMessage from "../../mixins/InputValidationMessage.vue";

export default {
    name: "MultipleSelect",
    emits: ['remove', 'add', 'input', 'update:value', 'search-change'],
    mixins: [InputValidationMessage],
    components: {
        Multiselect
    },
    props: {
        loading: {
            type: Boolean,
            default: false
        },
        /**
         * Inner loading state for the search
         */
        searchLoading: {
            type: Boolean,
            default: false
        },
        /**
         * Field name
         * @type {String}
         */
        name: {
            type: String,
            default: ''
        },
        /**
         * Presets the selected options value.
         * @type {Object||Array||String||Integer}
         */
        value: {
            type: null,
            default: []
        },
        /**
         * Array of available options: Objects, Strings or Integers.
         * If array of objects, visible label will default to option.label.
         * If `labal` prop is passed, label will equal option['label']
         * @type {Array}
         */
        options: {
            type: Array,
            required: true
        },
        /**
         * Equivalent to the `multiple` attribute on a `<select>` input.
         * @default false
         * @type {Boolean}
         */
        multiple: {
            type: Boolean,
            default: true
        },
        /**
         * Enable/disable closing after selecting an option
         * @default true
         * @type {Boolean}
         */
        closeOnSelect: {
            type: Boolean,
            default: false
        },
        /**
         * Clear the search input after `)
         * @default true
         * @type {Boolean}
         */
        clearOnSelect: {
            type: Boolean,
            default: false
        },
        /**
         * Allows to remove all selected values. Otherwise one must be left selected. `)
         * @default false
         * @type {Boolean}
         */
        allowEmpty: {
            type: Boolean,
            default: true
        },
        /**
         * Label to look for in option Object
         * @default 'label'
         * @type {String}
         */
        label: {
            type: String
        },
        /**
         * Key to compare objects
         * @default 'id'
         * @type {String}
         */
        trackBy: {
            type: String
        },
        /**
         * Placeholder when nothing is selected
         * @default 'Select option'
         * @type {String}
         */
        placeholder: {
            type: String,
            default: ''
        },
        /**
         * Disable the MultiSelect
         */
        disabled: false,
        selectLabel: {
            type: String,
            default: ''
        },
        selectedLabel: {
            type: String,
            default: ''
        },
        required: Boolean,
        validation: Object,
        groupValues: {
            type: String
        },
        groupLabel: {
            type: String
        },
        groupSelect: {
            type: Boolean,
            default: false
        },
        selectGroupLabel: {
            type: String,
            default: ''
        },
        maxHeight: {
                type: Number,
                default: 0 // 0 -> disable
            }
        },
        computed: {
            cssProps(){
                let cssProps = {};
                if (this.maxHeight !== 0){
                    cssProps['--max-height'] = this.maxHeight + 'px';
                }
                return cssProps;
            }
        },
    data() {
        return {
            innerValue: null
        }
    },
    mounted() {
        this.setValue(this.value);
    },
    methods: {
        emitFormValue(value) {
            if (value) {
                if (this.trackBy) {
                    if (Array.isArray(value)) {
                        value = value.map(v => v[this.trackBy]);
                    } else {
                        value = value[this.trackBy];
                    }
                }
            }
            this.$emit('update:value', value);
            this.$emit("input", value);
        },
        setValue(value) {
            if (this.trackBy) {
                let newVal = [];
                for (let i in this.options) {
                    if (Array.isArray(value)) {
                        if (value.includes(this.options[i][this.trackBy])) {
                            let entry = {};
                            entry[this.trackBy] = this.options[i][this.trackBy];
                            entry[this.label] = this.options[i][this.label];
                            newVal.push(entry);
                        }
                    } else {
                        if (value === this.options[i][this.trackBy]) {
                            let entry = {};
                            entry[this.trackBy] = this.options[i][this.trackBy];
                            entry[this.label] = this.options[i][this.label];
                            newVal.push(entry);
                        }
                    }
                }
                this.innerValue = newVal;
            } else {
                this.innerValue = value;
            }
        },
        validate() {
            if (this.required && this.innerValue.length < 1) {
                this.updateValidationMessage({
                    valid: false,
                    message: this.$t('validation.value_required')
                });

                return false;
            }

            this.clearValidationMessage();
            return true;
        },
        close() {
            this.$refs.multiselect.deactivate();
        }
    },
    watch: {
        value: function (newVal, oldVal) {
            this.setValue(newVal);
        },
        innerValue: function (newVal, oldVal) {
            if (newVal && newVal.length > 0) {
                this.clearValidationMessage();
            }
        },
    }
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>

<style>

.multiselect__tags {
    min-height: 40px;
    max-height: var(--max-height);
    overflow: auto;
}

.multiselect__tags > span {
    font-size: 14px !important;
}

.multiselect__tags > input {
    font-size: 14px !important;
}

.multiselect__option--highlight:after {
    background-color: #0F9BF3 !important;
}

.multiselect__content-wrapper > ul > li > span {
    white-space: break-spaces;
}

.multiselect__tag-icon:hover {
    background-color: inherit;
}

.multiselect__tag-icon::after {
    color: white;
}

.multiselect__tag-icon:hover:after {
    color: black;
}

.multiselect__single {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.multiselect--disabled {
    .multiselect__select {
        width: 40px;
        height: 41px;
        border-bottom-right-radius: 6px;
    }
}

.spinner {
    mask: url('../../../images/spinning-loader.svg') no-repeat center;
    cursor: wait;
    background-color: deepskyblue;
    height: 28px;
    margin: 7px 0;
}
</style>