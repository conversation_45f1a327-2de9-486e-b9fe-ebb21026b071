<template>
    <div class="message-container">
        <Notification class="notification" :text="login_disabled_label" :type="'is-warning'"></Notification>
    </div>
</template>

<script>
import Notification from "../components/Forms/Notification";

export default {
    name: "Error",
    components: {
        Notification
    },
    data() {
        return {
            login_disabled_label: this.$t('auth.login_disabled')
        }
    }
}
</script>

<style scoped>
.message-container{
    display: flex;
    align-self: center;
    justify-content: center;

    .notification{
        height: 30px;
        width: 90%;
    }
}

</style>