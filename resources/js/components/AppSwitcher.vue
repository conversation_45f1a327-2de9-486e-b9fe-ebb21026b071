<script>
import {mapState} from 'vuex';

import Popover from './Popover.vue';
import Icon from './Icon.vue';
import AppSwitcherItem from './AppSwitcherItem.vue';
import Indicator from "./Indicator.vue";

export default {
    name: 'AppSwitcher',
    components: {Indicator, AppSwitcherItem, Popover, Icon},
    computed: {
        ...mapState({
            pdfSignRequests: state => state.pdf_signing_requests.all,
            allRequests: state => state.service_task_signing_requests.all,
            isMobile: state => state.isMobile,
            isManagerPortal: state => state.isManagerPortal,
            account: state => state.account.current_account,
            unreadWhatsAppCount: state => state.account.unread_whatsapp_messages,
            user: state => state.user.profile,
            managerMenu: state => state.menu.managerMenu
        }),
        showNotificationIndicator() {
            if (this.user.can.is_external) {
                return true;
            }
            return this.pdfSignRequests.length > 0 || this.allRequests.length > 0;
        }
    },
    props: {
        updatedItem: Object,
    },
    data() {
        return {
            visible: false,
            menuItems: [
                {
                    name: 'user-tasks',
                    path: '/tasks',
                },
                {
                    name: 'user-companies',
                    path: '/companies',
                },
                {
                    name: 'open-questions',
                    path: '/open_questions',
                },
                {
                    name: 'view-dossier',
                    path: '/view-dossier',
                },
                {
                    name: 'requests',
                    path: '/requests',
                },
                {
                    name: 'pdf-sign-requests',
                    path: '/pdf-sign-task',
                },
                {
                    name: 'manager',
                    path: '/manage',
                },
            ],
        };
    },
    watch: {
        updatedItem: {
            handler() {
                if (this.updatedItem.path.includes('manage')) {
                    this.menuItems.find(item => item.name === 'manager').path = this.updatedItem.path;
                } else {
                    this.menuItems.forEach(item => {
                        if (item.name === this.updatedItem.name) {
                            item.path = this.updatedItem.path;
                        }
                    });
                }

            },
            deep: true,
        },
    },
};
</script>

<template>
    <Popover v-model:visible="visible" type="default-no-padding" appendTo="self" :no-style="true" style="display: flex; width: 40px; justify-content: center;">
        <template #trigger>
            <button
                class="action-container app-switcher-button"
                @click="visible=!visible"
                :title="$t('menus.top.app_switcher')"
            >
                <Indicator :visible="showNotificationIndicator">
                    <Icon class="action-icon" :size="18" :name="user.can.is_external ? 'Inbox' : 'Grip'"/>
                </Indicator>
            </button>
        </template>

        <div class="container">
            <div class="items">
                <AppSwitcherItem
                    v-if="user.can.is_external"
                    :title="$t('app_switcher.hix_login.client.title')"
                    :description="$t('app_switcher.hix_login.client.description')"
                    icon="/images/icons/hix-login.svg"
                    route="/app/portal/hix_login"
                />

                <AppSwitcherItem
                    v-else
                    :title="$t('app_switcher.hix_login.title')"
                    :description="$t('app_switcher.hix_login.description')"
                    icon="/images/icons/hix-login.svg"
                    route="/app/portal/hix_login"
                />

                <template v-if="user.can.use_declarations">
                    <AppSwitcherItem
                        v-if="user.can.is_external && account.licenses.includes('new_ui_2024') && account.licenses.includes('new_ui_client_2024')"
                        :title="$t('app_switcher.hix_docs.client.title')"
                        :description="$t('app_switcher.hix_docs.client.description')"
                        icon="/images/icons/hix-docs.svg"
                        route="/app/front/docs"
                    />
                    <AppSwitcherItem
                        v-else-if="user.can.is_external"
                        :title="$t('app_switcher.hix_docs.client.title')"
                        :description="$t('app_switcher.hix_docs.client.description')"
                        icon="/images/icons/hix-docs.svg"
                        :route="menuItems[0].path"
                    />
                    <AppSwitcherItem
                        v-else
                        :title="$t('app_switcher.hix_docs.title')"
                        :description="$t('app_switcher.hix_docs.description')"
                        icon="/images/icons/hix-docs.svg"
                        :route="menuItems[1].path"
                    />
                </template>

                <template v-if="user.can.use_open_questions">
                    <AppSwitcherItem
                        v-if="user.can.is_external && account.licenses.includes('new_ui_2024') && account.licenses.includes('new_ui_client_2024')"
                        :title="$t('app_switcher.hix_questions.client.title')"
                        :description="$t('app_switcher.hix_questions.client.description')"
                        icon="/images/icons/hix-questions.svg"
                        route="/app/front/questions"
                    />
                    <AppSwitcherItem
                        v-else-if="user.can.is_external"
                        :title="$t('app_switcher.hix_questions.client.title')"
                        :description="$t('app_switcher.hix_questions.client.description')"
                        icon="/images/icons/hix-questions.svg"
                        :route="menuItems[2].path"
                    />

                    <AppSwitcherItem
                        v-else
                        :title="$t('app_switcher.hix_questions.title')"
                        :description="$t('app_switcher.hix_questions.description')"
                        icon="/images/icons/hix-questions.svg"
                        :route="menuItems[2].path"
                    />
                </template>

                <AppSwitcherItem
                    v-if="user.can.is_external"
                    :title="$t('app_switcher.hix_dossiers.client.title')"
                    :description="$t('app_switcher.hix_dossiers.client.description')"
                    icon="/images/icons/hix-dossiers.svg"
                    :route="menuItems[3].path"
                />

                <AppSwitcherItem
                    v-if="allRequests && allRequests.length > 0"
                    :title="$t('app_switcher.hix_signing.title_task')"
                    :description="$t('app_switcher.hix_signing.description')"
                    icon="/images/icons/hix-signing.svg"
                    :route="menuItems[4].path"
                />
                <AppSwitcherItem
                    v-if="pdfSignRequests && pdfSignRequests.length > 0"
                    :title="$t('app_switcher.hix_signing.title_pdf')"
                    :description="$t('app_switcher.hix_signing.description')"
                    icon="/images/icons/hix-signing.svg"
                    :route="menuItems[5].path"
                />
            </div>

            <div v-if="!isManagerPortal && (this.user.can.is_manager || this.user.can.manage_companies)" class="items">
                <AppSwitcherItem
                    type="secondary"
                    :title="$t('app_switcher.manager.title')"
                    :description="$t('app_switcher.manager.description')"
                    icon="Wrench"
                    :route="menuItems[6].path"
                />
            </div>
        </div>
    </Popover>
</template>

<style lang="scss" scoped>
@import "../../sass/initial-variables";

.container {
    padding: 6px 0;

    .items:first-child {
        border: none;
        padding-top: 0;
    }

    .items:last-child {
        padding-bottom: 0;
    }

    .items {
        border-top: 1px solid $gray-300;
        padding: 6px 0;
    }

    .action-container {
        display: flex;
        position: relative;
        border: none;
        cursor: pointer;
        background: transparent;
        width: 40px;
        padding: 0;
        justify-content: center;
    }

    .secondary {
        .item-icon-container {
            background: transparent;
            border: 1px solid $gray-300;
            color: $gray-900 !important;
        }
    }
}

button.app-switcher-button {
    border: none;
    background: none;
    cursor: pointer;
    background: transparent;
}
</style>