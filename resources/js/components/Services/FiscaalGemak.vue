<template>
    <div class="columns">
        <div class="column is-6">
            <Loader :is-loading="loading" :text="$t('common.loading')">
                <Label :text="$t('service.service_provider.fiscaal_gemak_service_provider.configure_title')" type="label-border"/>
                <br>

                <!--div v-if="!loading && meResponse !== undefined">
                    <div v-if="meResponse === null">
                        <Notification type="is-warning"
                                      :text="$t('service.service_provider.fiscaal_gemak_service_provider.not_authorized')"/>
                    </div>
                    <div v-else>
                        <Notification type="success-notification"
                                      :text="$t('service.service_provider.fiscaal_gemak_service_provider.already_authorized')"/>
                    </div>
                    <form @submit="configure">
                        <br>
                        <div v-if="meResponse === null || configuring === true">
                            <p>{{ $t('service.service_provider.fiscaal_gemak_service_provider.copy_instructions') }}</p>
                            <LabelCopy
                                :label="$t('service.service_provider.fiscaal_gemak_service_provider.fields.configuration_url')"
                                :value="account_service.properties.connection_url"/>
                            <br>
                            <p class="intro">
                                {{ $t('service.service_provider.fiscaal_gemak_service_provider.configure_intro') }}</p>
                            <FormGenerator v-model:schema="schema_configuration" v-model:value="formDataConfiguration" />
                            <br>
                        </div>
                        <button v-if="meResponse === null" class="is-info button">{{ $t('common.configure') }}</button>
                        <button v-else class="is-info button" @click="reConfigure">{{ $t('common.reconfigure') }}</button>
                        <button class="button is-danger pull-right" @click="removeService">{{ $t('common.delete') }}
                        </button>
                    </form>
                </div-->

                <div v-if="!loading && meResponse !== undefined">
                    <div v-if="meResponse === null || account_service.properties.oauth">
                        <p class="intro">{{$t('service.service_provider.fiscaal_gemak_service_provider.configure_intro_oauth')}}</p>
                        <button class="button is-info" @click="configureOauth">{{$t('common.configure')}}</button>
                        <button class="button is-danger pull-right" @click="removeService">{{ $t('common.delete') }}</button>
                    </div>
                    <div v-else>
                        <Notification type="warning"
                                      :text="$t('service.service_provider.fiscaal_gemak_service_provider.configure_warning_oauth')"/>
                        <button class="button is-info" @click="configureOauth">{{$t('common.configure')}}</button>
                        <button class="button is-danger pull-right" @click="removeService">{{ $t('common.delete') }}</button>
                        <br><br><br>
                        <Notification type="success-notification"
                                      :text="$t('service.service_provider.fiscaal_gemak_service_provider.already_authorized')"/>
                    </div>
                    <form @submit="configure">
                        <br>
                        <div v-if="configuring === true">
                            <p>{{ $t('service.service_provider.fiscaal_gemak_service_provider.copy_instructions') }}</p>
                            <LabelCopy
                                :label="$t('service.service_provider.fiscaal_gemak_service_provider.fields.configuration_url')"
                                :value="account_service.properties.connection_url"/>
                            <br>
                            <p class="intro">
                                {{ $t('service.service_provider.fiscaal_gemak_service_provider.configure_intro') }}</p>
                            <FormGenerator v-model:schema="schema_configuration" v-model:value="formDataConfiguration" />
                            <br>
                            <button class="button is-info" @click="configure">{{$t('common.configure')}}</button>
                        </div>
                        <button v-else-if="meResponse!== null && !account_service.properties.oauth" class="is-info button" @click="reConfigure">
                            {{ $t('common.reconfigure') }}
                        </button>
                    </form>
                </div>
            </Loader>
        </div>
        <div class="column is-6">
            <Label :text="$t('common.preferences')" type="label-border"/>
            <br>
            <FormGenerator v-model:schema="schema" v-model:value="formData"/>
            <br>
            <button @click="savePreferences" class="button is-info">{{ $t('common.save') }}</button>
        </div>
    </div>

</template>

<script>
import FiscaalGemakApi from "../../api/services/declarations/FiscaalGemakApi";
import Notification from "../Forms/Notification";
import Loader from "../Loader";
import store from "../../store";
import FormGenerator from "../Forms/FormGenerator";
import Label from "../Forms/Label";
import FormSubmitHandler from '../../mixins/FormSubmitHandler';
import LabelCopy from '../Forms/LabelCopy';
import {mapActions} from 'vuex';
import Form from "../CommunicationWidgets/Form";

export default {
    name: 'FiscaalGemak',
    components: {Form, Label, FormGenerator, Loader, Notification, LabelCopy},
    mixins: [FormSubmitHandler],
    props: [
        'account_service'
    ],
    data() {
        return {
            urlOptions: ['app.fiscaalgemak.nl', 'app2.fiscaalgemak.nl', 'app3.fiscaalgemak.nl'],
            meResponse: undefined,
            configuring: false,
            loading: true,
            formData: {},
            formDataConfiguration: {},
            schema_configuration: {
                url: {
                    fieldType: 'SingleSelect',
                    name: 'url',
                    label: this.$t('service.service_provider.fiscaal_gemak_service_provider.fields.url'),
                    options: {
                        'app.fiscaalgemak.nl': 'app.fiscaalgemak.nl',
                        'app2.fiscaalgemak.nl': 'app2.fiscaalgemak.nl',
                        'app3.fiscaalgemak.nl': 'app3.fiscaalgemak.nl'
                    },
                    default: this.account_service.properties && this.account_service.properties.app_url ? this.account_service.properties.app_url.replace('https://', '') : '0',
                    visibility: true,
                    required: true,
                },
                apiKey: {
                    fieldType: 'Input',
                    type: 'text',
                    placeholder: this.$t('service.service_provider.fiscaal_gemak_service_provider.fields.api_key'),
                    label: this.$t('service.service_provider.fiscaal_gemak_service_provider.fields.api_key'),
                    required: true,
                    name: 'api_key',
                    visibility: true,
                },
            },
            schema: {
                all_users_permission: {
                    fieldType: 'SingleSelect',
                    name: 'all_users_permission',
                    label: this.$t('service.all_users_permission_label'),
                    options: this.account_service.all_users_permission_options,
                    default: this.account_service.properties && this.account_service.properties.all_users_permission ? this.account_service.properties.all_users_permission : 'none',
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager
                },
                logius_connection: {
                    fieldType: 'SingleSelect',
                    name: 'logius_connection',
                    label: this.$t('service.logius_connection_label'),
                    options: this.account_service.logius_options,
                    default: this.account_service.properties && this.account_service.properties.logius_connection ? this.account_service.properties.logius_connection : 'none',
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager
                },
                approval_preferences: {
                    fieldType: 'SingleSelect',
                    name: 'approval_preferences',
                    label: this.$t('service.approval_preferences_label'),
                    options: this.account_service.approval_preferences,
                    default: this.account_service.properties && this.account_service.properties.approval_preferences ? this.account_service.properties.approval_preferences : 'all',
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager
                },
                send_reminders_label: {
                    fieldType: 'Label',
                    text: this.$t('service.send_reminders_preference_label'),
                    visibility: true,
                },
                send_reminders: {
                    fieldType: 'Switch',
                    id: 'send_reminders',
                    label: this.$t('service.send_reminders_preference_switch'),
                    checked: this.account_service.properties && this.account_service.properties.send_reminders,
                    default: this.account_service.properties && this.account_service.properties.send_reminders,
                    name: 'send_reminders',
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager,
                },
                render_xbrl_label: {
                    fieldType: 'Label',
                    text: this.$t('service.render_xbrl_label'),
                    visibility: true,
                },
                render_xbrl: {
                    fieldType: 'Switch',
                    id: 'render_xbrl',
                    name: 'render_xbrl',
                    label: this.$t('service.render_xbrl'),
                    checked: this.account_service.properties && this.account_service.properties.render_xbrl,
                    default: false,
                    visibility: true,
                },
                automated_sending_label: {
                    fieldType: 'Label',
                    text: this.$t('service.automated_sending_preference_label'),
                    visibility: true,
                },
                automated_sending: {
                    fieldType: 'MultipleSelect',
                    name: 'automated_sending',
                    trackBy: "key",
                    label: "name",
                    multiple: true,
                    options: this.account_service.task_types,
                    default: this.account_service.properties && this.account_service.properties.automated_sending ? this.account_service.properties.automated_sending : [],
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager
                },
                ignore_unmatched_files_label: {
                    fieldType: 'Label',
                    text: this.$t('service.ignore_unmatched_files_label'),
                    visibility: true,
                },
                ignore_unmatched_files: {
                    fieldType: 'Switch',
                    id: 'ignore_unmatched_files',
                    label: this.$t('service.ignore_unmatched_files_switch'),
                    checked: this.account_service.ignore_unmatched_files && this.account_service.properties.ignore_unmatched_files,
                    default: this.account_service.ignore_unmatched_files && this.account_service.properties.ignore_unmatched_files,
                    name: 'ignore_unmatched_files',
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager,
                },
            }
        };
    },
    created() {
        this.loading = true;
        this.axios.get('/service/' + this.account_service.id + '/fiscaal_gemak/me').then((response) => {
            this.meResponse = response.data.data;
        }).finally(() => {
            this.loading = false;
        });
    },
    methods: {
        ...mapActions({
            toggleEnabled: 'services/toggleEnabled',
            deleteAccountService: 'services/deleteAccountService',
        }),
        getAdoptionDateReminderOptions() {
            let options = [{'value': 1, 'label': this.$t('service.1_day')}];

            for(let i = 2; i <= 14; i++) {
                options.push({'value': i, 'label': this.$t('service.x_days', {'day': i})});
            }
            return options;
        },
        configure(event) {
            this.loading = true;
            let url = this.formDataConfiguration.url;
            this.formDataConfiguration['app_url'] = "https://" + url;
            this.formDataConfiguration['api_url'] = "https://api-" + url;

            this.checkSubmit(event, () => this.axios.post('/services/' + this.account_service.id + '/fiscaal_gemak/authorize', this.formDataConfiguration).then((response) => {
                this.meResponse = response.data;
                if (this.meResponse) {
                    this.toggleEnabled({service: this.account_service, enabled: true});
                    this.configuring = false;
                }
            }).finally(() => {
                this.loading = false;
            }));
        },
        reConfigure(event) {
            event.preventDefault();
            if (this.configuring === false) {
                this.configuring = true;
            } else {
                this.configure(event);
            }
        },
        removeService(event) {
            event.preventDefault();
            let self = this;
            this.$swal({
                title: this.$t('service.service_provider.fiscaal_gemak_service_provider.ib_vpb_declaration.delete_title'),
                html: this.$t('service.service_provider.fiscaal_gemak_service_provider.ib_vpb_declaration.delete_message'),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#F30F2A',
                confirmButtonText: this.$t('common.delete'),
            }).then((result) => {
                if (result.value) {
                    self.deleteAccountService(self.account_service.id);
                }
            })
        },
        savePreferences() {
            let payload = { ...this.formData };
            this.$emit('update-preferences', payload);
        },
        configureOauth() {
            FiscaalGemakApi.configure(this.account_service.id).then((response) => {
                if (response.data.data) {
                    window.location.replace(response.data.data);
                }
            });
        }
    }

}
</script>

<style lang="scss" scoped>
@import '../../../sass/initial-variables';

.intro {
    white-space: pre-line;
}

.success-notification {
    background-color: $green;
}

.label-border {
    border-bottom: solid 1px $black-03;
    padding-bottom: 20px;
}

</style>