<template>
    <div>
        <div id="login-container">
            <h1 class="has-text-centered">{{$t('common.welcome')}}</h1>
            <div v-if="data.post_auth_actions && (data.identity_linking || data.onboarding_enabled)" v-for="(post_auth_data, post_auth_key) in data.post_auth_actions">
                <div class="login-notification" v-if="post_auth_key === 'auth.fragments.login_with_identity_attribute'">
                    {{$t('auth.login_with_identity_attribute')}}
                    <div class="identity-row">
                        <div><strong>{{$t('auth.attr_issuer')}}:</strong></div>
                        <div>{{post_auth_data.attr_issuer}}</div>
                    </div>
                    <div class="identity-row">
                        <div><strong>{{$t('auth.attr_subject')}}:</strong></div>
                        <div>{{post_auth_data.attr_subject}}</div>
                    </div>
                    <div class="identity-row">
                        <div><strong>{{$t('auth.attr_name')}}:</strong></div>
                        <div>{{post_auth_data.attr_name}}</div>
                    </div>
                    <div class="identity-row">
                        <div><strong>{{$t('auth.attr_email')}}:</strong></div>
                        <div>{{post_auth_data.attr_email}}</div>
                    </div>
                </div>
                <div class="login-notification" v-else-if="post_auth_key === 'auth.fragments.login_with_auth_recovery'">
                    {{$t('auth.login_with_auth_recovery', {auth_id: post_auth_data.user.auth_id})}}
                </div>
                <div class="login-notification" v-else-if="post_auth_key === 'auth.fragments.login_with_saml_request'">
                    {{$t('auth.login_with_saml_request', {saml_sp: post_auth_data.saml_sp})}}
                </div>
            </div>
            <form v-if="data.login_enabled || (data.post_auth_actions && data.identity_linking)" method="post" @submit="checkFieldsForSubmit" :action="getFormAction()">
                <div class="input-fields">
                    <input type="hidden" name="_token" value="">
                    <input name="login_type" type="hidden" value="default_login" />
                    <Input v-model:value="auth_id" :label="$t('common.username')" name="auth_id" id="auth_id" type="text" :autocomplete="this.data.account.login_autocomplete ? 'on' : 'off'" :maxlength="80" allowAutofill/>
                    <Input v-model:value="auth_secret" :label="$t('common.password')" name="auth_secret" id="auth_secret" type="password" :autocomplete="this.data.account.login_autocomplete ? 'on' : 'off'" allowAutofill/>
                </div>
                <div class="remember-me" v-if="data.account.settings['remember_me']">
                    <Checkbox @updateCheck="updateRememberMe" :label="$t('common.remember_me')" name="remember_password" :checked="data.remember_me === '1'"/>
                </div>
                <div class="login-buttons">
                    <button id="login-button" class="button is-info" type="submit">{{$t('auth.login')}}</button>
                    <a href="/auth/forgot" class="forgot-password">{{$t('auth.forgot_secret_button')}}</a>
                </div>
            </form>
            <div class="has-text-centered" v-if="data.login_enabled && data.post_auth_actions && data.onboarding_enabled">
                <strong>-- {{$t('common.or')}} --</strong>
            </div>
            <div class="onboard-buttons" v-if="data.post_auth_actions && data.onboarding_enabled">
                <div class="has-text-centered">
                    <button class="button is-info" @click="onboard">{{$t('auth.onboard')}}</button>
                </div>
            </div>
            <Notification v-if="data.post_auth_actions && data.post_auth_actions['auth.fragments.login_with_identity_attribute'] && !data.identity_linking && !data.onboarding_enabled" :text="$t('auth.no_identity_linking_options')" type="is-warning"/>
            <p v-if="!data.login_enabled && !data.post_auth_actions" class="has-text-centered">{{$t('auth.choose_external_methods')}}</p>
        </div>

        <footer class="login-footer">
            <div class="external-options" v-if="data.issuers.length && (!data.post_auth_actions || data.post_auth_actions['auth.fragments.login_with_auth_recovery'])">
                <p>{{$t('auth.external_login_options')}}</p>
                <div class="buttons">
                    <a v-for="issuer in data.issuers" :href="getIssuerHref(issuer)">
                        <img v-if="issuer.login_button_icon && imageExists('/images/issuers/' + issuer.login_button_icon + '.png')" :src="'/images/issuers/' + issuer.login_button_icon + '.png'" :title="issuer.login_button_title">
                        <img v-else-if="issuer.protocol && imageExists('/images/issuers/' + issuer.protocol + '.png')" :src="'/images/issuers/' + issuer.protocol + '.png'" :title="issuer.login_button_title">
                        <button v-else class="button is-info">{{issuer.login_button_title}}</button>
                    </a>
                </div>
            </div>

            <span v-if="cookieCheck" class="is-pulled-left"><i :class="cookieCheck.succeed ? 'sl-icon-checkmark' : 'sl-icon-close-cross'" /><span v-if="!cookieCheck.succeed">{{cookieCheck.summary}}</span></span>
            <span class="is-pulled-right">IP: {{data.ip}}</span>
        </footer>
    </div>
</template>

<script>
    import Checkbox from "../Checkbox";
    import FormSubmitHandler from "../../mixins/FormSubmitHandler";
    import Notification from "../Forms/Notification";
    import Input from "../Forms/Input";

    export default {
        name: "Login",
        mixins: [FormSubmitHandler],
        components: {
            Notification,
            Checkbox,
            Input
        },
        props: {
            data: null
        },
        created() {
            this.checkCompatibility();
            this.startCSRFupdater();
        },
        mounted() {
            this.showMessages();
            document.getElementById('auth_id').focus();
            this.tokenMetaTag = document.querySelector('meta[name="csrf-token"]');
            this.tokenInputTag = document.querySelector('#login-container form input[name="_token"]');
            this.tokenInputTag.setAttribute('value', this.tokenMetaTag.getAttribute('content'));
        },
        data() {
            return {
                formData: {
                    'login_type': this.data.account.default_login_enabled ? 'default_login' : null
                },
                auth_id: '',
                auth_secret: '',
                rememberMe: this.data.remember_me,
                cookieCheck: null,
                lastCSRFfetchDate: new Date(),
                tokenInputTag: null,
                tokenMetaTag: null
            };
        },
        methods: {
            onboard() {
                window.location.href = this.data.onboarding_url;
            },
            getIssuerHref(issuer) {
                let url = '/idp/' + issuer.id + '/auth';
                if(this.data.scenario_key) {
                    url += '/' + this.data.scenario_key;
                }
                return url;
            },
            getFormAction() {
                if(this.data.scenario_key) {
                    return '/auth/login/' + this.data.scenario_key;
                } else {
                    return '/auth/login';
                }
            },
            showMessages() {
                if(this.data.succeed_msg) {
                    this.$notify({
                        group: 'general',
                        type: 'success',
                        title: this.$t('auth.succeed_title'),
                        text: this.data.succeed_msg,
                    });
                }
                if(this.data.failed_msg) {
                    this.$notify({
                        group: 'general',
                        type: 'error',
                        title: this.$t('auth.failed_title'),
                        text: this.data.failed_msg,
                    });
                }
                if(this.data.errors) {
                    this.$notify({
                        group: 'general',
                        type: 'error',
                        title: this.$t('auth.errors_title'),
                        text: this.data.errors[0]
                    });
                }
            },
            checkCompatibility(){
                this.axios.get('/auth/check_compatibility')
                .then((response) => {
                    this.cookieCheck = response.data;
                });
            },
            imageExists(image_url){
                let http = new XMLHttpRequest();
                http.open('HEAD', image_url, false);
                http.send();
                return http.status !== 404;
            },
            updateRememberMe(event, value){
                this.rememberMe = value;
            },
            startCSRFupdater() {
                let obj = this;
                setInterval(function() {
                    if (new Date().getTime() - obj.lastCSRFfetchDate.getTime() >= 15 * 60000) {
                        obj.lastCSRFfetchDate = new Date();
                        obj.refreshCSRF();
                    }
                }, 1000);
            },
            refreshCSRF() {
                this.axios.get('/auth/token').then((response) => {
                    this.updateCSRF(response.data);
                });
            },
            updateCSRF(token) {
                this.tokenMetaTag.setAttribute('content', token);
                this.tokenInputTag.setAttribute('value',  token);
            },
            checkFieldsForSubmit(event) {
                let message = '';
                if(!this.auth_id) {
                    message += this.$t('auth.username_required')+'<br>';
                }
                if(!this.auth_secret) {
                    message += this.$t('auth.password_required');
                }

                if(message) {
                    event.preventDefault();
                    this.$notify({
                        group: 'general',
                        type: 'error',
                        title: this.$t('auth.validation_failed'),
                        text: message
                    });
                }
            }
        }
    }
</script>

<style lang="scss" scoped>

    @import '../../../sass/initial-variables';

    #login-container {
        margin-left: 80px;
        margin-right: 80px;

        .input-fields {
            margin-bottom: 20px;
        }

        .login-buttons {
            margin-top: 20px;
            margin-bottom: 40px;
            display: flex;
            justify-content: space-between;

            #login-button {
                min-width: 120px;
            }

            .forgot-password {
                display: inline-block;
                height: 46px;
                padding: 17px;
                font-size: 14px;
                line-height: 14px;
            }
        }

        .onboard-buttons {
            margin-top: 20px;
            margin-bottom: 40px;
        }
    }

    .login-footer {
        border-top: 1px solid $black-10;
        background-color: $black-03;
        overflow: hidden;
        color: $black-40;
        border-radius: 0 0 5px 5px;
        font-size: 12px;
        padding: 10px 13px;
        margin-bottom: -20px;

        img {
            height: 32px;
        }

        .buttons * {
            margin: 0 5px 0 0;
            max-height: 32px;

            .button {
                padding: 10px;
            }
        }
    }

    .external-options {
        margin-bottom: 16px;
        p {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 6px;
        }
    }

    @media screen and (max-width: 480px) {

        #login-container {
            margin-left: 20px;
            margin-right: 20px;
        }
    }

    .identity-row {
        display: flex;

        div:first-child {
            min-width: 30%;
        }
    }

    .login-notification {
        background-color: $black-03;
        padding: 10px;
        border: 0px;
        border-radius: 5px;
    }

</style>