<template>
    <div>
        <div id="sms-otp-container">
            <form method="post">
                <label>{{$t('auth.sms_otp_label')}}</label>
                <FormGenerator v-model:schema="schema" v-model:value="formData" ref="form" />
                <div class="button-container">
                    <button class="button is-info" type="submit">{{$t('auth.login')}}</button>
                </div>
            </form>
        </div>
    </div>
</template>

<script>
    import FormGenerator from "../Forms/FormGenerator";
    import FormSubmitHandler from "../../mixins/FormSubmitHandler";
    import Notification from "../Forms/Notification";

    export default {
        name: "SmsOtp",
        mixins: [FormSubmitHandler],
        components: {
            Notification,
            FormGenerator
        },
        props: {
            data: null,
        },
        mounted() {
            this.showMessages();
        },
        data() {
            return {
                formData: {},
                schema: {
                    _token: {
                        fieldType: 'Input',
                        type: 'hidden',
                        name: '_token',
                        default: document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        visibility: true,
                    },
                    login_type: {
                        fieldType: 'Input',
                        type: 'hidden',
                        name: 'login_type',
                        default: this.data.login_type,
                        visibility: true,
                    },
                    login_ticket: {
                        fieldType: 'Input',
                        type: 'hidden',
                        name: 'login_ticket',
                        default: this.data.login_ticket,
                        visibility: true,
                    },
                    login_otp: {
                        fieldType: 'Input',
                        type: 'number',
                        name: 'login_otp',
                        label: this.$t('security_settings.code'),
                        visibility: true,
                        maxlength: 6,
                        autofocus: true,
                    }
                },
            }
        },
        methods: {
            getFormAction() {
                if(this.data.scenario_key) {
                    return '/auth/sms_otp/' + this.data.scenario_key;
                } else {
                    return '/auth/sms_otp';
                }
            },
            showMessages() {
                if(this.data.failed_msg) {
                    this.$notify({
                        group: 'general',
                        type: 'error',
                        title: this.$t('auth.failed_title'),
                        text: this.data.failed_msg,
                    });
                }
                if (this.data.errors) {

                    // Only shows the first error.
                    this.$notify({
                        group: 'general',
                        type: 'error',
                        title: this.$t('auth.errors_title'),
                        text: this.data.errors[0]
                    });
                }
            }
        }
    }
</script>

<style lang="scss" scoped>

    @import '../../../sass/initial-variables';

    #sms-otp-container {
        margin-left: 80px;
        margin-right: 80px;
        padding-bottom: 40px;

        form {
            .button-container {
                margin-top: 20px;

                button[type='submit'] {
                    min-width: 120px;
                }
            }
        }
    }
</style>
