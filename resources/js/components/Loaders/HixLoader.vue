<script>
export default {
    name: "Hix<PERSON>oader"
}
</script>

<template>
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="200px" height="200px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
        <g transform="translate(67,50)">
            <g transform="rotate(0)">
                <circle cx="0" cy="0" r="1" fill="#FE9FD1" fill-opacity="1">
                    <animateTransform attributeName="transform" type="scale" begin="-1.75s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                    <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-1.75s"/>
                </circle>
            </g>
        </g><g transform="translate(62,62)">
        <g transform="rotate(45)">
            <circle cx="0" cy="0" r="1" fill="#FCABB8" fill-opacity="0.875">
                <animateTransform attributeName="transform" type="scale" begin="-1.5s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-1.5s"/>
            </circle>
        </g>
    </g><g transform="translate(50,67)">
        <g transform="rotate(90)">
            <circle cx="0" cy="0" r="1" fill="#FBB1AB" fill-opacity="0.75">
                <animateTransform attributeName="transform" type="scale" begin="-1.25s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-1.25s"/>
            </circle>
        </g>
    </g><g transform="translate(38,62)">
        <g transform="rotate(135)">
            <circle cx="0" cy="0" r="1" fill="#FDB79A" fill-opacity="0.625">
                <animateTransform attributeName="transform" type="scale" begin="-1s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-1s"/>
            </circle>
        </g>
    </g><g transform="translate(33,50)">
        <g transform="rotate(180)">
            <circle cx="0" cy="0" r="1" fill="#FBBD8F" fill-opacity="0.5">
                <animateTransform attributeName="transform" type="scale" begin="-0.75s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-0.75s"/>
            </circle>
        </g>
    </g><g transform="translate(38,38)">
        <g transform="rotate(225)">
            <circle cx="0" cy="0" r="1" fill="#F9BF86" fill-opacity="0.375">
                <animateTransform attributeName="transform" type="scale" begin="-0.5s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-0.5s"/>
            </circle>
        </g>
    </g><g transform="translate(50,33)">
        <g transform="rotate(270)">
            <circle cx="0" cy="0" r="1" fill="#F8C37B" fill-opacity="0.25">
                <animateTransform attributeName="transform" type="scale" begin="-0.25s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="-0.25s"/>
            </circle>
        </g>
    </g><g transform="translate(62,38)">
        <g transform="rotate(315)">
            <circle cx="0" cy="0" r="1" fill="#F8C772" fill-opacity="0.125">
                <animateTransform attributeName="transform" type="scale" begin="0s" values="4 4;1 1" keyTimes="0;1" dur="2s" repeatCount="indefinite"/>
                <animate attributeName="fill-opacity" keyTimes="0;1" dur="2s" repeatCount="indefinite" values="1;0" begin="0s"/>
            </circle>
        </g>
    </g>
    </svg>
</template>
