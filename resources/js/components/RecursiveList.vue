<template>
    <div :class="['depth-' + depth, 'show-underline', {'is-child': depth > 0}]" v-if="hideMenuItem">
        <div class="item columns" @click="clickHandler">
            <div class="text">
                <RouterLink class="router-link" v-bind:class="{ 'router-link-exact-active active': isActive() }"
                             v-if="!node.subgroups || node.subgroups.length === 0" :to="node.to"
                             :style="{'padding-left': (14 * (depth+1)) + 'px !important'}">
                    {{ node.title }}
                    <div v-if="node.warning_tooltip" :title="node.warning_tooltip" class="warning-container">
                        <span class="sl-icon-warning"></span>
                    </div>
                </RouterLink>
                <a v-else>
                    {{ node.title }}
                </a>
            </div>

            <div class="collapse-btn column" v-if="node.subgroups && node.subgroups.length>0">
                <i class="fa is-pulled-right" :class="{ 'fa-angle-down': !isOpen, 'fa-angle-up': isOpen }"></i>
            </div>
        </div>
        <div class="children" v-show="isOpen">
            <RecursiveList
                :depth="depth + 1"
                :key="childIndex"
                :node="child"
                :nav-active="navActive"
                @change="checkItem" v-for="(child, childIndex) in node.subgroups"
                @membershipChanged="updateMembershipType(child.membership_id, child.can_upgrade)"
            />
        </div>
    </div>
</template>

<script>

export default {
    name: 'RecursiveList',
    props: {
        node: {
            type: Object
        },
        navActive: {
            type: String
        },
        depth: {
            type: Number,
            default: 0
        },
    },
    data() {
        return {
            isOpen: false,
            showAll: false,
        }
    },
    computed: {
        hideMenuItem() {
            return !this.node.meta || !this.node.meta.hide;
        }
    },
    methods: {
        isActive() {
            if (!this.node.meta) {
                return false;
            }
            return this.node.meta.navName === this.navActive;
        },
        clickHandler(event) {
            this.isOpen = !this.isOpen
        },
        checkItem(id, value) {
            this.$emit('change', id, value);
        },
        updateMembershipType(id, canUpgrade) {
            this.$emit('membershipChanged', id, canUpgrade);
        }
    },
    watch: {
        '$parent.showAll'(newValue) {
            this.showAll = newValue;
        },
        'showAll'(newValue) {
            this.isOpen = newValue;
        }
    }
}
</script>

<style lang="scss" scoped>

@import '../../sass/initial-variables';

.columns, .column {
    margin: 0;
    padding: 0;
}

.item {
    position: relative;

    :hover {
        cursor: pointer;
    }

    .text {
        position: relative;
        width: 100%;
        display: flex;
        height: 60px;
        justify-content: space-between;
        align-items: center;

        .title {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            height: 60px;
            padding: 23px 14px !important;
            line-height: 14px;
            font-size: 14px;
            font-weight: normal;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 60%;
        }
    }

    p {
        margin: 0;
    }

    .warning-container {
        .sl-icon-warning {
            color: $yellow;
        }
    }

    .collapse-btn {
        height: 60px;
        padding: 23px 14px !important;
        line-height: 14px;
        font-size: 14px;
        max-width: 37px;
        cursor: pointer;

        &:hover {
            background-color: $grey-lighter;
        }

        .collapse-arrow {
            font-weight: bold;
        }
    }
}

.show-underline {
    .item {
        border-bottom: 1px solid $black-10;
    }
}

.vertical-middle {
    vertical-align: middle;
}

a {
    display: block;
    font-weight: 500;
    color: $grey-darker;
    padding: 20px 14px;

    &.router-link-exact-active {
        color: $white;
        background-color: $blue;
    }
}

.router-link {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
}
</style>