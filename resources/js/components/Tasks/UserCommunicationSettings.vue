<template>
    <div>
        <Loader :is-loading="loading" :text="$t('common.loading')"/>
        <template v-if="!loading">
            <div class="sticky">
                <h1>{{ $t('communication_settings.title') }}</h1>
                <br/>
                <div class="notification">
                    {{ $t('communication_settings.information') }}
                </div>
                <div class="channel-grid">
                    <div class="channel-grid-header">
                        <strong>{{ $t('communication_settings.channels.title') }}</strong>
                    </div>
                    <div class="channel-grid-header channel-types">
                        <div v-for="availableChannel in availableChannels">
                            <img
                                class="channel-image"
                                :class="{disabled: !availableChannel.enabled}"
                                :title="$t('settings.communication.channels.channel.' + availableChannel.name)"
                                :alt="availableChannel.name"
                                :src="'/images/communication/channels/' + availableChannel.name + '.png'"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="accountSettings" class="channel-grid">
                <template v-for="subjectType in accountSettings">
                    <template v-for="subject in subjectType.subjects">
                        <div class="channel-grid-item">{{ subject.subject === 'question_reminders' ? $t('communication_settings.subjects.question_reminders') : subject.name }}</div>
                        <div class="channel-grid-item channel-types">
                            <template v-for="channel in subject.channels">
                                <RadioButton
                                    class="channel-radio-button"
                                    :name="subjectType.subject_type + '_' + (subject.subject ? subject.subject : subject.upload_type_id)"
                                    :options="parseChannel(channel, subject)"
                                    :inline="true"
                                    @input="(value) => updateSettings(subjectType.subject_type, subject, value)"
                                />
                            </template>
                        </div>
                    </template>
                </template>
            </div>
        </template>
    </div>
</template>

<script>

import RadioButton from "../Forms/RadioButton";
import FrontApi from "../../api/Front";
import Loader from "../Loader";
import CommunicationSettingsApi from "../../api/app/front/CommunicationSettingsApi";

export default {
    name: "UserCommunicationSettings",
    components: {
        Loader,
        RadioButton
    },
    data() {
        return {
            loading: true,
            availableChannels: [],
            accountSettings: [],
            userChannels: [],
        }
    },
    created() {
        this.getAvailableCommunicationSettings();
    },
    methods: {
        getAvailableCommunicationSettings() {
            CommunicationSettingsApi.settings().then((response) => {
                this.availableChannels = response.data.data.available_channels;
                this.accountSettings = response.data.data.account_settings;
                this.userChannels = response.data.data.user_channels;
            }).finally(() => {
                this.loading = false;
            });
        },
        updateSettings(subjectType, subject, channel) {
            CommunicationSettingsApi.update({
                subject_type: subjectType,
                subject: subject.subject,
                upload_type_id: subject.upload_type_id,
                channel: channel
            });
        },
        parseChannel(channel, subject) {
            let parsedChannels = [];
            let data = {
                value: channel.channel,
                checked: false,
                disabled: false
            };

            // disable channel if its not available
            if (!channel.is_enabled) {
                data.disabled = true;
            } else {
                let userChannelsForSubject = this.getUserChannelsForSubject(subject);
                if (userChannelsForSubject.length !== 0) {
                    data.checked = this.userHasChannel(userChannelsForSubject, channel.channel);
                } else {
                    data.checked = channel.is_default;
                }
                data.disabled = !channel.is_enabled;
            }

            parsedChannels.push(data);
            return parsedChannels;
        },
        getUserChannelsForSubject(subject) {
            return this.userChannels.filter(uc => uc.subject === subject.subject && uc.upload_type_id === subject.upload_type_id);
        },
        userHasChannel(userChannelsForSubject, channel) {
            return userChannelsForSubject.findIndex(u => u.channel === channel) !== -1;
        }
    }
}
</script>

<style scoped lang="scss">

@import '../../../sass/initial-variables';

.sticky {
    box-shadow: rgba(149, 157, 165, 0.2) 0px 10px 10px -10px;
    margin-bottom: 10px !important;
    padding-bottom: 10px;
    top: 40px !important;
    max-width: 800px;

    .notification {
        margin-bottom: 10px;
    }

    .channel-grid {
        margin-bottom: 0;

        .channel-grid-header {
            border-bottom: 0;
            padding-bottom: 0;
            margin-bottom: 0;
        }
    }
}

.channel-grid {
    max-width: 800px;
    display: grid;
    grid-template-columns: 2fr 1fr;
    padding-top: 10px;

    .channel-grid-header {
        border-bottom: 1px solid $black-10;
        padding-bottom: 5px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .channel-grid-item {
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid $black-03;
        display: flex;
        align-items: center;
    }

    .default-dropdown {
        width: 100%;
    }

    .channel-image {
        height: 28px;
        width: 28px;

        &.disabled {
            filter: grayscale(1);
            opacity: 0.4;
        }
    }

    .channel-radio-button {
        width: 16px;
        height: 16px;
        margin: 6px
    }

    .channel-types {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

</style>