<template>
    <div :class="item.visible ? '' : 'none'">
        <div v-if="item.children.length === 0 && forceCollapsible === false" class="parent">
            <ListItem :show-logo="showLogo"
                      :item="item.parent"
                      :visible="item.visible"
                      :selected="item.selected"
                      :todo-field="todoField"
                      @select-item="select"
                      @select-mount="handleSelectMount"
            />
        </div>
        <div v-else>
            <div class="parent">
                <div :class="'list-item-container ' + (item.collapsed ? 'active' : '')" @click="collapse">
                    <div class="list-item-container-child-left">
                        <Logo v-if="showLogo"
                              class="list-item-icon"
                              :image="item.parent.image"
                              :text="item.parent.title"
                              :background-color="item.parent.color"
                              :width="44"
                              :height="44"
                              :dot-count="item.parent[todoField]"
                        />
                        <div class="list-item-content">
                            <h2 class="list-item-title" :title="item.parent.title">{{ item.parent.title }}</h2>
                            <h3 class="list-item-subtitle" :title="item.parent.subtitle">{{ item.parent.subtitle }}</h3>
                            <h3 class="list-item-subtitle" v-if="item.parent.subsubtitle" :title="item.parent.subsubtitle">{{ item.parent.subsubtitle }}</h3>
                        </div>
                    </div>
                    <div :title="item.parent.warning_tooltip" class="warning-container">
                        <span :class="'simple-arrow ' + (item.collapsed ? 'sl-icon-simple-arrow-down' : 'sl-icon-simple-arrow-up')"></span>
                    </div>
                </div>
            </div>
            <div class="children">
                <ListItem v-for="(child, index) in item.children"
                          :key="index"
                          :show-logo="showLogo"
                          :item="child.item"
                          :visible="child.visible && item.collapsed"
                          :selected="child.selected"
                          :todo-field="todoField"
                          @select-item="select"
                          @select-mount="handleSelectMount"
                />
            </div>
        </div>
    </div>
</template>

<script>
import Logo from "../Logo"
import ListItem from './ScrollableListItem';

export default {
    name: 'CollapsibleListItem',
    components: {
        Logo,
        ListItem,
    },
    props: {
        item: Object,
        showLogo: true,
        todoField: String,
        forceCollapsible: false
    },
    methods: {
        collapse() {
            this.item.collapsed = !this.item.collapsed;
        },
        select(item) {
            this.$emit('select-item', item)
        },
        handleSelectMount(mount) {
            this.$emit('select-mount', mount);
        },
        getVisibleChildsLength(childs) {
            return childs.filter(child => child.visible === true).length;
        }
    }
}
</script>

<style lang="scss">

@import '../../../sass/initial-variables';

.list-item-container {
    height: 72px;
    position: relative;
    padding: 0 17px;
    align-items: center;
    display: flex;
    justify-content: space-between;

    &:hover {
        background-color: $hoverBg;
        cursor: pointer;
    }

    &.disabled {
        background-color: #f0f0f0;
        opacity: 0.5;
    }

    &.active {
        background-color: #E0E0E0;

        .list-item-container-child-left {
            .list-item-content {
                .list-item-subtitle {
                    color: $grey-dark;
                }
            }
        }
    }

    .list-item-container-child-left {
        display: flex;
        flex-direction: row;
        min-width: 0;
        align-items: center;

        .list-item-icon {
            vertical-align: top;
            margin-right: 17px;
        }

        .list-item-content {
            flex-direction: column;
            min-width: 0;

            h3, h2 {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .list-item-title {
                font-weight: bold;
                font-size: 14px;
                line-height: 24px;
            }

            .list-item-subtitle {
                font-size: 12px;
                line-height: 18px;
                color: #999999;
            }
        }
    }
}

.children {
    .list-item-container {
        background-color: #E9E9E9;

        .list-item-container-child-left {
            .list-item-content {
                .list-item-subtitle {
                    color: $grey-dark;
                }
            }
        }
    }
}

.none {

    display: none;

}

.badge-container {
    color: white;
    font-size: 12px;
    margin: auto 0 auto 10px;
    font-weight: bold;
    text-align: center;
}

.badge {
    display: inline-block;
    min-width: 18px;
    padding: 2px 2px 2px 1px;
    border-radius: 50%;
    font-size: 0.8em;
    background: #F30F2A;
}

.warning-container {
    display: flex;
    font-size: 18px;
}

.warning-color {
    color: $yellow;
}

.simple-arrow {
    padding-left: 10px;
}
</style>