<template>
    <div class="vue-flipcard horizontal is-unselectable" :class="[direction, { 'back': flipped }]" :style="{ 'width': width, 'height': height }">
        <div class="vue-flipcard__front">
            <slot name="front" />
        </div>
        <div class="vue-flipcard__back">
            <slot name="back" />
        </div>
    </div>
</template>

<script>
    export default {
        name: 'FlipCard',
        data () {
            return {
                back: false
            }
        },
        props: {
            width: {
                type: String,
                default: '300px'
            },
            height: {
                type: String,
                default: '300px'
            },
            disabled: {
                type: Boolean,
                default: false
            },
            flipped: {
                type: Boolean,
                default: false
            },
            direction: {
              type: String,
              default: 'horizontal'
            }
        },
        methods: {

        }
    }
</script>

<style lang="scss" scoped>
    .vue-flipcard {
        position: relative;

        vue-flipcard__front, vue-flipcard__back {
            transform-origin: center center 10px;
        }

        &__front, &__back {
            width: 100%;
            height: 100%;
            border-radius: 5px;
            overflow: hidden;
            position: absolute;
            perspective: 1000;
            transform-style: preserve-3d;
            transition: transform .6s;
            backface-visibility: hidden;
        }

        &__front {
            z-index: 2;
        }
        &.horizontal &__back {
            transform: rotateY(-180deg);
        }
        &.horizontal.back &__front {
            transform: rotateY(180deg);
        }
        &.horizontal.back &__back {
            transform: rotateY(0);
        }

        &.vertical &__back {
            transform: rotateX(-180deg);
        }
        &.vertical.back &__front {
            transform: rotateX(180deg);
        }
        &.vertical.back &__back {
            transform: rotateX(0);
        }
    }
</style>