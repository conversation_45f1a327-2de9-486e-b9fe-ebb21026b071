<script>
import {mapGetters, mapState} from 'vuex';

import Logo from './Logo.vue';
import Icon from './Icon.vue';
import Text from './Typography/Text.vue'
import Custom from './Custom.vue';
import AppSwitcher from './AppSwitcher.vue';
import Avatar from './Avatar.vue';
import Indicator from "./Indicator.vue";

export default {
    name: 'TopMenu',
    components: {
        Indicator,
        Logo,
        Text,
        AppSwitcher,
        Icon,
        Custom,
        Avatar
    },
    props: {
        updatedItem: Object,
        showLogo: {
            type: Boolean,
            default: true,
        },
        hasRibbon: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        ...mapState({
            pdfSignRequests: state => state.pdf_signing_requests.all,
            allRequests: state => state.service_task_signing_requests.all,
            isMobile: state => state.isMobile,
            isManagerPortal: state => state.isManagerPortal,
            account: state => state.account.current_account,
            unreadWhatsAppCount: state => state.account.unread_whatsapp_messages,
            user: state => state.user.profile,
            unreadCount: state => state.notifications.unread_count,
        }),
        ...mapGetters({
            currentAccount: 'account/getCurrent',
        }),
        currentModule() {
            if (!this.$route.meta.module) {
                return '';
            }
            return this.$t('common.modules.' + this.$route.meta.module);
        },
    }
}
</script>

<template>
    <div class="top-menu-container" :class="{'has-background': user, 'has-ribbon': hasRibbon}"
         :style="{'background-color': '$white'}">
        <div class="container-start">
            <Logo
                v-show="showLogo"
                :title="currentAccount.name"
                :image="currentAccount.logo_url ?? 'img/hix.png'"
                :is-circular="true"
                :height="40"
                :width="40"
            />
            <Text v-if="!isMobile" type="bold" v-text="currentAccount.name"/>
        </div>

        <Text class="module" v-if="isMobile" type="bold">{{ currentModule }}</Text>

        <div class="container-end" v-if="!$route.meta.hide_menu">
            <template v-if="!isMobile">
                <button @click="$emit('select', 'help');" style="padding-right: 4px;">
                    <Text type="menu">{{ $t('segmented_buttons.help') }}</Text>
                </button>

                <button
                    v-if="!user.can.is_external && user.can.use_whatsapp && user.can.has_companies"
                    class="action-container"
                    @click="$router.push({name: 'whatsapp-business'})"
                    :title="$t('menus.top.whatsapp')"
                >
                    <Indicator :visible="false">
                        <Custom class="action-icon" path="/images/icons/hix-whatsapp.svg" :size="18"/>
                    </Indicator>
                </button>

                <button class="action-container"
                        @click="$emit('select', 'notifications');"
                        :title="$t('menus.top.notifications')"
                >
                    <Indicator :visible="unreadCount !== 0">
                        <Icon class="action-icon" :size=18 name="Bell"/>
                    </Indicator>
                </button>
            </template>

            <AppSwitcher :updatedItem="updatedItem"/>
            <button class="action-container" @click="$emit('select', 'profile');">
                <Avatar :name="user.name" :title="$t('menus.top.profile')"/>
            </button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import "../../sass/initial-variables";

.button {
    cursor: pointer;
}

.top-menu-container {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: space-between;
    padding-left: 24px;
    padding-right: 10px;
    position: sticky;
    top: 0;
    z-index: 999;

    &.has-ribbon {
        top: 40px;
    }

    &.has-background {
        background: $white;
        border-bottom: 1px solid $gray-300;
    }
    
    font-family: 'Inter', sans-serif;
    text-rendering: auto !important;
    font-smooth: auto !important;
    webkit-font-smoothing: auto !important;
    -moz-osx-font-smoothing: auto !important;
}

.container-start {
    display: flex;
    align-items: center;
    gap: 16px;

    :deep(.search-container) input {
        width: 315px;
    }

    .back {
        margin-left: 8px;
    }

    .account-name {
        margin-left: 16px;
    }
}

.container-end {
    display: flex;
    align-items: center;

    button {
        background: inherit;
        border: none;

        &:hover {
            cursor: pointer;
        }
    }
}

.help {
    margin-right: 8px;
    font-size: 12pt;
}

.action-container {
    display: flex;
    position: relative;
    cursor: pointer;
    background: transparent;
    border: none;
    width: 40px;
    padding: 0;
    justify-content: center;
}

.action-icon {
    width: 18px;
    color: $gray-800;
}

@media (max-width: 768px) {
    .top-menu-container {
        padding: 0 16px;
    }
}

.module {
    margin: auto 0;
}
</style>