{"name": "securelogin", "license": "UNLICENSED", "private": true, "scripts": {"dev": "yarn run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --config=node_modules/laravel-mix/setup/webpack.config.js", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --https --progress --config=node_modules/laravel-mix/setup/webpack.config.js --key ./install/apache2/ssl.local.securelogin.nu/server.key --cert ./install/apache2/ssl.local.securelogin.nu/server.crt", "prod": "yarn run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --config=node_modules/laravel-mix/setup/webpack.config.js", "test": "mochapack --webpack-config webpack.config.js --require tests-js/setup.js tests-js/**/*.spec.js", "test:unit": "mochapack --webpack-config webpack.config.js --require tests-js/setup.js tests-js/Unit/**/*.spec.js", "test:watch": "mochapack --webpack-config webpack.config.js --watch --require tests-js/setup.js tests-js/**/*.spec.js", "test:cover": "cross-env NODE_ENV=coverage nyc --reporter=lcov --reporter=text yarn run test", "watch": "yarn run development --watch", "watch-poll": "yarn run watch --watch-poll"}, "dependencies": {"@ckpack/vue-color": "^1.4.1", "@datadog/browser-rum": "^4.30.0", "@dattn/dnd-grid": "^0.0.14", "@kyvg/vue3-notification": "^2.7.0", "@popperjs/core": "^2.11.6", "@sipec/vue3-tags-input": "^3.0.4", "@tato30/vue-pdf": "^1.9.7", "@tiptap/core": "^2.5.8", "@tiptap/extension-bold": "^2.5.8", "@tiptap/extension-document": "^2.5.8", "@tiptap/extension-hard-break": "^2.5.8", "@tiptap/extension-history": "^2.5.8", "@tiptap/extension-italic": "^2.5.8", "@tiptap/extension-link": "^2.5.8", "@tiptap/extension-paragraph": "^2.5.8", "@tiptap/extension-text": "^2.5.8", "@tiptap/pm": "^2.5.8", "@tiptap/vue-3": "^2.5.8", "@vuepic/vue-datepicker": "^8.8.0", "animate.css": "^4.1.1", "babel-loader": "^9.1.3", "buefy": "npm:@ntohq/buefy-next", "bulma": "^0.9.4", "bulma-calendar": "^6.1.18", "canvas-toBlob": "^1.0.0", "css-loader": "^6.7.3", "dayjs": "^1.11.10", "expect": "^29.3.1", "file-loader": "^6.2.0", "font-awesome": "^4.7.0", "full-icu": "^1.5.0", "highcharts": "^10.3.2", "hint.css": "^2.7.0", "interactjs": "^1.10.17", "jsdom": "^21.0.0", "jsdom-global": "^3.0.2", "laravel-echo": "^2.0.2", "laravel-mix": "^6.0.49", "lucide-vue-next": "^0.469.0", "pdfjs-dist": "3.4.120", "primevue": "^3.52.0", "promise": "^8.3.0", "pusher-js": "^8.4.0", "rrweb": "^2.0.0-alpha.4", "signature_pad": "^4.1.4", "vue-avatar-cropper": "^6.1.1", "vue-cookies": "^1.8.2", "vue-cropperjs": "^5.0.0", "vue-i18n": "^9.2.2", "vue-json-editor": "^1.4.3", "vue-loader": "^17.0.1", "vue-loading-overlay": "^6.0.2", "vue-masonry-css": "^1.0.3", "vue-multiselect": "^3.0.0-alpha.2", "vue-plyr": "^7.0.0", "vue-router": "^4.1.6", "vue-sidebar-menu": "^5.2.5", "vue-slicksort": "^2.0.3", "vue-slider-component": "^4.1.0-beta.7", "vue-treeselectjs": "^0.4.2", "vue-twitter": "^0.1.0", "vue-upload-component": "^3.1.8", "vue3-clipboard": "^1.0.0", "vue3-emoji-picker": "^1.1.7", "vue3-treeselect": "https://github.com/drummerroma/vue3-treeselect.git", "vuetify": "3", "vuex": "^4.1.0", "webpack": "^5.75.0", "yarn": "^1.22.19"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/preset-env": "^7.20.2", "@vue/compiler-sfc": "^3.2.45", "@vue/test-utils": "^2.2.7", "ajax-bootstrap-select": "^1.4.5", "axios": "^1.2.2", "bootstrap": "^5.2.3", "bootstrap-notify": "^3.1.3", "bootstrap-select": "^1.13.18", "bulma-extensions": "^6.2.7", "chai": "^4.3.7", "chart.js": "^4.1.2", "clipboard": "^2.0.11", "core-js": "^3.27.1", "cross-env": "^7.0.3", "desandro-matches-selector": "^2.0.2", "draggabilly": "^3.0.0", "ev-emitter": "^2.1.2", "expose-loader": "^4.0.0", "farbstastic": "^1.3.0", "fizzy-ui-utils": "^3.0.0", "get-size": "^3.0.0", "jasny-bootstrap": "^4.0.0", "jquery": "^3.6.3", "jquery-bootgrid": "^1.3.1", "jquery-form": "^4.3.0", "jquery-mousewheel": "^3.1.13", "jquery-ui": "^1.13.2", "jquery-ui-dist": "^1.13.2", "jquery-validation": "^1.19.5", "jquery.nicescroll": "^3.7.6", "jsoneditor": "^9.9.2", "laravel-mix-polyfill": "^3.0.1", "lodash": "^4.17.21", "malihu-custom-scrollbar-plugin": "^3.1.5", "masonry-layout": "^4.2.2", "material-design-iconic-font": "^2.2.0", "mediaelement": "^5.1.0", "mocha": "^10.2.0", "mochapack": "^2.1.4", "node-waves": "^0.7.6", "nouislider": "^15.6.1", "nyc": "^15.1.0", "outlayer": "^2.1.1", "plyr": "^3.7.3", "postcss": "^8.2.15", "resolve-url-loader": "^5.0.0", "roboto-fontface": "^0.10.0", "sass": "^1.57.1", "sass-loader": "^13.2.0", "stylus": "^0.59.0", "stylus-loader": "^7.1.0", "twitter-bootstrap-wizard": "^1.2.0", "unidragger": "^3.0.1", "unipointer": "^2.4.0", "vue": "^3.3.4", "vue-cli-plugin-vuetify": "^2.5.8", "vue-sweetalert2": "^5.0.5", "webpack-cli": "^4.9.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}