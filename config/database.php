<?php

return [

    /*
    |--------------------------------------------------------------------------
    | PDO Fetch Style
    |--------------------------------------------------------------------------
    |
    | By default, database results will be returned as instances of the PHP
    | stdClass object; however, you may desire to retrieve records in an
    | array format for simplicity. Here you can tweak the fetch style.
    |
    */

    'fetch' => PDO::FETCH_CLASS,

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver'   => 'sqlite',
            'database' => storage_path('database.sqlite'),
            'prefix'   => '',
        ],

        'mysql' => [
            'driver'    => 'mysql',
            'host'      => env('DB_HOST', 'localhost'),
            'database'  => env('DB_DATABASE', 'homestead'),
            'username'  => env('DB_USERNAME', 'homestead'),
            'password'  => env('DB_PASSWORD', ''),
            'prefix'    => '',
            'strict'    => env('DB_STRICT', true),
            'engine' => 'InnoDB'
        ],

        'mongodb' => [
            'driver' => 'mongodb',
            'host' => env('MONGODB_HOST', '127.0.0.1'),
            'port' => env('MONGODB_PORT', 27017),
            'database' => env('MONGODB_DATABASE', 'securelogin'),
            'username' => env('MONGODB_USERNAME'),
            'password' => env('MONGODB_PASSWORD'),
            'options' => [
                // here you can pass more settings to the Mongo Driver Manager
                // see https://www.php.net/manual/en/mongodb-driver-manager.construct.php
                // under "Uri Options" for a list of complete parameters that you can use
                 'database' => env('MONGODB_AUTHENTICATION_DATABASE', 'admin'), // required with Mongo 3+
            ],
        ],
        /* This config will be changed on the fly to connect to client databases per id.
        We don't want this to confuse any other connections on models other than events. so we keep it separate. */
        'mongodb-dynamic' => [
            'driver' => 'mongodb',
            'host' => env('MONGODB_DYNAMIC_HOST', '127.0.0.1'),
            'port' => env('MONGODB_DYNAMIC_PORT', 27017),
            'database' => 'events',
            'username' => env('MONGODB_DYNAMIC_USERNAME'),
            'password' => env('MONGODB_DYNAMIC_PASSWORD'),
            'options' => [
                'database' => env('MONGODB_DYNAMIC_AUTHENTICATION_DATABASE', 'admin'), // required with Mongo 3+
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'predis'),

        'default' => [
            'host'     => env('REDIS_HOST','127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6000),
            'database' => env('REDIS_DB', 0),
            'read_timeout' => 60,
        ],

        'cache' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6000),
            'database' => env('REDIS_CACHE_DB', 1),
        ],

        'session' => [
          'host' => env('REDIS_SESSION_HOST', '127.0.0.1'),
          'password' => env('REDIS_SESSION_PASSWORD', null),
          'port' => env('REDIS_SESSION_PORT', 6379),
          'database' => env('REDIS_SESSION_DB', 2),
        ],
    ],

];
