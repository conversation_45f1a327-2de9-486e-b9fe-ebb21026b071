-- MySQL dump 10.13  Distrib 8.0.35, for Linux (x86_64)
--
-- Host: mysql    Database: securelogin
-- ------------------------------------------------------
-- Server version	8.0.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `securelogin`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `securelogin` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `securelogin`;

--
-- Temporary view structure for view `acc_managers`
--

DROP TABLE IF EXISTS `acc_managers`;
/*!50001 DROP VIEW IF EXISTS `acc_managers`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `acc_managers` AS SELECT 
 1 AS `user_id`,
 1 AS `firstname`,
 1 AS `lastname`,
 1 AS `email`,
 1 AS `auth_method`,
 1 AS `created_at`,
 1 AS `last_login`,
 1 AS `account_id`,
 1 AS `account_name`,
 1 AS `account_type`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `acc_stats_history`
--

DROP TABLE IF EXISTS `acc_stats_history`;
/*!50001 DROP VIEW IF EXISTS `acc_stats_history`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `acc_stats_history` AS SELECT 
 1 AS `id`,
 1 AS `name`,
 1 AS `status`,
 1 AS `created_at`,
 1 AS `user_count m-9`,
 1 AS `active_user_count m-9`,
 1 AS `login_count m-9`,
 1 AS `user_count m-8`,
 1 AS `active_user_count m-8`,
 1 AS `login_count m-8`,
 1 AS `user_count m-7`,
 1 AS `active_user_count m-7`,
 1 AS `login_count m-7`,
 1 AS `user_count m-6`,
 1 AS `active_user_count m-6`,
 1 AS `login_count m-6`,
 1 AS `user_count m-5`,
 1 AS `active_user_count m-5`,
 1 AS `login_count m-5`,
 1 AS `user_count m-4`,
 1 AS `active_user_count m-4`,
 1 AS `login_count m-4`,
 1 AS `user_count m-3`,
 1 AS `active_user_count m-3`,
 1 AS `login_count m-3`,
 1 AS `user_count m-2`,
 1 AS `active_user_count m-2`,
 1 AS `login_count m-2`,
 1 AS `user_count m-1`,
 1 AS `active_user_count m-1`,
 1 AS `login_count m-1`,
 1 AS `user_count m(cached)`,
 1 AS `active_user_count m(cached)`,
 1 AS `login_count m(cached)`,
 1 AS `sms_count m-1`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `acc_stats_live`
--

DROP TABLE IF EXISTS `acc_stats_live`;
/*!50001 DROP VIEW IF EXISTS `acc_stats_live`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `acc_stats_live` AS SELECT 
 1 AS `id`,
 1 AS `name`,
 1 AS `status`,
 1 AS `type`,
 1 AS `created_at`,
 1 AS `users`,
 1 AS `activated`,
 1 AS `active`,
 1 AS `internal`,
 1 AS `internal_weekly`,
 1 AS `internal_5_widgets`,
 1 AS `internal_weekly_5_widgets`,
 1 AS `external`,
 1 AS `external_activated`,
 1 AS `users_with_sms_otp`,
 1 AS `users_with_totp`,
 1 AS `identity_attributes`,
 1 AS `licenses`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `account_billing_products`
--

DROP TABLE IF EXISTS `account_billing_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_billing_products` (
  `code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `unit_price` decimal(8,2) NOT NULL,
  `annual_monthly_price` decimal(8,2) NOT NULL,
  `yearly_account_billing_price` decimal(8,2) NOT NULL,
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_billing_products`
--

LOCK TABLES `account_billing_products` WRITE;
/*!40000 ALTER TABLE `account_billing_products` DISABLE KEYS */;
INSERT INTO `account_billing_products` VALUES ('P0009','SecureLogin SMS verbruik','sms',0.00,0.00,0.00),('P0020','Virtueel nummer','single_sign_on',0.00,0.00,0.00),('P0021','Basis','single_sign_on',0.00,0.00,0.00),('P0022','Premium','single_sign_on',0.00,0.00,0.00),('P0023','Enterprise','single_sign_on',0.00,0.00,0.00),('P0024','SSO Externe gebruikers','single_sign_on',0.00,0.00,0.00),('P0034','BTW/LH/SSO','declarations_and_documents',0.00,0.00,0.00),('P0035','IB/VPB','declarations_and_documents',0.00,0.00,0.00),('P0036','Publicatiestukken micro en klein','declarations_and_documents',0.00,0.00,0.00),('P0037','Publicatiestukken middelgroot en groot','declarations_and_documents',0.00,0.00,0.00),('P0038','Vraagposten','open_questions',0.00,0.00,0.00);
/*!40000 ALTER TABLE `account_billing_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_billings`
--

DROP TABLE IF EXISTS `account_billings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_billings` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `min_quantity` int unsigned NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_billings_account_id_code_unique` (`account_id`,`code`),
  KEY `account_billings_code_foreign` (`code`),
  CONSTRAINT `account_billings_code_foreign` FOREIGN KEY (`code`) REFERENCES `account_billing_products` (`code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_billings`
--

LOCK TABLES `account_billings` WRITE;
/*!40000 ALTER TABLE `account_billings` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_billings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_communication_channels`
--

DROP TABLE IF EXISTS `account_communication_channels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_communication_channels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `subject` varchar(50) DEFAULT NULL,
  `upload_type_id` int unsigned DEFAULT NULL,
  `channel` varchar(50) NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_communication_channels_account_id_foreign` (`account_id`),
  KEY `account_communication_channels_subject_foreign` (`subject`),
  KEY `account_communication_channels_upload_type_id_foreign` (`upload_type_id`),
  KEY `account_communication_channels_channel_foreign` (`channel`),
  CONSTRAINT `account_communication_channels_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_communication_channels_channel_foreign` FOREIGN KEY (`channel`) REFERENCES `communication_channels` (`name`) ON DELETE CASCADE,
  CONSTRAINT `account_communication_channels_subject_foreign` FOREIGN KEY (`subject`) REFERENCES `communication_subjects` (`name`) ON DELETE CASCADE,
  CONSTRAINT `account_communication_channels_upload_type_id_foreign` FOREIGN KEY (`upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_communication_channels`
--

LOCK TABLES `account_communication_channels` WRITE;
/*!40000 ALTER TABLE `account_communication_channels` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_communication_channels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_keychains`
--

DROP TABLE IF EXISTS `account_keychains`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_keychains` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `sign_keys` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_keychains_account_id_unique` (`account_id`),
  CONSTRAINT `account_keychains_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_keychains`
--

LOCK TABLES `account_keychains` WRITE;
/*!40000 ALTER TABLE `account_keychains` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_keychains` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_license`
--

DROP TABLE IF EXISTS `account_license`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_license` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `license_id` int unsigned NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_license_account_id_license_id_unique` (`account_id`,`license_id`),
  KEY `account_license_license_id_foreign` (`license_id`),
  CONSTRAINT `account_license_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_license_license_id_foreign` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=137 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_license`
--

LOCK TABLES `account_license` WRITE;
/*!40000 ALTER TABLE `account_license` DISABLE KEYS */;
INSERT INTO `account_license` VALUES (1,1,3,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(2,1,23,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(3,1,33,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(4,1,35,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(5,1,20,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(6,1,24,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(7,1,1,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(8,1,21,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(9,1,2,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(10,1,22,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(11,1,32,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(12,1,12,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(13,1,16,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(14,1,37,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(15,1,45,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(16,1,11,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(17,1,39,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(18,1,36,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(19,1,19,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(20,1,17,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(21,1,5,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(22,1,27,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(23,1,38,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(24,1,44,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(25,1,6,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(26,1,7,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(27,1,4,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(28,1,25,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(29,1,34,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(30,1,41,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(31,1,42,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(32,1,31,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(33,1,28,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(34,1,29,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(35,1,30,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(36,1,14,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(37,1,15,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(38,1,40,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(39,1,43,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(40,1,10,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(41,1,26,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(42,1,13,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(43,1,8,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(44,1,18,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(45,1,9,'0000-00-00 00:00:00',NULL,'0000-00-00 00:00:00',NULL),(46,1,66,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(47,1,65,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(48,1,67,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(49,1,64,'2018-05-23 09:03:43','1','2018-05-23 09:03:43','1'),(99,1,46,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(100,1,47,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(101,1,49,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(102,1,50,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(103,1,51,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(104,1,52,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(105,1,53,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(106,1,54,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(107,1,55,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(108,1,56,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(109,1,57,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(110,1,60,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(111,1,58,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(112,1,61,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(113,1,62,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(114,1,63,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(115,1,68,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(116,1,69,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(117,1,70,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(118,1,71,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(119,1,73,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(120,1,72,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(121,1,74,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(122,1,75,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(123,1,76,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(124,1,77,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(125,1,78,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(126,1,79,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(127,1,80,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(128,1,83,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(129,1,81,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(130,1,82,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(131,1,85,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(132,1,86,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(133,1,87,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(134,1,88,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL),(136,1,90,'2024-03-21 13:53:38',NULL,'2024-03-21 13:53:38',NULL);
/*!40000 ALTER TABLE `account_license` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_service_companies`
--

DROP TABLE IF EXISTS `account_service_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_service_companies` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `external_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `external_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `external_client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_service_companies_account_service_id_external_id_unique` (`account_service_id`,`external_id`,`deleted_at`),
  UNIQUE KEY `account_service_companies_entry_unique` (`account_service_id`,`company_id`,`external_id`,`deleted_at`),
  KEY `account_service_companies_account_id_company_id_index` (`account_id`,`company_id`),
  KEY `account_service_companies_keywords_index` (`keywords`),
  KEY `account_service_companies_company_id_foreign` (`company_id`),
  CONSTRAINT `account_service_companies_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_companies_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `account_service_companies_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_service_companies`
--

LOCK TABLES `account_service_companies` WRITE;
/*!40000 ALTER TABLE `account_service_companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_service_companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_service_company_users`
--

DROP TABLE IF EXISTS `account_service_company_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_service_company_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `account_service_company_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `company_user_id` int unsigned NOT NULL,
  `permission` enum('approve','inform') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_account_service_company_user` (`account_service_company_id`,`user_id`),
  KEY `account_service_company_users_account_id_company_id_index` (`account_id`,`company_id`),
  KEY `account_service_company_users_user_id_index` (`user_id`),
  KEY `account_service_company_users_account_service_id_foreign` (`account_service_id`),
  KEY `account_service_company_users_company_id_foreign` (`company_id`),
  KEY `account_service_company_users_company_user_id_foreign` (`company_user_id`),
  CONSTRAINT `account_service_company_users_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_company_users_account_service_company_id_foreign` FOREIGN KEY (`account_service_company_id`) REFERENCES `account_service_companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_company_users_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_company_users_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_company_users_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_service_company_users`
--

LOCK TABLES `account_service_company_users` WRITE;
/*!40000 ALTER TABLE `account_service_company_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_service_company_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_service_upload_type_fields`
--

DROP TABLE IF EXISTS `account_service_upload_type_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_service_upload_type_fields` (
  `uuid` varchar(30) NOT NULL,
  `upload_type_id` int unsigned NOT NULL,
  `title` varchar(60) NOT NULL,
  `position` tinyint unsigned NOT NULL,
  `required` tinyint(1) NOT NULL,
  KEY `account_service_upload_type_fields_upload_type_id_foreign` (`upload_type_id`),
  CONSTRAINT `account_service_upload_type_fields_upload_type_id_foreign` FOREIGN KEY (`upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_service_upload_type_fields`
--

LOCK TABLES `account_service_upload_type_fields` WRITE;
/*!40000 ALTER TABLE `account_service_upload_type_fields` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_service_upload_type_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_service_upload_types`
--

DROP TABLE IF EXISTS `account_service_upload_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_service_upload_types` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `title` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `permission` enum('inherit','none','inform','approve') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'inherit',
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_service_upload_types_account_id_foreign` (`account_id`),
  KEY `account_service_upload_types_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `account_service_upload_types_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_upload_types_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_service_upload_types`
--

LOCK TABLES `account_service_upload_types` WRITE;
/*!40000 ALTER TABLE `account_service_upload_types` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_service_upload_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_service_users`
--

DROP TABLE IF EXISTS `account_service_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_service_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `external_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_service_user_unique` (`account_service_id`,`user_id`,`external_id`),
  KEY `account_service_users_user_id_foreign` (`user_id`),
  KEY `account_service_users_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `account_service_users_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_service_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_service_users`
--

LOCK TABLES `account_service_users` WRITE;
/*!40000 ALTER TABLE `account_service_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_service_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_services`
--

DROP TABLE IF EXISTS `account_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_services` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `service_id` int unsigned NOT NULL,
  `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `enabled` tinyint(1) DEFAULT NULL,
  `interval` int unsigned DEFAULT NULL,
  `last_retrieval` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_services_account_id_foreign` (`account_id`),
  KEY `account_services_service_id_foreign` (`service_id`),
  KEY `account_services_display_name_index` (`display_name`),
  CONSTRAINT `account_services_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_services`
--

LOCK TABLES `account_services` WRITE;
/*!40000 ALTER TABLE `account_services` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `account_services_snelstart`
--

DROP TABLE IF EXISTS `account_services_snelstart`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_services_snelstart` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_service_id` int unsigned NOT NULL,
  `token_id` int unsigned NOT NULL,
  `administration_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `client_key` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'This key is needed to request access token',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_services_snelstart_accser_id_adm_id_unique` (`account_service_id`,`administration_id`),
  KEY `account_services_snelstart_token_id_foreign` (`token_id`),
  CONSTRAINT `account_services_snelstart_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `account_services_snelstart_token_id_foreign` FOREIGN KEY (`token_id`) REFERENCES `tokens` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_services_snelstart`
--

LOCK TABLES `account_services_snelstart` WRITE;
/*!40000 ALTER TABLE `account_services_snelstart` DISABLE KEYS */;
/*!40000 ALTER TABLE `account_services_snelstart` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `accounts`
--

DROP TABLE IF EXISTS `accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `accounts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hostname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `pubkey_pins` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `login_options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `login_autocomplete` tinyint(1) NOT NULL DEFAULT '1',
  `support_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `support_categories` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `debtor_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `pricing_model` enum('classic','basic','premium','enterprise','none','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'none',
  `pipedrive_number` int DEFAULT NULL,
  `feature_package` enum('basic','premium','enterprise','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'none',
  `pricing_settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `primary_color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `secondary_color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `logo_color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `logo_white` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `logo_circle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '',
  `email_header` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `background_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `language` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'nl',
  `settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `embed_settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `activated_at` timestamp NULL DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'customer',
  `usage_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `api_origins` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mrr` double(8,2) DEFAULT NULL,
  `status_expiration` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `uuid` varchar(30) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accounts_name_unique` (`name`),
  UNIQUE KEY `accounts_uuid_unique` (`uuid`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `accounts`
--

LOCK TABLES `accounts` WRITE;
/*!40000 ALTER TABLE `accounts` DISABLE KEYS */;
INSERT INTO `accounts` VALUES (1,'SecureLogin','local.securelogin.nu',NULL,NULL,1,'<EMAIL>',NULL,NULL,'none',NULL,'none','','#ff4400','#6ac837',NULL,'5b0551e86afae','',NULL,'5b0551e86bb1b','nl',NULL,NULL,'active','2016-05-17 08:55:34','customer','[\"gui\",\"api\"]','',NULL,NULL,'2016-05-17 08:55:34','2016-05-17 08:55:34','3','2024-03-21 14:53:38','1',NULL,NULL,NULL);
/*!40000 ALTER TABLE `accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `communication_channels`
--

DROP TABLE IF EXISTS `communication_channels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `communication_channels` (
  `name` varchar(50) NOT NULL,
  `order` tinyint unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `communication_channels`
--

LOCK TABLES `communication_channels` WRITE;
/*!40000 ALTER TABLE `communication_channels` DISABLE KEYS */;
/*!40000 ALTER TABLE `communication_channels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `communication_subjects`
--

DROP TABLE IF EXISTS `communication_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `communication_subjects` (
  `name` varchar(50) NOT NULL,
  `type` varchar(50) NOT NULL,
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `communication_subjects`
--

LOCK TABLES `communication_subjects` WRITE;
/*!40000 ALTER TABLE `communication_subjects` DISABLE KEYS */;
/*!40000 ALTER TABLE `communication_subjects` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `companies`
--

DROP TABLE IF EXISTS `companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `companies` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `internal_client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `wage_tax_period` enum('M','W4') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `billable` tinyint(1) NOT NULL DEFAULT '0',
  `settings` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int unsigned DEFAULT NULL,
  `archived_at` timestamp NULL DEFAULT NULL,
  `archived_by` int unsigned DEFAULT NULL,
  `restored_at` timestamp NULL DEFAULT NULL,
  `restored_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  `external_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `uuid` varchar(30) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_name_unique` (`account_id`,`name`),
  UNIQUE KEY `companies_uuid_unique` (`uuid`),
  KEY `companies_account_id_index` (`account_id`),
  KEY `companies_external_id_index` (`external_id`),
  CONSTRAINT `companies_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `companies`
--

LOCK TABLES `companies` WRITE;
/*!40000 ALTER TABLE `companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `companies_users`
--

DROP TABLE IF EXISTS `companies_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `companies_users` (
  `company_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_service_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_company` (`user_id`,`company_id`),
  KEY `user_id_foreign` (`user_id`),
  KEY `company_id_foreign` (`company_id`),
  KEY `companies_users_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `companies_users_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `companies_users_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `companies_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `companies_users`
--

LOCK TABLES `companies_users` WRITE;
/*!40000 ALTER TABLE `companies_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `companies_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_declarants`
--

DROP TABLE IF EXISTS `company_declarants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_declarants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_service_id` int unsigned DEFAULT NULL,
  `company_id` int unsigned NOT NULL,
  `bsn_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `bsn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dms_configurations` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_declarants_company_id_foreign` (`company_id`),
  KEY `company_declarants_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `company_declarants_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_declarants_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_declarants`
--

LOCK TABLES `company_declarants` WRITE;
/*!40000 ALTER TABLE `company_declarants` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_declarants` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_identifiers`
--

DROP TABLE IF EXISTS `company_identifiers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_identifiers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `type` enum('bsnnumber','fiscalnumber','lhnumber','kvknumber','vatnumber') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `identifier` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `uuid` varchar(30) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_identifiers_account_id_identifier_type_unique` (`account_id`,`identifier`,`type`),
  UNIQUE KEY `company_identifiers_uuid_unique` (`uuid`),
  KEY `company_identifiers_created_by_foreign` (`created_by`),
  KEY `company_identifiers_company_id_foreign` (`company_id`),
  CONSTRAINT `company_identifiers_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_identifiers_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_identifiers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_identifiers`
--

LOCK TABLES `company_identifiers` WRITE;
/*!40000 ALTER TABLE `company_identifiers` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_identifiers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_ocr_emails`
--

DROP TABLE IF EXISTS `company_ocr_emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_ocr_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int unsigned NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` enum('purchase','sale') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_ocr_emails_company_id_foreign` (`company_id`),
  CONSTRAINT `company_ocr_emails_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_ocr_emails`
--

LOCK TABLES `company_ocr_emails` WRITE;
/*!40000 ALTER TABLE `company_ocr_emails` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_ocr_emails` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_tags`
--

DROP TABLE IF EXISTS `company_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int unsigned NOT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_tags_company_id_foreign` (`company_id`),
  CONSTRAINT `company_tags_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_tags`
--

LOCK TABLES `company_tags` WRITE;
/*!40000 ALTER TABLE `company_tags` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_tags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_upload_type_permissions`
--

DROP TABLE IF EXISTS `company_upload_type_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_upload_type_permissions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `account_service_upload_type_id` int unsigned NOT NULL,
  `company_user_id` int unsigned NOT NULL,
  `permission` enum('inform','approve','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_upload_type_permissions_account_id_foreign` (`account_id`),
  KEY `company_upload_type_permissions_company_id_foreign` (`company_id`),
  KEY `account_service_upload_type_id_foreign` (`account_service_upload_type_id`),
  KEY `company_upload_type_permissions_company_user_id_foreign` (`company_user_id`),
  CONSTRAINT `account_service_upload_type_id_foreign` FOREIGN KEY (`account_service_upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_upload_type_permissions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_upload_type_permissions_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_upload_type_permissions_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_upload_type_permissions`
--

LOCK TABLES `company_upload_type_permissions` WRITE;
/*!40000 ALTER TABLE `company_upload_type_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_upload_type_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_user_context_companies`
--

DROP TABLE IF EXISTS `company_user_context_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_user_context_companies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_user_id` int unsigned NOT NULL,
  `context_company_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_user_context_companies_company_user_id_foreign` (`company_user_id`),
  KEY `company_user_context_companies_context_company_id_foreign` (`context_company_id`),
  CONSTRAINT `company_user_context_companies_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_user_context_companies_context_company_id_foreign` FOREIGN KEY (`context_company_id`) REFERENCES `context_companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_user_context_companies`
--

LOCK TABLES `company_user_context_companies` WRITE;
/*!40000 ALTER TABLE `company_user_context_companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_user_context_companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `company_user_permissions`
--

DROP TABLE IF EXISTS `company_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_user_permissions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `identifier_id` int unsigned NOT NULL,
  `company_user_id` int unsigned NOT NULL,
  `permission` enum('inform','approve','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_user_permissions_account_id_foreign` (`account_id`),
  KEY `company_user_permissions_company_id_foreign` (`company_id`),
  KEY `company_user_permissions_identifier_id_foreign` (`identifier_id`),
  KEY `company_user_permissions_company_user_id_foreign` (`company_user_id`),
  CONSTRAINT `company_user_permissions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_user_permissions_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_user_permissions_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_user_permissions_identifier_id_foreign` FOREIGN KEY (`identifier_id`) REFERENCES `company_identifiers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_user_permissions`
--

LOCK TABLES `company_user_permissions` WRITE;
/*!40000 ALTER TABLE `company_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `consumers`
--

DROP TABLE IF EXISTS `consumers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `is_idp` tinyint(1) NOT NULL DEFAULT '0',
  `auth_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `provisioning_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `generic_widget_id` int unsigned DEFAULT NULL,
  `account_service_id` int unsigned DEFAULT NULL,
  `auth_secret` blob,
  `verify_keys` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `sign_keys` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `allowed_origins` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `protocol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `mfa` tinyint(1) NOT NULL DEFAULT '0',
  `metadata_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `acs_endpoints` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `login_endpoints` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `logout_endpoints` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `name_id_format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `onboard_context_id` int unsigned DEFAULT NULL,
  `login_button` int unsigned NOT NULL DEFAULT '0',
  `login_button_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `login_button_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `bypass_ip_check` tinyint(1) DEFAULT NULL,
  `identity_linking` tinyint(1) NOT NULL DEFAULT '1',
  `is_external` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `consumers_account_id_foreign` (`account_id`),
  KEY `consumers_onboard_context_id_foreign` (`onboard_context_id`),
  KEY `consumers_generic_widget_id_foreign` (`generic_widget_id`),
  KEY `consumers_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `consumers_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `consumers_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`),
  CONSTRAINT `consumers_generic_widget_id_foreign` FOREIGN KEY (`generic_widget_id`) REFERENCES `generic_widgets` (`id`) ON DELETE SET NULL,
  CONSTRAINT `consumers_onboard_context_id_foreign` FOREIGN KEY (`onboard_context_id`) REFERENCES `contexts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `consumers`
--

LOCK TABLES `consumers` WRITE;
/*!40000 ALTER TABLE `consumers` DISABLE KEYS */;
INSERT INTO `consumers` VALUES (1,1,'PkiSigning',0,'securelogin',NULL,NULL,NULL,_binary '675ff9b34d91d825df02ea5db297a9dcc6a9787b',NULL,NULL,NULL,'oauth2','{\"token_endpoint\":\"https:\\/\\/accidentity.pkisigning.io\\/connect\\/token\",\"redirect_uri\":\"https:\\/\\/securelogin.staging.securelogin.nu\\/services\\/pki_signing\\/authenticated\\/\"}',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,1,NULL,'2020-09-01 12:09:47',NULL,'2020-09-01 12:09:47',NULL,NULL,NULL),(2,1,'Azure Publicated SSO',0,'1cd121ac-255f-4cae-8028-fdcffaeda4b6',NULL,NULL,NULL,_binary 'LkQ61SK_vZ~58eKk4XFNR0wQSd5~ByJY7.',NULL,NULL,'[\"https:\\/\\/login.microsoftonline.com\"]','oauth2','{\"provider\":\"azure\",\"multi-accounts\":true}',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,1,NULL,'2021-11-22 12:00:29',NULL,'2021-11-22 12:00:29',NULL,NULL,NULL),(3,1,'Azure User Provisioning',0,'b47022cc-d7e7-4d04-9e85-667af551e707',NULL,NULL,NULL,_binary 'Ae`y)Re6G,D9-\\#6QPmJR3aFMs<X2R5n',NULL,NULL,NULL,'oauth2','{\"scopes\":[\"scim\"]}',0,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,1,NULL,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(4,1,'OAuth Visionplanner',0,'SecureLoginProd',NULL,NULL,NULL,_binary 'YE3zsrNXJ2oEg973pKej3HZEjj4oqxPz',NULL,NULL,NULL,NULL,'{\"access_token_url\":\"https:\\/\\/auth.visionplanner.nl\\/connect\\/token\",\"authorization_method\":\"post\",\"expire_in\":\"300\"}',0,NULL,NULL,'[{\"location\":\"https:\\/\\/auth.visionplanner.nl\\/connect\\/authorize?\"}]',NULL,NULL,NULL,0,NULL,NULL,NULL,1,NULL,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL);
/*!40000 ALTER TABLE `consumers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `consumers_issuers`
--

DROP TABLE IF EXISTS `consumers_issuers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `consumers_issuers` (
  `consumer_id` int unsigned NOT NULL,
  `account_id` int unsigned NOT NULL,
  `issuer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `consumers_issuers_consumer_id_account_id_unique` (`consumer_id`,`account_id`),
  UNIQUE KEY `consumers_issuers_issuer_unique` (`issuer`),
  KEY `consumers_issuers_account_id_foreign` (`account_id`),
  CONSTRAINT `consumers_issuers_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `consumers_issuers_consumer_id_foreign` FOREIGN KEY (`consumer_id`) REFERENCES `consumers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `consumers_issuers`
--

LOCK TABLES `consumers_issuers` WRITE;
/*!40000 ALTER TABLE `consumers_issuers` DISABLE KEYS */;
/*!40000 ALTER TABLE `consumers_issuers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `context_companies`
--

DROP TABLE IF EXISTS `context_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `context_companies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `context_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `account_service_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `context_companies_company_id_context_id_unique` (`company_id`,`context_id`),
  KEY `context_companies_context_id_foreign` (`context_id`),
  KEY `context_companies_company_id_foreign` (`company_id`),
  KEY `context_companies_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `context_companies_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE SET NULL,
  CONSTRAINT `context_companies_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `context_companies_context_id_foreign` FOREIGN KEY (`context_id`) REFERENCES `contexts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `context_companies`
--

LOCK TABLES `context_companies` WRITE;
/*!40000 ALTER TABLE `context_companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `context_companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `context_widgets`
--

DROP TABLE IF EXISTS `context_widgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `context_widgets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `context_id` int unsigned DEFAULT NULL,
  `parent_id` int unsigned DEFAULT NULL,
  `generic_parent_id` int unsigned DEFAULT NULL,
  `context_parent_id` int unsigned DEFAULT NULL,
  `generic_widget_id` int unsigned DEFAULT NULL,
  `account_id` int unsigned NOT NULL,
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `properties` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `order` int DEFAULT NULL,
  `view` enum('default','propagate','inheritable','broadcast') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'default',
  `heredity` enum('final','inheritable','propagate','protected','restricted_inheritable','restricted_propagate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'inheritable',
  `auto_populate` tinyint(1) DEFAULT NULL,
  `auto_propagate` tinyint(1) DEFAULT NULL,
  `allow_external_start` tinyint(1) NOT NULL DEFAULT '0',
  `external_start_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `visible_in_main_context` tinyint(1) NOT NULL DEFAULT '1',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `users_count` int DEFAULT NULL,
  `user_widgets_count` int DEFAULT NULL,
  `cache` longblob,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `context_widgets_generic_parent_id_foreign` (`generic_parent_id`),
  KEY `context_widgets_context_parent_id_foreign` (`context_parent_id`),
  KEY `context_widgets_context_id_foreign` (`context_id`),
  KEY `context_widgets_parent_id_foreign` (`parent_id`),
  KEY `context_widgets_generic_widget_id_foreign` (`generic_widget_id`),
  KEY `context_widgets_account_id_index` (`account_id`),
  CONSTRAINT `context_widgets_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `context_widgets_context_id_foreign` FOREIGN KEY (`context_id`) REFERENCES `contexts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `context_widgets_context_parent_id_foreign` FOREIGN KEY (`context_parent_id`) REFERENCES `context_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `context_widgets_generic_parent_id_foreign` FOREIGN KEY (`generic_parent_id`) REFERENCES `generic_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `context_widgets_generic_widget_id_foreign` FOREIGN KEY (`generic_widget_id`) REFERENCES `generic_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `context_widgets_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `generic_widgets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `context_widgets`
--

LOCK TABLES `context_widgets` WRITE;
/*!40000 ALTER TABLE `context_widgets` DISABLE KEYS */;
/*!40000 ALTER TABLE `context_widgets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contexts`
--

DROP TABLE IF EXISTS `contexts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contexts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `external_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `parent_id` int unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '/',
  `left_order` int NOT NULL DEFAULT '0',
  `right_order` int NOT NULL DEFAULT '0',
  `level` int NOT NULL DEFAULT '0',
  `members_count` int DEFAULT NULL,
  `user_custom_fields` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `session_lifetime_max` int DEFAULT NULL,
  `session_lifetime_default` int DEFAULT NULL,
  `ip_whitelist_mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `ip_whitelist` varchar(2047) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `auth_methods` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `auth_methods_enforcement_at` timestamp NULL DEFAULT NULL,
  `auth_secret_expire_days` int DEFAULT NULL,
  `single_session_enforced` tinyint(1) DEFAULT NULL,
  `is_external` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `contexts_parent_id_name_unique` (`parent_id`,`name`),
  UNIQUE KEY `external_id_account_id_unique` (`external_id`,`account_id`),
  KEY `contexts_account_id_foreign` (`account_id`),
  KEY `contexts_path_index` (`path`),
  KEY `contexts_level_index` (`level`),
  KEY `contexts_created_at_index` (`created_at`),
  KEY `contexts_updated_at_index` (`updated_at`),
  KEY `contexts_deleted_at_index` (`deleted_at`),
  KEY `contexts_left_order_right_order_index` (`left_order`,`right_order`),
  CONSTRAINT `contexts_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `contexts_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `contexts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contexts`
--

LOCK TABLES `contexts` WRITE;
/*!40000 ALTER TABLE `contexts` DISABLE KEYS */;
INSERT INTO `contexts` VALUES (1,1,NULL,NULL,'SecureLogin','/',1,4,-1,1,'',480,480,'standard','','secret,totp,sms_otp',NULL,0,0,0,'2016-05-17 08:55:34','3','2018-05-23 11:33:10','1',NULL,NULL),(2,1,NULL,1,'Intern','/SecureLogin/',2,3,0,NULL,'',NULL,480,NULL,NULL,NULL,NULL,NULL,NULL,0,'2018-05-30 11:25:19','1','2018-05-30 11:28:52','1',NULL,NULL);
/*!40000 ALTER TABLE `contexts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `custom_link_domains`
--

DROP TABLE IF EXISTS `custom_link_domains`;
/*!50001 DROP VIEW IF EXISTS `custom_link_domains`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `custom_link_domains` AS SELECT 
 1 AS `domain`,
 1 AS `Number OF Context Widgets`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `custom_rule_conditions`
--

DROP TABLE IF EXISTS `custom_rule_conditions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `custom_rule_conditions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `custom_rule_id` int unsigned NOT NULL,
  `event` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `account_service_upload_type_id` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `custom_rule_conditions_custom_rule_id_foreign` (`custom_rule_id`),
  KEY `custom_rule_conditions_account_service_upload_type_id_foreign` (`account_service_upload_type_id`),
  KEY `custom_rule_conditions_account_id_event_index` (`account_id`,`event`),
  CONSTRAINT `custom_rule_conditions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `custom_rule_conditions_account_service_upload_type_id_foreign` FOREIGN KEY (`account_service_upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `custom_rule_conditions_custom_rule_id_foreign` FOREIGN KEY (`custom_rule_id`) REFERENCES `custom_rules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `custom_rule_conditions`
--

LOCK TABLES `custom_rule_conditions` WRITE;
/*!40000 ALTER TABLE `custom_rule_conditions` DISABLE KEYS */;
/*!40000 ALTER TABLE `custom_rule_conditions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `custom_rule_users`
--

DROP TABLE IF EXISTS `custom_rule_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `custom_rule_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `custom_rule_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `custom_rule_users_custom_rule_id_foreign` (`custom_rule_id`),
  KEY `custom_rule_users_user_id_foreign` (`user_id`),
  CONSTRAINT `custom_rule_users_custom_rule_id_foreign` FOREIGN KEY (`custom_rule_id`) REFERENCES `custom_rules` (`id`) ON DELETE CASCADE,
  CONSTRAINT `custom_rule_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `custom_rule_users`
--

LOCK TABLES `custom_rule_users` WRITE;
/*!40000 ALTER TABLE `custom_rule_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `custom_rule_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `custom_rules`
--

DROP TABLE IF EXISTS `custom_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `custom_rules` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parameters` json NOT NULL,
  `active` tinyint(1) NOT NULL,
  `followed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `custom_rules_account_id_action_index` (`account_id`,`action`),
  CONSTRAINT `custom_rules_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `custom_rules`
--

LOCK TABLES `custom_rules` WRITE;
/*!40000 ALTER TABLE `custom_rules` DISABLE KEYS */;
/*!40000 ALTER TABLE `custom_rules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dashboard_categories`
--

DROP TABLE IF EXISTS `dashboard_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dashboard_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `order_index` int unsigned NOT NULL,
  `favorite` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dashboard_categories_user_id_foreign` (`user_id`),
  CONSTRAINT `dashboard_categories_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dashboard_categories`
--

LOCK TABLES `dashboard_categories` WRITE;
/*!40000 ALTER TABLE `dashboard_categories` DISABLE KEYS */;
/*!40000 ALTER TABLE `dashboard_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dashboard_categories_user_widgets`
--

DROP TABLE IF EXISTS `dashboard_categories_user_widgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dashboard_categories_user_widgets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `dashboard_category_id` int unsigned NOT NULL,
  `user_widget_id` int unsigned NOT NULL,
  `order_index` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_widget_dashboard_category` (`dashboard_category_id`,`user_widget_id`),
  KEY `dashboard_categories_user_widgets_user_widget_id_foreign` (`user_widget_id`),
  CONSTRAINT `dashboard_categories_user_widgets_dashboard_category_id_foreign` FOREIGN KEY (`dashboard_category_id`) REFERENCES `dashboard_categories` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dashboard_categories_user_widgets_user_widget_id_foreign` FOREIGN KEY (`user_widget_id`) REFERENCES `user_widgets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dashboard_categories_user_widgets`
--

LOCK TABLES `dashboard_categories_user_widgets` WRITE;
/*!40000 ALTER TABLE `dashboard_categories_user_widgets` DISABLE KEYS */;
/*!40000 ALTER TABLE `dashboard_categories_user_widgets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `declarations_vat`
--

DROP TABLE IF EXISTS `declarations_vat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `declarations_vat` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_task_id` int unsigned NOT NULL,
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value_added_tax_owed` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value_added_tax_on_input` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value_added_tax_owed_to_be_paid_back` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `frequency` enum('monthly','quarterly','yearly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `declarations_vat_service_task_id_unique` (`service_task_id`),
  CONSTRAINT `declarations_vat_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `declarations_vat`
--

LOCK TABLES `declarations_vat` WRITE;
/*!40000 ALTER TABLE `declarations_vat` DISABLE KEYS */;
/*!40000 ALTER TABLE `declarations_vat` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `declarations_vpb`
--

DROP TABLE IF EXISTS `declarations_vpb`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `declarations_vpb` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_task_id` int unsigned NOT NULL,
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tax_according_to_ordinary_rate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `advance_levies_total` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tax_amount_elsewhere_taxed_balance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tax_reductions_total` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tax_amount_balance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `turnover_net_fiscal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `taxable_profit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `frequency` enum('monthly','quarterly','yearly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `declarations_vpb_service_task_id_unique` (`service_task_id`),
  CONSTRAINT `declarations_vpb_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `declarations_vpb`
--

LOCK TABLES `declarations_vpb` WRITE;
/*!40000 ALTER TABLE `declarations_vpb` DISABLE KEYS */;
/*!40000 ALTER TABLE `declarations_vpb` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `default_company_user_permissions`
--

DROP TABLE IF EXISTS `default_company_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `default_company_user_permissions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `company_user_id` int unsigned NOT NULL,
  `permission` enum('inform','approve','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `default_company_user_permissions_account_id_foreign` (`account_id`),
  KEY `default_company_user_permissions_company_id_foreign` (`company_id`),
  KEY `default_company_user_permissions_company_user_id_foreign` (`company_user_id`),
  CONSTRAINT `default_company_user_permissions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `default_company_user_permissions_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `default_company_user_permissions_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `default_company_user_permissions`
--

LOCK TABLES `default_company_user_permissions` WRITE;
/*!40000 ALTER TABLE `default_company_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `default_company_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `default_templates`
--

DROP TABLE IF EXISTS `default_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `default_templates` (
  `template_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`template_id`),
  CONSTRAINT `default_templates_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `open_question_templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `default_templates`
--

LOCK TABLES `default_templates` WRITE;
/*!40000 ALTER TABLE `default_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `default_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dms_configurations`
--

DROP TABLE IF EXISTS `dms_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dms_configurations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_identifier_id` int unsigned NOT NULL,
  `configuration` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `dms_configurations_company_identifier_id_unique` (`company_identifier_id`),
  CONSTRAINT `dms_configurations_company_identifier_id_foreign` FOREIGN KEY (`company_identifier_id`) REFERENCES `company_identifiers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dms_configurations`
--

LOCK TABLES `dms_configurations` WRITE;
/*!40000 ALTER TABLE `dms_configurations` DISABLE KEYS */;
/*!40000 ALTER TABLE `dms_configurations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dossier_audit_logs`
--

DROP TABLE IF EXISTS `dossier_audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dossier_audit_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `dossier_folder_id` bigint unsigned DEFAULT NULL,
  `dossier_file_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `agent_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dossier_audit_logs_account_id_foreign` (`account_id`),
  KEY `dossier_audit_logs_company_id_foreign` (`company_id`),
  KEY `dossier_audit_logs_dossier_folder_id_foreign` (`dossier_folder_id`),
  KEY `dossier_audit_logs_dossier_file_id_foreign` (`dossier_file_id`),
  KEY `dossier_audit_logs_created_by_foreign` (`created_by`),
  KEY `dossier_audit_logs_updated_by_foreign` (`updated_by`),
  CONSTRAINT `dossier_audit_logs_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_audit_logs_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_audit_logs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `dossier_audit_logs_dossier_file_id_foreign` FOREIGN KEY (`dossier_file_id`) REFERENCES `dossier_files` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `dossier_audit_logs_dossier_folder_id_foreign` FOREIGN KEY (`dossier_folder_id`) REFERENCES `dossier_folders` (`id`) ON DELETE SET NULL,
  CONSTRAINT `dossier_audit_logs_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dossier_audit_logs`
--

LOCK TABLES `dossier_audit_logs` WRITE;
/*!40000 ALTER TABLE `dossier_audit_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `dossier_audit_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dossier_file_users`
--

DROP TABLE IF EXISTS `dossier_file_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dossier_file_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `dossier_file_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `dossier_file_users_user_id_dossier_file_uuid_unique` (`user_id`,`dossier_file_uuid`),
  KEY `dossier_file_users_dossier_file_uuid_foreign` (`dossier_file_uuid`),
  CONSTRAINT `dossier_file_users_dossier_file_uuid_foreign` FOREIGN KEY (`dossier_file_uuid`) REFERENCES `dossier_files` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `dossier_file_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dossier_file_users`
--

LOCK TABLES `dossier_file_users` WRITE;
/*!40000 ALTER TABLE `dossier_file_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `dossier_file_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dossier_files`
--

DROP TABLE IF EXISTS `dossier_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dossier_files` (
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `dossier_folder_id` bigint unsigned NOT NULL,
  `service_task_id` int unsigned DEFAULT NULL,
  `task_file_id` int unsigned DEFAULT NULL,
  `checksum` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `extension` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `year` int unsigned NOT NULL,
  `sort_index` int unsigned NOT NULL DEFAULT '0',
  `size` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `dossier_files_uuid_unique` (`uuid`),
  KEY `dossier_files_dossier_folder_id_foreign` (`dossier_folder_id`),
  KEY `dossier_files_year_index` (`year`),
  KEY `dossier_files_account_id_foreign` (`account_id`),
  KEY `dossier_files_company_id_foreign` (`company_id`),
  KEY `dossier_files_service_task_id_foreign` (`service_task_id`),
  KEY `dossier_files_task_file_id_foreign` (`task_file_id`),
  CONSTRAINT `dossier_files_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_files_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_files_dossier_folder_id_foreign` FOREIGN KEY (`dossier_folder_id`) REFERENCES `dossier_folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_files_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE SET NULL,
  CONSTRAINT `dossier_files_task_file_id_foreign` FOREIGN KEY (`task_file_id`) REFERENCES `task_files` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dossier_files`
--

LOCK TABLES `dossier_files` WRITE;
/*!40000 ALTER TABLE `dossier_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `dossier_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dossier_folder_users`
--

DROP TABLE IF EXISTS `dossier_folder_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dossier_folder_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `dossier_folder_id` bigint unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dossier_folder_users_dossier_folder_id_foreign` (`dossier_folder_id`),
  KEY `dossier_folder_users_user_id_foreign` (`user_id`),
  CONSTRAINT `dossier_folder_users_dossier_folder_id_foreign` FOREIGN KEY (`dossier_folder_id`) REFERENCES `dossier_folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_folder_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dossier_folder_users`
--

LOCK TABLES `dossier_folder_users` WRITE;
/*!40000 ALTER TABLE `dossier_folder_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `dossier_folder_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dossier_folders`
--

DROP TABLE IF EXISTS `dossier_folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dossier_folders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `sort_index` int unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `dossier_folders_name_unique` (`company_id`,`parent_id`,`name`),
  KEY `dossier_folders_account_id_foreign` (`account_id`),
  KEY `dossier_folders_parent_id_foreign` (`parent_id`),
  CONSTRAINT `dossier_folders_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_folders_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `dossier_folders_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `dossier_folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dossier_folders`
--

LOCK TABLES `dossier_folders` WRITE;
/*!40000 ALTER TABLE `dossier_folders` DISABLE KEYS */;
/*!40000 ALTER TABLE `dossier_folders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_template_conditions`
--

DROP TABLE IF EXISTS `email_template_conditions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_template_conditions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `template_id` int unsigned NOT NULL,
  `language` varchar(2) NOT NULL,
  `event` varchar(80) NOT NULL,
  `upload_type_id` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_condition` (`account_id`,`language`,`event`,`upload_type_id`),
  KEY `email_template_conditions_template_id_foreign` (`template_id`),
  KEY `email_template_conditions_upload_type_id_foreign` (`upload_type_id`),
  CONSTRAINT `email_template_conditions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `email_template_conditions_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `email_template_conditions_upload_type_id_foreign` FOREIGN KEY (`upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_template_conditions`
--

LOCK TABLES `email_template_conditions` WRITE;
/*!40000 ALTER TABLE `email_template_conditions` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_template_conditions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_template_fields`
--

DROP TABLE IF EXISTS `email_template_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_template_fields` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int unsigned NOT NULL,
  `key` varchar(40) NOT NULL,
  `content` text NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_template_fields_template_id_key_unique` (`template_id`,`key`),
  CONSTRAINT `email_template_fields_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_template_fields`
--

LOCK TABLES `email_template_fields` WRITE;
/*!40000 ALTER TABLE `email_template_fields` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_template_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_templates`
--

DROP TABLE IF EXISTS `email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_templates` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `type` varchar(80) NOT NULL,
  `language` varchar(2) NOT NULL,
  `name` varchar(40) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_templates_account_id_foreign` (`account_id`),
  CONSTRAINT `email_templates_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_templates`
--

LOCK TABLES `email_templates` WRITE;
/*!40000 ALTER TABLE `email_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `export_types`
--

DROP TABLE IF EXISTS `export_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `export_types` (
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `export_types`
--

LOCK TABLES `export_types` WRITE;
/*!40000 ALTER TABLE `export_types` DISABLE KEYS */;
/*!40000 ALTER TABLE `export_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exports`
--

DROP TABLE IF EXISTS `exports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exports` (
  `uuid` varchar(30) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL,
  `account_id` int unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `requested_at` datetime NOT NULL,
  `user_id` int unsigned NOT NULL,
  `status` enum('processing','ready','error') NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `filters` json DEFAULT NULL,
  `expire_at` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `exports_account_id_foreign` (`account_id`),
  KEY `exports_type_foreign` (`type`),
  KEY `exports_user_id_foreign` (`user_id`),
  CONSTRAINT `exports_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `exports_type_foreign` FOREIGN KEY (`type`) REFERENCES `export_types` (`name`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `exports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exports`
--

LOCK TABLES `exports` WRITE;
/*!40000 ALTER TABLE `exports` DISABLE KEYS */;
/*!40000 ALTER TABLE `exports` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `connection` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `queue` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`),
  KEY `failed_jobs_failed_at_index` (`failed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `failed_jobs`
--

LOCK TABLES `failed_jobs` WRITE;
/*!40000 ALTER TABLE `failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `generic_services`
--

DROP TABLE IF EXISTS `generic_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `generic_services` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `allow_multiple` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `generic_services`
--

LOCK TABLES `generic_services` WRITE;
/*!40000 ALTER TABLE `generic_services` DISABLE KEYS */;
INSERT INTO `generic_services` VALUES (1,'service_provider',NULL,NULL,'service','vat_declaration',1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(2,'manual_tasks',NULL,NULL,'manual','vat_declaration',1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(3,'signing',NULL,NULL,'signing',NULL,1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(4,'documents_publication',NULL,NULL,'documents_publication',NULL,1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(5,'openid_connect','sl-icon-puzzle-piece','#FF6200','sso',NULL,1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(6,'document_management_system',NULL,NULL,'document_management_system',NULL,0,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(7,'open_questions',NULL,NULL,'open_questions','open_questions',1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(8,'customer_relationship_management',NULL,NULL,'customer_relationship_management',NULL,0,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(9,'other',NULL,NULL,'other',NULL,1,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL);
/*!40000 ALTER TABLE `generic_services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `generic_widget_categories`
--

DROP TABLE IF EXISTS `generic_widget_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `generic_widget_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `generic_widget_categories`
--

LOCK TABLES `generic_widget_categories` WRITE;
/*!40000 ALTER TABLE `generic_widget_categories` DISABLE KEYS */;
INSERT INTO `generic_widget_categories` VALUES (1,'wages','2024-03-21 08:41:19','2024-03-21 08:41:19'),(2,'fiscal','2024-03-21 08:41:19','2024-03-21 08:41:19'),(3,'accountancy','2024-03-21 08:41:19','2024-03-21 08:41:19'),(4,'general','2024-03-21 08:41:19','2024-03-21 08:41:19'),(5,'other','2024-03-21 08:41:19','2024-03-21 08:41:19');
/*!40000 ALTER TABLE `generic_widget_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `generic_widget_generic_widget_category`
--

DROP TABLE IF EXISTS `generic_widget_generic_widget_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `generic_widget_generic_widget_category` (
  `generic_widget_category_id` int unsigned DEFAULT NULL,
  `generic_widget_id` int unsigned DEFAULT NULL,
  KEY `gw_gwc_gwc_id` (`generic_widget_category_id`),
  KEY `gw_gwc_gw_id` (`generic_widget_id`),
  CONSTRAINT `gw_gwc_gw_id` FOREIGN KEY (`generic_widget_id`) REFERENCES `generic_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `gw_gwc_gwc_id` FOREIGN KEY (`generic_widget_category_id`) REFERENCES `generic_widget_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `generic_widget_generic_widget_category`
--

LOCK TABLES `generic_widget_generic_widget_category` WRITE;
/*!40000 ALTER TABLE `generic_widget_generic_widget_category` DISABLE KEYS */;
/*!40000 ALTER TABLE `generic_widget_generic_widget_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `generic_widget_per_user_widget`
--

DROP TABLE IF EXISTS `generic_widget_per_user_widget`;
/*!50001 DROP VIEW IF EXISTS `generic_widget_per_user_widget`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `generic_widget_per_user_widget` AS SELECT 
 1 AS `user_widget_id`,
 1 AS `user_id`,
 1 AS `generic_widget_id`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `generic_widgets`
--

DROP TABLE IF EXISTS `generic_widgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `generic_widgets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int unsigned NOT NULL,
  `reference_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `version_date` date DEFAULT NULL,
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `properties` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `operations` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `onshow_operations` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `onstart_operations` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `onstart_checks` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `oninstall_operations` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `order` int DEFAULT '0',
  `view` enum('default','propagate','inheritable','broadcast') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'default',
  `heredity` enum('final','inheritable','propagate','protected','restricted_inheritable','restricted_propagate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'inheritable',
  `licenses` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `status` enum('alpha','beta','active','deprecated','blocked') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'alpha',
  `cache` longblob,
  `contact_detail` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `debug_telemetry` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'none',
  `monitored_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `generic_widgets_template_id_foreign` (`template_id`),
  CONSTRAINT `generic_widgets_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `widget_templates` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `generic_widgets`
--

LOCK TABLES `generic_widgets` WRITE;
/*!40000 ALTER TABLE `generic_widgets` DISABLE KEYS */;
/*!40000 ALTER TABLE `generic_widgets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `global_stats`
--

DROP TABLE IF EXISTS `global_stats`;
/*!50001 DROP VIEW IF EXISTS `global_stats`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `global_stats` AS SELECT 
 1 AS `year`,
 1 AS `month`,
 1 AS `user_count`,
 1 AS `active_user_count`,
 1 AS `login_count`,
 1 AS `sms_count`,
 1 AS `notification_count`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `identity_attributes`
--

DROP TABLE IF EXISTS `identity_attributes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `identity_attributes` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `account_id` int unsigned NOT NULL,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'subject',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `issuer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `validated` int NOT NULL DEFAULT '0',
  `unique_value` tinyint(1) NOT NULL DEFAULT '0',
  `masterkey` blob,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identity_attributes_user_id_key_value_issuer_unique` (`user_id`,`key`,`value`,`issuer`),
  KEY `identity_attributes_account_id_foreign` (`account_id`),
  CONSTRAINT `identity_attributes_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `identity_attributes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `identity_attributes`
--

LOCK TABLES `identity_attributes` WRITE;
/*!40000 ALTER TABLE `identity_attributes` DISABLE KEYS */;
/*!40000 ALTER TABLE `identity_attributes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `internet_kassa`
--

DROP TABLE IF EXISTS `internet_kassa`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `internet_kassa` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `service_task_id` int unsigned DEFAULT NULL,
  `status` enum('Open','Pending','Paid') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `paid_by_link_id` int unsigned DEFAULT NULL,
  `payment_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `polling_round` int unsigned NOT NULL DEFAULT '0',
  `transaction_created_at` datetime DEFAULT NULL,
  `next_poll` datetime DEFAULT NULL,
  `queued` tinyint(1) NOT NULL DEFAULT '0',
  `last_kassa_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `transaction_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `internet_kassa_service_task_id_unique` (`service_task_id`),
  KEY `internet_kassa_status_index` (`status`),
  KEY `internet_kassa_next_poll_index` (`next_poll`),
  KEY `internet_kassa_queued_index` (`queued`),
  KEY `internet_kassa_paid_by_link_id_foreign` (`paid_by_link_id`),
  CONSTRAINT `internet_kassa_paid_by_link_id_foreign` FOREIGN KEY (`paid_by_link_id`) REFERENCES `internet_kassa_links` (`id`),
  CONSTRAINT `internet_kassa_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `internet_kassa`
--

LOCK TABLES `internet_kassa` WRITE;
/*!40000 ALTER TABLE `internet_kassa` DISABLE KEYS */;
/*!40000 ALTER TABLE `internet_kassa` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `internet_kassa_links`
--

DROP TABLE IF EXISTS `internet_kassa_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `internet_kassa_links` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `service_task_response_id` int unsigned DEFAULT NULL,
  `service_task_id` int unsigned DEFAULT NULL,
  `internet_kassa_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `internet_kassa_links_token_unique` (`token`),
  KEY `internet_kassa_links_service_task_response_id_foreign` (`service_task_response_id`),
  KEY `internet_kassa_links_service_task_id_foreign` (`service_task_id`),
  KEY `internet_kassa_links_internet_kassa_id_foreign` (`internet_kassa_id`),
  CONSTRAINT `internet_kassa_links_internet_kassa_id_foreign` FOREIGN KEY (`internet_kassa_id`) REFERENCES `internet_kassa` (`id`) ON DELETE CASCADE,
  CONSTRAINT `internet_kassa_links_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE SET NULL,
  CONSTRAINT `internet_kassa_links_service_task_response_id_foreign` FOREIGN KEY (`service_task_response_id`) REFERENCES `service_task_responses` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `internet_kassa_links`
--

LOCK TABLES `internet_kassa_links` WRITE;
/*!40000 ALTER TABLE `internet_kassa_links` DISABLE KEYS */;
/*!40000 ALTER TABLE `internet_kassa_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `letter_requests`
--

DROP TABLE IF EXISTS `letter_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `letter_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `status` enum('concept','in_progress','sent','undeliverable') NOT NULL,
  `external_id` varchar(20) NOT NULL,
  `mailbox_id` int NOT NULL,
  `address` varchar(255) NOT NULL,
  `cost` decimal(4,2) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `letter_requests_account_id_foreign` (`account_id`),
  CONSTRAINT `letter_requests_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `letter_requests`
--

LOCK TABLES `letter_requests` WRITE;
/*!40000 ALTER TABLE `letter_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `letter_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `licenses`
--

DROP TABLE IF EXISTS `licenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `licenses` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=91 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `licenses`
--

LOCK TABLES `licenses` WRITE;
/*!40000 ALTER TABLE `licenses` DISABLE KEYS */;
INSERT INTO `licenses` VALUES (1,'accounting_nl','Accounting NL Basic',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(2,'accounting_uk','Accounting UK Basic',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(3,'accounting_be','Accounting BE Basic',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(4,'health_nl','Health NL Basic',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(5,'custom_vvocm','CUS VvOCM',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(6,'custom_zwaartekracht','CUS Zwaartekracht',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(7,'custom_link','Custom Link',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(8,'video_DEPRECATED','ZZZ-deprecated Video Widget',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(9,'securelogin','SecureLogin',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(10,'banner_DEPRECATED','ZZZ-deprecated Banner Widget',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(11,'custom_koenenenco','CUS Koenenenco',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(12,'custom_forms','CUS DRV',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(13,'twitter_DEPRECATED','ZZZ-deprecated Twitter Widget',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(14,'communication','SL Communication',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(15,'uri_launcher','SL URI Launcher',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(16,'custom_finhill','CUS Finhill',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(17,'custom_vanwezel','CUS Van Wezel',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(18,'wistia_DEPRECATED','ZZZ-deprecated Wistia',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(19,'custom_schipper','CUS Schipper',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(20,'accounting_global','Accounting Global',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(21,'accounting_ext_nl','Accounting NL Browser Extension',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(22,'accounting_ext_uk','Accounting UK Browser Extension',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(23,'accounting_ext_be','Accounting BE Browser Extension',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(24,'accounting_ext_global','Accounting Global Browser Extension',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(25,'legal_nl','Juridisch NL',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(26,'notifications_DEPRECATED','ZZZ-deprecated SL Notifications',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(27,'custom_wea','CUS WEA Zuidwest',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(28,'shopping_global','Shopping Global',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(29,'shopping_nl','Shopping NL',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(30,'shopping_uk','Shopping UK',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(31,'shopping_be','Shopping BE',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(32,'custom_complyport','CUS Complyport',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(33,'accounting_de','Accounting DE Basic',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(34,'funds_nl','Pensioenfondsen',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(35,'accounting_dk','Accounting DK Basic',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(36,'pti','CUS PTI',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(37,'custom_ifiac','CUS Ifiac',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(38,'custom_wvdb','CUS WVDB',NULL,'2018-05-23 09:03:02',NULL,'2018-05-23 09:03:02',NULL),(39,'custom_oovb','CUS OOvB',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(40,'wordpress','SL WordPress',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(41,'partner_fiscount','RES Fiscount',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(42,'partner_unidis','RES Unidis',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(43,'testing','Testing',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(44,'custom_zecuur','CUS Zecuur',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(45,'custom_jouwcijfers','CUS Jouwcijfers',NULL,'2018-05-23 09:03:03',NULL,'2018-05-23 09:03:03',NULL),(46,'custom_hoekenblok','CUS hoekenblok',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(47,'totp','TOTP',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(48,'fallback','Fallback',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(49,'jeugdformaat','Jeugdformaat',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(50,'custom_fidor','CUS Fidor',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(51,'companies','Companies',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(52,'custom_tioga_tours','CUS Tioga Tours',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(53,'afas_openid','AFAS OpenID',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(54,'custom_noab','CUS NOAB',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(55,'custom_meyer','CUS Meyer',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(56,'eherkenning','eHerkenning',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(57,'loket_oauth','Loket OAuth',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(58,'sms_feed','MessageBird SMS Feed',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(59,'widget_propagation','Widget Propagation',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(60,'branding','Branding',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(61,'ip_whitelist','IP Whitelisting',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(62,'securelogin_safe','SecureLogin Safe',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(63,'custom_domain','Custom Domain',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(64,'new_ui','New UI',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(65,'new_ui_manager','New UI for managers',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(66,'hide_old_ui','Hide the old UI',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(67,'services','Services',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(68,'joost_acceptatie','Joost Online Acceptatie',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(69,'vat_declaration','Services: VAT Declarations',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(70,'intellifin_openid','Intellifin OpenID',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(71,'twinfield_openid','Twinfield OpenID',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(72,'twinfield_testing','Twinfield testing',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(73,'custom_fleetaccess','CUS Fleetaccess',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(74,'custom_van_braak','CUS van Braak',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(75,'custom_van_oers','CUS Van Oers',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(76,'custom_4pz','CUS 4PZ',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(77,'custom_basecone','CUS Basecone',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(78,'open_questions','Open Questions',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(79,'cus_haak_baak','CUS Haak & Baak',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(80,'cus_mulderij_en_partners','CUS Mulderij & Partners',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(81,'cus_rsult','CUS Rsult',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(82,'loket_credentials','Loket credentials',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(83,'custom_mth','CUS MTH',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(84,'no_front_auth','No front auth',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(85,'custom_qwoater','CUS Qwoater',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(86,'custom_acuity','CUS Acuity',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(87,'bizcuit','Bizcuit',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(88,'whatsapp_business','WhatsApp Business',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(89,'paperproof','Paperproof',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL),(90,'postbode','Postbode',NULL,'2024-03-21 08:41:18',NULL,'2024-03-21 08:41:18',NULL);
/*!40000 ALTER TABLE `licenses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `logins_per_day`
--

DROP TABLE IF EXISTS `logins_per_day`;
/*!50001 DROP VIEW IF EXISTS `logins_per_day`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `logins_per_day` AS SELECT 
 1 AS `date`,
 1 AS `login_actions`,
 1 AS `unique_users`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `logins_per_week`
--

DROP TABLE IF EXISTS `logins_per_week`;
/*!50001 DROP VIEW IF EXISTS `logins_per_week`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `logins_per_week` AS SELECT 
 1 AS `year`,
 1 AS `week`,
 1 AS `login_actions`,
 1 AS `unique_users`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `logius_authorization_email_setting_users`
--

DROP TABLE IF EXISTS `logius_authorization_email_setting_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logius_authorization_email_setting_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_user_id` int unsigned NOT NULL,
  `identifier_id` int unsigned NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `logius_auth_email_setting_users_company_user_id_foreign` (`company_user_id`),
  KEY `logius_auth_email_setting_users_identifier_id_foreign` (`identifier_id`),
  KEY `logius_auth_email_setting_users_user_id_foreign` (`created_by`),
  CONSTRAINT `logius_auth_email_setting_users_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `logius_auth_email_setting_users_identifier_id_foreign` FOREIGN KEY (`identifier_id`) REFERENCES `company_identifiers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `logius_auth_email_setting_users_user_id_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `logius_authorization_email_setting_users`
--

LOCK TABLES `logius_authorization_email_setting_users` WRITE;
/*!40000 ALTER TABLE `logius_authorization_email_setting_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `logius_authorization_email_setting_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `logius_authorization_types`
--

DROP TABLE IF EXISTS `logius_authorization_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logius_authorization_types` (
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  UNIQUE KEY `logius_authorization_type_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `logius_authorization_types`
--

LOCK TABLES `logius_authorization_types` WRITE;
/*!40000 ALTER TABLE `logius_authorization_types` DISABLE KEYS */;
INSERT INTO `logius_authorization_types` VALUES ('DoMa_IH'),('DoMa_OB'),('DoMa_TSG'),('DoMa_VpB');
/*!40000 ALTER TABLE `logius_authorization_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `logius_authorizations`
--

DROP TABLE IF EXISTS `logius_authorizations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logius_authorizations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_identifier_id` int unsigned NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `condition` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `retracted_at` timestamp NULL DEFAULT NULL,
  `send_email_at` timestamp NULL DEFAULT NULL,
  `email_sent_at` timestamp NULL DEFAULT NULL,
  `activated_at` datetime DEFAULT NULL,
  `activated_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `logius_authorizations_account_foreign` (`account_id`),
  KEY `logius_authorizations_identifier_foreign` (`company_identifier_id`),
  KEY `logius_authorizations_type_foreign` (`type`),
  KEY `logius_authorizations_send_email_at_index` (`send_email_at`),
  CONSTRAINT `logius_authorizations_account_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `logius_authorizations_identifier_foreign` FOREIGN KEY (`company_identifier_id`) REFERENCES `company_identifiers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `logius_authorizations_type_foreign` FOREIGN KEY (`type`) REFERENCES `logius_authorization_types` (`name`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `logius_authorizations`
--

LOCK TABLES `logius_authorizations` WRITE;
/*!40000 ALTER TABLE `logius_authorizations` DISABLE KEYS */;
/*!40000 ALTER TABLE `logius_authorizations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `logius_requests`
--

DROP TABLE IF EXISTS `logius_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logius_requests` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `service_task_id` int unsigned DEFAULT NULL,
  `logius_authorization_id` int unsigned DEFAULT NULL,
  `external_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `round` int unsigned DEFAULT NULL,
  `last_supplied_date` datetime DEFAULT NULL,
  `queued_date` datetime DEFAULT NULL,
  `tries` int unsigned NOT NULL DEFAULT '0',
  `check_after_date` datetime DEFAULT NULL,
  `status` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'new',
  `last_checked_date` datetime DEFAULT NULL,
  `response_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `response_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `error_description` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `response_timestamp` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `logius_requests_external_id_unique` (`external_id`),
  KEY `logius_requests_account_id_foreign` (`account_id`),
  KEY `logius_requests_service_task_id_foreign` (`service_task_id`),
  KEY `logius_requests_logius_authorization_id_foreign` (`logius_authorization_id`),
  CONSTRAINT `logius_requests_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `logius_requests_logius_authorization_id_foreign` FOREIGN KEY (`logius_authorization_id`) REFERENCES `logius_authorizations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `logius_requests_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `logius_requests`
--

LOCK TABLES `logius_requests` WRITE;
/*!40000 ALTER TABLE `logius_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `logius_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `memberships`
--

DROP TABLE IF EXISTS `memberships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `memberships` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `user_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `context_id` int unsigned NOT NULL,
  `context_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `account_id` int unsigned NOT NULL,
  `type` enum('member','manager') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'member',
  `level` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `memberships_user_id_context_id_unique` (`user_id`,`context_id`),
  KEY `memberships_deleted_at_index` (`deleted_at`),
  KEY `memberships_account_id_foreign` (`account_id`),
  KEY `memberships_context_id_foreign` (`context_id`),
  CONSTRAINT `memberships_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `memberships_context_id_foreign` FOREIGN KEY (`context_id`) REFERENCES `contexts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `memberships_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `memberships`
--

LOCK TABLES `memberships` WRITE;
/*!40000 ALTER TABLE `memberships` DISABLE KEYS */;
INSERT INTO `memberships` VALUES (1,1,'Admin SecureLogin',1,'/SecureLogin',1,'manager',-1,'2016-05-17 08:55:36',NULL,'2016-05-17 08:55:36',NULL,NULL,NULL);
/*!40000 ALTER TABLE `memberships` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `batch` int NOT NULL,
  UNIQUE KEY `migration` (`migration`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES ('2019_12_14_000001_create_personal_access_tokens_table',52),('2021_09_17_154205_add_user_to_open_questions_attachments',51),('2021_10_25_123540_add_columns_to_open_questions_emails_settings_accounts_table',51),('2021_10_26_093210_create_account_billing_products_table',51),('2021_10_27_153736_add_enum_option_none_to_all_permissions',51),('2021_10_28_094658_create_account_billings_table',51),('2021_11_08_133044_add_internet_kassa_payment_url',51),('2021_11_08_133140_extend_replaces_column_for_open_question_audit_log_table',51),('2021_11_22_133000_add_columns_to_open_questions_emails_settings_companies_table',52),('2021_11_23_190132_udpate_account_billings_add_cascade',52),('2021_11_23_199359_update_account_billings_product_codes_table',52),('2021_11_24_120006_update_service_tasks_forgein_key_constraint',52),('2021_11_24_145102_update_task_files_content_column',52),('2021_11_25_131321_company_wage_tax_period',52),('2021_11_25_155800_add_primary_key_to_graph_tables',52),('2021_11_30_090500_add_last_sent_column_to_open_questions_table',52),('2021_12_06_131732_update_companies_table_drop_last_retrieval_column',52),('2021_12_08_113552_create_tracking_pixel_mongo_collection',52),('2021_12_13_140324_add_property_client_types_for_afas_crm_account_services',52),('2021_12_13_163700_add_open_question_dms_preferences_table',52),('2021_12_22_100200_create_open_questions_dms_preferences_table',52),('2022_01_03_112046_update_open_questions_bookkeeping_description',52),('2022_01_07_103430_add_upload_types_to_caseware',52),('2022_01_07_140123_create_uploads_table',52),('2022_01_07_140207_create_uploaded_files_table',52),('2022_01_10_090933_update_open_questions_emails_settings_accounts_frequency',52),('2022_01_10_101011_update_open_questions_emails_settings_companies_frequency',52),('2022_01_14_111124_create_table_upload_service_companies',52),('2022_01_14_132059_add_delete_at_to_uploads_table',52),('2022_01_17_152200_fix_for_frequency_not_null',52),('2022_01_19_131454_add_note_field',52),('2022_01_20_152834_increase_external_name_size_in_open_questions_external_companies_table',52),('2022_01_21_154351_add_billable_companies_file_uplaods_to_stats',52),('2022_01_21_164614_create_table_open_questions_templates_answers_remarks',52),('2022_01_24_143900_add_uploaded_file_company_dms_preferences_table',52),('2022_01_26_094615_add_unique_constraint_to_account_billings_table',52),('2022_01_28_110917_clean_generic_widgets_table',52),('2022_01_28_140700_add_table_for_dms_entries_relating_uploaded_files',52),('2022_01_31_103700_create_logius_auth_requests_table',52),('2022_01_31_114242_add_required_column_to_open_question_template_fields_table',52),('2022_02_01_103238_add_required_column_to_open_question_template_answers_table',52),('2022_02_02_121419_create_logius_auth__email_setting_users_table',52),('2022_02_09_170657_add_authorization_id_to_logius_requests_table',52),('2022_02_11_092318_updateroundlogiusrequest',52),('2022_02_11_144521_addactivatedbytoauthorization',52),('2022_02_16_134333_add_type_to_open_question_template_field_types_table',52),('2022_02_21_090200_add_options_to_open_question_template_fields_table',52),('2022_02_22_092641_add_options_to_open_question_template_answers_table',52),('2022_02_23_084922_addremindedattoresponses',52),('2022_02_28_142800_create_open_question_fiscal_table',52),('2022_03_02_115610_create_open_questions_custom_types_table',52),('2022_03_02_121029_add_type_id_to_open_questions_table',52),('2022_03_10_161436_add_subject_column_to_open_questions_custom_types_table',52),('2022_03_11_164446_update_length_of_column_message_on_uploaded_file_dms_entries_table',52),('2022_03_15_135920_create_qualified_signing_users',52),('2022_03_21_220446_make_service_id_nullable_in_service_task_signing_requests_table',52),('2022_03_22_120000_custom_types_cleanup',52),('2022_04_06_114057_add_qualified_signing_users_to_stats',52),('2022_04_11_164930_update_logius_request_response_code_type',52),('2022_04_12_083808_update_uploaded_file_dms_entries_table_link_url_length',52),('2022_05_03_112119_add_year_to_service_tasks_table',52),('2022_05_10_104457_create_custom_rules_table',52),('2022_05_10_104533_create_custom_rule_conditions_table',52),('2022_05_10_104705_create_custom_rule_users_table',52),('2022_05_11_103349_update_service_tasks_table_year_required',52),('2022_05_11_111000_add_locked_column_to_company_identifiers_table',52),('2022_05_11_114925_add_data_start_date_end_columns_to_task_files_table',52),('2022_05_23_142503_add_tab_name_to_task_files_table',52),('2022_05_30_091204_create_task_file_trash_table',52),('2022_06_03_121747_move_yearwork_reminder_to_custom_rules',52),('2022_06_07_120000_create_user_expiration_table',52),('2022_06_07_131953_add_settings_column_to_companies_table',52),('2022_06_09_144000_add_timestamps_to_role_user_table_table',52),('2022_06_22_180937_remove_old_migrations',52),('2022_06_27_111917_add_instant_uuid_to_task_files_table',52),('2022_06_29_114400_add_deleted_by_column_to_uploaded_files_table',52),('2022_07_05_140541_add_column_id_to_account_services_snelstart_table',52),('2022_07_12_111047_change_properties_to_hyarchys_service',52),('2022_07_18_120553_create_dossier_folders_table',52),('2022_07_18_122753_create_dossier_files_table',52),('2022_07_18_123509_create_dossier_audit_logs_table',52),('2022_07_25_164050_update_dossier_files_table',52),('2022_07_29_160812_update_dossier_logs_table',52),('2022_08_03_105817_make_account_id_nullable_on_translations_table',52),('2022_08_16_114238_create_context_companies_table',52),('2022_08_16_114540_create_company_user_context_companies_table',52),('2022_08_22_162853_increase_column_name_size_on_dossier_folders_table',52),('2022_08_29_103145_create_ribbon_notifications_table',52),('2022_08_30_112137_create_ribbon_notification_recipients_table',52),('2022_08_30_165554_add_public_url_column_to_open_questions_bookkeeping_table',52),('2022_09_06_105332_add_size_to_dossier_files_table',52),('2022_09_07_131054_add_dossier_files_disk_usage_to_stats',52),('2022_09_07_144624_add_property_simplicate_service',52),('2022_10_03_123722_add_task_files_table_order',52),('2022_10_05_134921_create_user_reactivation_requests_table',52),('2022_10_12_161209_change_file_size_to_int_in_uploaded_files_table',52),('2022_10_13_104000_make_default_auth_method_null_on_users_table',52),('2022_10_18_104900_migrate_widget_settings_for_pensioenfonds_widgets',52),('2022_11_07_110022_create_sbr_recipients_table',52),('2022_11_07_110049_create_sbr_requests_table',52),('2022_11_09_104952_add_front_auth_method_to_users_table',52),('2022_11_14_120756_add_error_desciption_to_sbr_requests_table',52),('2022_11_15_154759_add_sbr_to_service_task_status_table',52),('2022_11_18_135843_remove_type_column_from_open_questions_table',52),('2022_11_28_115110_update_email_note_limit_for_service_tasks_table',52),('2022_12_06_124338_increaseopenquestionstitle',52),('2022_12_13_161600_change_sent_by_to_nullable_for_uploaded_files_dms_entries',52),('2022_12_15_140932_add_external_id_to_companies_table',52),('2022_12_15_150809_update_open_question_fiscal_table',52),('2022_12_19_135814_add_internal_client_id_to_users',52),('2022_12_20_155014_create_dossier_file_users_table',52),('2022_12_20_155533_add_service_task_id_and_task_file_id_columns_to_dossier_files_table',52),('2022_12_22_095500_add_foreign_key_to_dms_preference_table',52),('2022_12_28_144700_update_email_note_for_service_tasks_table',52),('2022_12_30_130330_alter_foreign_constraint_in_company_identifiers_table',52),('2023_01_02_115916_add_fields_open_questions_table',53),('2023_01_02_161543_update_open_questions_setting_users_with_category',53),('2023_01_03_144950_add_recipient_type_to_open_questions_audit_logs',53),('2023_01_05_141722_add_signed_docs_to_acc_stats',53),('2023_01_17_111607_add_primary_key_to_account_service_users_table',53),('2023_01_17_150752_update_tokens_table_add_unique_constraint',53),('2023_01_30_112358_add_id_to_external_companies',53),('2023_01_30_113013_create_open_questions_external_ledger',53),('2023_02_01_153403_add_office_code_to_external_companies',53),('2023_02_02_092700_add_signing_order_column_to_task_file_placeholders_table',53),('2023_02_10_154300_add_signing_order_columns_to_service_tasks_table',53),('2023_02_13_132016_create_webhooks_table',53),('2023_02_13_132031_create_webhook_conditions_table',53),('2023_02_15_131905_create_table_email_templates',53),('2023_02_15_132011_create_table_email_template_fields',53),('2023_02_15_132123_create_table_email_template_conditions',53),('2023_02_27_170205_alter_front_auth_method_column_in_users_table',53),('2023_03_03_174500_add_auto_fill_column_to_task_files_table',53),('2023_03_13_113740_create_shared_file_bundles_table',53),('2023_03_13_115303_create_shared_files_table',53),('2023_03_13_120508_create_shared_file_audit_logs',53),('2023_03_13_121404_create_shared_file_bundle_users',53),('2023_03_14_105950_create_open_question_users_table',53),('2023_03_17_111722_add_uuid_to_service_task_groups',53),('2023_03_17_141222_add_group_uuid_to_service_tasks',53),('2023_03_21_152347_add_column_group_to_task_file_placeholders_table',53),('2023_03_28_131832_changedefaultcharencoding',53),('2023_03_28_133019_drop_wizard_tables',53),('2023_03_28_140100_migrate_send_task_reminder_to_send_reminder_in_custom_rules',53),('2023_03_28_141121_utf8mb4_account_service',53),('2023_03_28_141121_utf8mb4_accounts',53),('2023_03_28_141121_utf8mb4_companies',53),('2023_03_28_141121_utf8mb4_consumers',53),('2023_03_28_141121_utf8mb4_custom_rules',53),('2023_03_28_141121_utf8mb4_dashboard',53),('2023_03_28_141121_utf8mb4_declarations',53),('2023_03_28_141121_utf8mb4_email_templates',53),('2023_03_28_141121_utf8mb4_internet_kassa',53),('2023_03_28_141121_utf8mb4_memberships',53),('2023_03_28_141121_utf8mb4_ribbon_notifications',53),('2023_03_28_141121_utf8mb4_services',53),('2023_03_28_141121_utf8mb4_translations',53),('2023_03_28_141121_utf8mb4_uploaded_files',53),('2023_03_28_141121_utf8mb4_users',53),('2023_03_28_141121_utf8mb4_webhooks',53),('2023_03_28_141121_utf8mb4_widgets',53),('2023_04_03_141121_utf8mb4_account_billing',53),('2023_04_03_141121_utf8mb4_company_related',53),('2023_04_03_141121_utf8mb4_contexts',53),('2023_04_03_141121_utf8mb4_dossier',53),('2023_04_03_141121_utf8mb4_failed_jobs',53),('2023_04_03_141121_utf8mb4_logius',53),('2023_04_03_141121_utf8mb4_migrations',53),('2023_04_03_141121_utf8mb4_open_question_templates',53),('2023_04_03_141121_utf8mb4_open_questions',53),('2023_04_03_141121_utf8mb4_personal_access_tokens',53),('2023_04_03_141121_utf8mb4_sbr',53),('2023_04_03_141121_utf8mb4_shared_files',53),('2023_04_03_141121_utf8mb4_short_links',53),('2023_04_03_141121_utf8mb4_signing',53),('2023_04_03_141121_utf8mb4_task_files',53),('2023_04_03_141121_utf8mb4_tasks',53),('2023_04_03_141121_utf8mb4_uploaded_files',53),('2023_04_03_141121_utf8mb4_zzz',53),('2023_04_04_102858_make_logo_circle_column_nullable_in_accounts_table',53),('2023_04_04_125900_drop_table_jobs',53),('2023_04_05_105732_delete_exact_division_timestamps_table',53),('2023_04_05_111446_delete_password_resets_table',53),('2023_04_05_112840_delete_actions_table',53),('2023_04_06_095537_delete_old_identifier_tables',53),('2023_04_07_100747_update_tokens_table_widget_id_foreign_key_to_cascade',53),('2023_04_07_102650_update_token_id_foreign_key_contraint_for_account_services_snelstart_table',53),('2023_04_11_105428_add_note_to_table_open_questions_template_fields',53),('2023_04_11_105506_add_note_to_table_open_questions_template_answers',53),('2023_04_13_163805_delete_translations',53),('2023_04_19_123100_delete_events_table',53),('2023_04_19_133908_change_foreign_key_for_dossier_audit_logs_table',53),('2023_04_21_141924_update_notifications_table',53),('2023_04_25_155325_add_secure_share_count_to_zzz_view_acc_stats_table',53),('2023_04_28_125159_servicetaskgroupprimarykey',53),('2023_05_02_110000_delete_widget_requests_table',53),('2023_05_02_141002_taskgroupaccountforeignkey',53),('2023_05_04_134700_change_foreign_key_for_open_question_templates_table',53),('2023_05_17_141459_drop_table_service_task_pdf_sign_requests',53),('2023_06_05_120912_set_status_expiration_to_trial_accounts',54),('2023_06_07_155615_add_columns_to_context_companies',54),('2023_06_19_141700_add_columns_to_account_billing_products',54),('2023_06_19_144202_create_table_open_questions_clients',54),('2023_06_19_150454_add_open_questions_client_category',54),('2023_07_05_123800_create_dossier_folder_users_table',54),('2023_07_07_123004_create_open_questions_unverified_table',54),('2023_07_07_133004_create_open_questions_unverified_files_table',54),('2023_08_08_142110_create_table_communication_channels',54),('2023_08_08_142533_create_user_communication_channels_table',54),('2023_08_09_123621_create_whatsapp_user_last_message_table',54),('2023_08_10_112400_create_whatsapp_templates_table',54),('2023_08_10_113738_add_whatsapp_users_count_to_zzz_view_acc_stats_table',54),('2023_08_16_133916_add_fields_to_zzz_view_acc_stats_table',54),('2023_08_17_124353_add_accounts_uuid',54),('2023_08_17_134353_add_companies_uuid',54),('2023_08_17_134403_add_company_identifiers_uuid',54),('2023_08_28_162000_add_sanctum_expires_at_for_package_upgrade',55),('2023_08_30_165917_create_pdf_backgrounds_table',55),('2023_09_05_151226_create_table_task_file_background',55),('2023_09_06_102827__increase_size_of_ip_address_column_in_shared_file_audit_logs_table',55),('2023_10_03_171402_add_unique_constraint_on_context_companies_table',55),('2023_10_09_111223_add_anchor_tag_column_to_task_file_placeholders_table',55),('2023_10_10_112059_update_company_ocr_emails_table',55),('2023_10_10_131624_create_open_question_missing_invoice_types_table',55),('2023_10_12_112200_add_ocr_tags_table',55),('2023_10_16_110025_add_settings_column_to_templates',55),('2023_10_20_144300_increase_size_of_ip_address_column',55),('2023_10_23_103820_create_default_templates_table',55),('2023_10_24_151331_create_export_types_table',55),('2023_10_24_151421_create_exports_table',55),('2023_10_30_105811_add_deleted_at_column_to_shared_file_bundles_table',55),('2023_11_06_111400_increase_size_of_open_question_note_column_in_open_questions_table',55),('2023_11_06_112900_increase_size_of_replaces_column_in_open_questions_audit_logs_table',55),('2023_11_06_141434_create_open_questions_yearwork',55),('2023_11_06_153128_add_open_question_yearwork_category',55),('2023_11_06_163800_index_created_at_column_for_task_files_table',55),('2023_11_07_114810_add_type_to_ribbon_notifications_table',55),('2023_11_07_124931_create_ribbon_notification_emails_table',55),('2023_11_17_130547_add_yearly_account_billing_column_to_account_billing_products_table',55),('2023_11_21_142028_create_upload_type_fields',55),('2023_11_22_111839_migrare_sdu_indicator_to_sdu_fiscaal',55),('2023_11_27_162100_expand_message_field_for_task_audit_log_table',55),('2023_12_05_100931_create_service_task_associations_table',55),('2023_12_13_110925_create_service_task_user_permissions_table',55),('2023_12_20_100215_create_task_file_paperproof_table',55),('2024_01_09_151128_migrate_widget_akkermans_and_partners',55),('2024_01_10_105001_remove_task_group_uuid_constraint',55),('2024_01_17_100949_add_fields_to_ocr_table',55),('2024_01_19_133500_remove_widget_monitoring_tables',55),('2024_01_19_145200_remove_widget_monitoring_columns_from_generic_widgets_table',55),('2024_01_22_133428_migrate_widget_pensioenaangifte_apg',55),('2024_01_29_160244_create_communication_subjects_table',55),('2024_02_07_163925_create_account_communication_channels_table',55),('2024_02_07_163943_create_user_communication_channels_preferences_table',55),('2024_02_14_102044_letter_requests',55),('2024_02_19_143728_letter_requests_cost',55),('2024_02_19_144920_view_acc_stats_postbode',55),('2024_02_20_184733_replace_pb_number_column_with_external_id_in_letter_requests_table',55),('2024_02_22_1111100_add_order_column_to_communication_channels_table',55),('2024_02_26_110551_remove_zzz_wekly_user_data',55),('2024_02_29_162742_change_letter_requests_table',55),('2024_02_29_163335_create_task_file_letter_requests_table',55),('2024_03_11_114300_add_table_for_declaration_questions',55),('2024_03_11_163600_add_declaration_as_an_enum_type_for_open_question_types_subject',55),('2024_03_15_110306_add_deleted_at_to_letter_requests_table',55),('2024_03_18_182711_add_column_account_service_upload_type_id_to_service_tasks_table',55);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `recipient_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `recipient_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `origin_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `origin_id` int unsigned NOT NULL,
  `origin_account_id` int unsigned DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `references` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `push_channels` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `active_from` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `priority` int DEFAULT '0',
  `read` int NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_user_id_foreign` (`user_id`),
  KEY `notifications_origin_account_id_foreign` (`origin_account_id`),
  CONSTRAINT `notifications_origin_account_id_foreign` FOREIGN KEY (`origin_account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES (1,1,'App\\User','[1]','gUzEzjBpucK5f0QUr6X6v4jbYpQy7ulf8Yu25S5k5b0e8b3767ae1','App\\Account',1,1,'Gebruiker Fabiano 2 is geactiveerd','De onderstaande gebruiker is geactiveerd\n\nNaam: Fabiano 2\nGebruikersnaam: fabiano2\nE-mail: <EMAIL>\n\n2018-05-30 13:29:59 / 127.0.0.1','[]','[]',NULL,NULL,0,0,'2018-05-30 11:29:59',1,'2018-05-30 11:29:59',1,NULL,NULL);
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ocr_tags`
--

DROP TABLE IF EXISTS `ocr_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ocr_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `type` varchar(255) NOT NULL,
  `tag` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ocr_tags_account_id_type_unique` (`account_id`,`type`),
  CONSTRAINT `ocr_tags_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ocr_tags`
--

LOCK TABLES `ocr_tags` WRITE;
/*!40000 ALTER TABLE `ocr_tags` DISABLE KEYS */;
/*!40000 ALTER TABLE `ocr_tags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_categories`
--

DROP TABLE IF EXISTS `open_question_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_categories` (
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  UNIQUE KEY `open_question_categories_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_categories`
--

LOCK TABLES `open_question_categories` WRITE;
/*!40000 ALTER TABLE `open_question_categories` DISABLE KEYS */;
INSERT INTO `open_question_categories` VALUES ('bookkeeping'),('client'),('fiscal'),('ocr'),('other'),('wage'),('yearwork');
/*!40000 ALTER TABLE `open_question_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_company_dms_preferences`
--

DROP TABLE IF EXISTS `open_question_company_dms_preferences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_company_dms_preferences` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `preferences` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `created_at` timestamp NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_foreign` (`company_id`),
  KEY `account_service_foreign` (`account_service_id`),
  CONSTRAINT `account_service_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_company_dms_preferences`
--

LOCK TABLES `open_question_company_dms_preferences` WRITE;
/*!40000 ALTER TABLE `open_question_company_dms_preferences` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_company_dms_preferences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_declarations`
--

DROP TABLE IF EXISTS `open_question_declarations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_declarations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `external_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_question_declarations_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_question_declarations_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_declarations`
--

LOCK TABLES `open_question_declarations` WRITE;
/*!40000 ALTER TABLE `open_question_declarations` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_declarations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_dms_preferences`
--

DROP TABLE IF EXISTS `open_question_dms_preferences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_dms_preferences` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_at` timestamp NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_question_foreign` (`open_question_id`),
  KEY `open_question_dms_preference_account_service_foreign` (`account_service_id`),
  CONSTRAINT `open_question_dms_preference_account_service_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_question_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_dms_preferences`
--

LOCK TABLES `open_question_dms_preferences` WRITE;
/*!40000 ALTER TABLE `open_question_dms_preferences` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_dms_preferences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_missing_invoice_types`
--

DROP TABLE IF EXISTS `open_question_missing_invoice_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_missing_invoice_types` (
  `open_question_id` int unsigned NOT NULL,
  `type` enum('purchase','sale') NOT NULL,
  UNIQUE KEY `open_question_missing_invoice_types_open_question_id_type_unique` (`open_question_id`,`type`),
  CONSTRAINT `open_question_missing_invoice_types_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_missing_invoice_types`
--

LOCK TABLES `open_question_missing_invoice_types` WRITE;
/*!40000 ALTER TABLE `open_question_missing_invoice_types` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_missing_invoice_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_template_answer_attachments`
--

DROP TABLE IF EXISTS `open_question_template_answer_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_template_answer_attachments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `template_answer_id` bigint unsigned NOT NULL,
  `filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hash` char(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `template_answer_id_foreign` (`template_answer_id`),
  CONSTRAINT `template_answer_id_foreign` FOREIGN KEY (`template_answer_id`) REFERENCES `open_question_template_answers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_template_answer_attachments`
--

LOCK TABLES `open_question_template_answer_attachments` WRITE;
/*!40000 ALTER TABLE `open_question_template_answer_attachments` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_template_answer_attachments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_template_answer_remarks`
--

DROP TABLE IF EXISTS `open_question_template_answer_remarks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_template_answer_remarks` (
  `template_answer_id` bigint unsigned NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  KEY `open_question_template_answer_remarks_template_answer_id_foreign` (`template_answer_id`),
  CONSTRAINT `open_question_template_answer_remarks_template_answer_id_foreign` FOREIGN KEY (`template_answer_id`) REFERENCES `open_question_template_answers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_template_answer_remarks`
--

LOCK TABLES `open_question_template_answer_remarks` WRITE;
/*!40000 ALTER TABLE `open_question_template_answer_remarks` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_template_answer_remarks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_template_answers`
--

DROP TABLE IF EXISTS `open_question_template_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_template_answers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `template_entry_id` bigint unsigned NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `options` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `answer` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `order` int unsigned NOT NULL DEFAULT '0',
  `required` tinyint(1) NOT NULL DEFAULT '1',
  `info_note` varchar(1000) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_question_template_answers_template_entry_id_foreign` (`template_entry_id`),
  KEY `open_question_template_answers_type_foreign` (`type`),
  CONSTRAINT `open_question_template_answers_template_entry_id_foreign` FOREIGN KEY (`template_entry_id`) REFERENCES `open_question_template_entries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_question_template_answers_type_foreign` FOREIGN KEY (`type`) REFERENCES `open_question_template_field_types` (`name`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_template_answers`
--

LOCK TABLES `open_question_template_answers` WRITE;
/*!40000 ALTER TABLE `open_question_template_answers` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_template_answers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_template_entries`
--

DROP TABLE IF EXISTS `open_question_template_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_template_entries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  `template_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_question_template_entries_open_question_id_foreign` (`open_question_id`),
  KEY `open_question_template_entries_template_id_foreign` (`template_id`),
  CONSTRAINT `open_question_template_entries_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_question_template_entries_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `open_question_templates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_template_entries`
--

LOCK TABLES `open_question_template_entries` WRITE;
/*!40000 ALTER TABLE `open_question_template_entries` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_template_entries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_template_field_types`
--

DROP TABLE IF EXISTS `open_question_template_field_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_template_field_types` (
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  UNIQUE KEY `open_question_template_field_types_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_template_field_types`
--

LOCK TABLES `open_question_template_field_types` WRITE;
/*!40000 ALTER TABLE `open_question_template_field_types` DISABLE KEYS */;
INSERT INTO `open_question_template_field_types` VALUES ('checkbox'),('multiple_checkboxes'),('multitext'),('text'),('upload_file');
/*!40000 ALTER TABLE `open_question_template_field_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_template_fields`
--

DROP TABLE IF EXISTS `open_question_template_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_template_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `template_id` bigint unsigned NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `options` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `order` int unsigned NOT NULL DEFAULT '0',
  `required` tinyint(1) NOT NULL DEFAULT '1',
  `info_note` varchar(1000) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_question_template_fields_template_id_foreign` (`template_id`),
  KEY `open_question_template_fields_type_foreign` (`type`),
  CONSTRAINT `open_question_template_fields_template_id_foreign` FOREIGN KEY (`template_id`) REFERENCES `open_question_templates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_question_template_fields_type_foreign` FOREIGN KEY (`type`) REFERENCES `open_question_template_field_types` (`name`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_template_fields`
--

LOCK TABLES `open_question_template_fields` WRITE;
/*!40000 ALTER TABLE `open_question_template_fields` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_template_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_templates`
--

DROP TABLE IF EXISTS `open_question_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `intro_text` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `settings` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `archived_at` timestamp NULL DEFAULT NULL,
  `archived_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_question_templates_account_id_foreign` (`account_id`),
  KEY `open_question_templates_category_foreign` (`category`),
  CONSTRAINT `open_question_templates_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_question_templates_category_foreign` FOREIGN KEY (`category`) REFERENCES `open_question_categories` (`name`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_templates`
--

LOCK TABLES `open_question_templates` WRITE;
/*!40000 ALTER TABLE `open_question_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_types`
--

DROP TABLE IF EXISTS `open_question_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_types` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `display_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `order` tinyint NOT NULL,
  `subject` enum('company','employee','declaration') NOT NULL DEFAULT 'company',
  `preferences` json DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `account_service_id` int unsigned NOT NULL,
  `created_at` timestamp NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_custom_types_account_service_id_foreign` (`account_service_id`),
  CONSTRAINT `open_questions_custom_types_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_types`
--

LOCK TABLES `open_question_types` WRITE;
/*!40000 ALTER TABLE `open_question_types` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_question_users`
--

DROP TABLE IF EXISTS `open_question_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_question_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `question_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `open_question_users_question_id_user_id_unique` (`question_id`,`user_id`),
  KEY `open_question_users_user_id_foreign` (`user_id`),
  KEY `open_question_users_created_by_foreign` (`created_by`),
  CONSTRAINT `open_question_users_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `open_question_users_question_id_foreign` FOREIGN KEY (`question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_question_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_question_users`
--

LOCK TABLES `open_question_users` WRITE;
/*!40000 ALTER TABLE `open_question_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_question_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions`
--

DROP TABLE IF EXISTS `open_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_service_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `title` varchar(350) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `subtitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type_id` int unsigned DEFAULT NULL,
  `status` enum('open','pending','completed','deleted') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `internal_note` varchar(1000) DEFAULT NULL,
  `attachment_needed` tinyint(1) NOT NULL,
  `urgent` tinyint(1) NOT NULL DEFAULT '0',
  `page_title` varchar(255) DEFAULT NULL,
  `page_url` varchar(255) DEFAULT NULL,
  `last_sent` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_questions_bookkeeping_created_by_foreign` (`created_by`),
  KEY `open_questions_questions_bookkeeping_updated_by_foreign` (`updated_by`),
  KEY `open_questions_questions_bookkeeping_company_id_foreign` (`company_id`),
  KEY `open_questions_questions_bookkeeping_account_service_id_foreign` (`account_service_id`),
  KEY `open_questions_category_foreign` (`category`),
  KEY `open_questions_type_id_foreign` (`type_id`),
  CONSTRAINT `open_questions_category_foreign` FOREIGN KEY (`category`) REFERENCES `open_question_categories` (`name`),
  CONSTRAINT `open_questions_questions_bookkeeping_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_questions_bookkeeping_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_questions_bookkeeping_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `open_questions_questions_bookkeeping_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `open_questions_type_id_foreign` FOREIGN KEY (`type_id`) REFERENCES `open_question_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions`
--

LOCK TABLES `open_questions` WRITE;
/*!40000 ALTER TABLE `open_questions` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_attachments`
--

DROP TABLE IF EXISTS `open_questions_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_attachments` (
  `open_question_id` int unsigned NOT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hash` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` int unsigned NOT NULL,
  KEY `open_questions_attachments_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_attachments_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_attachments`
--

LOCK TABLES `open_questions_attachments` WRITE;
/*!40000 ALTER TABLE `open_questions_attachments` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_attachments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_audit_logs`
--

DROP TABLE IF EXISTS `open_questions_audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_audit_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned DEFAULT NULL,
  `open_question_id` int unsigned DEFAULT NULL,
  `text` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `recipient_type` varchar(30) DEFAULT NULL,
  `replaces` varchar(2048) DEFAULT NULL,
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_audit_logs_user_id_foreign` (`user_id`),
  KEY `open_questions_audit_logs_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_audit_logs_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_audit_logs`
--

LOCK TABLES `open_questions_audit_logs` WRITE;
/*!40000 ALTER TABLE `open_questions_audit_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_audit_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_bookkeeping`
--

DROP TABLE IF EXISTS `open_questions_bookkeeping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_bookkeeping` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `amount` decimal(11,2) NOT NULL,
  `currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `invoice_number` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `bookkeeping_number` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `transaction_date` datetime NOT NULL,
  `public_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_bookkeeping_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_bookkeeping_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_bookkeeping`
--

LOCK TABLES `open_questions_bookkeeping` WRITE;
/*!40000 ALTER TABLE `open_questions_bookkeeping` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_bookkeeping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_client`
--

DROP TABLE IF EXISTS `open_questions_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_client` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  `message` varchar(1000) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_client_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_client_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_client`
--

LOCK TABLES `open_questions_client` WRITE;
/*!40000 ALTER TABLE `open_questions_client` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_client` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_completed_settings_accounts`
--

DROP TABLE IF EXISTS `open_questions_completed_settings_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_completed_settings_accounts` (
  `account_id` int unsigned NOT NULL,
  `automatic_completed` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `open_questions_completed_settings_accounts_account_id_unique` (`account_id`),
  CONSTRAINT `open_questions_completed_settings_accounts_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_completed_settings_accounts`
--

LOCK TABLES `open_questions_completed_settings_accounts` WRITE;
/*!40000 ALTER TABLE `open_questions_completed_settings_accounts` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_completed_settings_accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_emails_settings_accounts`
--

DROP TABLE IF EXISTS `open_questions_emails_settings_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_emails_settings_accounts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `frequency` enum('weekly','biweekly','monthly','manually') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `day` enum('monday','tuesday','wednesday','thursday','friday','saturday','sunday') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'monday',
  `hour` tinyint NOT NULL DEFAULT '10',
  `send_reminder_at` datetime DEFAULT NULL,
  `last_sent` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `open_questions_emails_accounts_account_id_unique` (`account_id`),
  KEY `open_questions_emails_settings_accounts_send_reminder_at_index` (`send_reminder_at`),
  CONSTRAINT `open_questions_emails_accounts_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_emails_settings_accounts`
--

LOCK TABLES `open_questions_emails_settings_accounts` WRITE;
/*!40000 ALTER TABLE `open_questions_emails_settings_accounts` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_emails_settings_accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_emails_settings_companies`
--

DROP TABLE IF EXISTS `open_questions_emails_settings_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_emails_settings_companies` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int unsigned NOT NULL,
  `account_id` int unsigned NOT NULL,
  `frequency` enum('weekly','biweekly','monthly','manually') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `day` enum('monday','tuesday','wednesday','thursday','friday','saturday','sunday') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `hour` tinyint DEFAULT NULL,
  `send_reminder_at` datetime DEFAULT NULL,
  `last_sent` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `open_questions_emails_companies_company_id_unique` (`company_id`),
  KEY `open_questions_emails_settings_companies_send_reminder_at_index` (`send_reminder_at`),
  CONSTRAINT `open_questions_emails_companies_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_emails_settings_companies`
--

LOCK TABLES `open_questions_emails_settings_companies` WRITE;
/*!40000 ALTER TABLE `open_questions_emails_settings_companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_emails_settings_companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_emails_settings_users`
--

DROP TABLE IF EXISTS `open_questions_emails_settings_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_emails_settings_users` (
  `company_user_id` int unsigned NOT NULL,
  `category` varchar(20) NOT NULL DEFAULT 'bookkeeping',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned DEFAULT NULL,
  UNIQUE KEY `open_questions_emails_settings_company_user_id_category` (`company_user_id`,`category`),
  KEY `open_questions_emails_users_settings_created_by_foreign` (`created_by`),
  KEY `open_questions_emails_settings_users_category_foreign` (`category`),
  CONSTRAINT `open_questions_emails_settings_users_category_foreign` FOREIGN KEY (`category`) REFERENCES `open_question_categories` (`name`) ON DELETE RESTRICT,
  CONSTRAINT `open_questions_emails_settings_users_company_user_id_foreign` FOREIGN KEY (`company_user_id`) REFERENCES `companies_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_emails_users_settings_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_emails_settings_users`
--

LOCK TABLES `open_questions_emails_settings_users` WRITE;
/*!40000 ALTER TABLE `open_questions_emails_settings_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_emails_settings_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_emails_users`
--

DROP TABLE IF EXISTS `open_questions_emails_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_emails_users` (
  `user_id` int unsigned NOT NULL,
  `last_sent` datetime NOT NULL,
  UNIQUE KEY `open_questions_emails_users_user_id_unique` (`user_id`),
  CONSTRAINT `open_questions_emails_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_emails_users`
--

LOCK TABLES `open_questions_emails_users` WRITE;
/*!40000 ALTER TABLE `open_questions_emails_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_emails_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_external_companies`
--

DROP TABLE IF EXISTS `open_questions_external_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_external_companies` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_service_id` int unsigned NOT NULL,
  `company_id` int unsigned DEFAULT NULL,
  `external_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `office_code` varchar(50) DEFAULT NULL,
  `external_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `blocked` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_account_service_id_external_id` (`account_service_id`,`external_id`),
  KEY `open_questions_external_companies_company_id_foreign` (`company_id`),
  KEY `open_questions_external_companies_created_by_foreign` (`created_by`),
  KEY `open_questions_external_companies_updated_by_foreign` (`updated_by`),
  CONSTRAINT `open_questions_external_companies_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_external_companies_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_external_companies_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `open_questions_external_companies_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_external_companies`
--

LOCK TABLES `open_questions_external_companies` WRITE;
/*!40000 ALTER TABLE `open_questions_external_companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_external_companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_external_ledger`
--

DROP TABLE IF EXISTS `open_questions_external_ledger`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_external_ledger` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_service_id` int unsigned NOT NULL,
  `external_company_id` int unsigned NOT NULL,
  `open_question_id` int unsigned NOT NULL,
  `ledger_number` varchar(32) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `open_questions_external_ledger_open_question_id_unique` (`open_question_id`),
  KEY `account_service_id_ledger_number_index` (`account_service_id`,`ledger_number`),
  KEY `open_questions_external_ledger_external_company_id_foreign` (`external_company_id`),
  CONSTRAINT `open_questions_external_ledger_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_external_ledger_external_company_id_foreign` FOREIGN KEY (`external_company_id`) REFERENCES `open_questions_external_companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_external_ledger_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_external_ledger`
--

LOCK TABLES `open_questions_external_ledger` WRITE;
/*!40000 ALTER TABLE `open_questions_external_ledger` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_external_ledger` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_fiscal`
--

DROP TABLE IF EXISTS `open_questions_fiscal`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_fiscal` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_fiscal_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_fiscal_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_fiscal`
--

LOCK TABLES `open_questions_fiscal` WRITE;
/*!40000 ALTER TABLE `open_questions_fiscal` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_fiscal` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_ocr`
--

DROP TABLE IF EXISTS `open_questions_ocr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_ocr` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  `public_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_on_date` datetime DEFAULT NULL,
  `amount` decimal(8,2) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `invoice_number` varchar(150) DEFAULT NULL,
  `currency` varchar(10) DEFAULT NULL,
  `transaction_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_ocr_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_ocr_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_ocr`
--

LOCK TABLES `open_questions_ocr` WRITE;
/*!40000 ALTER TABLE `open_questions_ocr` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_ocr` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_other`
--

DROP TABLE IF EXISTS `open_questions_other`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_other` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_other_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_other_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_other`
--

LOCK TABLES `open_questions_other` WRITE;
/*!40000 ALTER TABLE `open_questions_other` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_other` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_unverified`
--

DROP TABLE IF EXISTS `open_questions_unverified`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_unverified` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned DEFAULT NULL,
  `email` varchar(50) NOT NULL,
  `title` varchar(350) NOT NULL,
  `description` varchar(255) NOT NULL,
  `verified_at` timestamp NULL DEFAULT NULL,
  `expire_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `open_questions_unverified_uuid_unique` (`uuid`),
  KEY `open_questions_unverified_account_id_foreign` (`account_id`),
  KEY `open_questions_unverified_company_id_foreign` (`company_id`),
  CONSTRAINT `open_questions_unverified_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `open_questions_unverified_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_unverified`
--

LOCK TABLES `open_questions_unverified` WRITE;
/*!40000 ALTER TABLE `open_questions_unverified` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_unverified` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_unverified_files`
--

DROP TABLE IF EXISTS `open_questions_unverified_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_unverified_files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `unverified_question_id` bigint unsigned NOT NULL,
  `path` varchar(255) NOT NULL,
  `hash` varchar(32) NOT NULL,
  `file_size` int unsigned NOT NULL,
  `extension` varchar(10) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_unverified_files_unverified_question_id_foreign` (`unverified_question_id`),
  KEY `open_questions_unverified_files_deleted_by_foreign` (`deleted_by`),
  CONSTRAINT `open_questions_unverified_files_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `open_questions_unverified_files_unverified_question_id_foreign` FOREIGN KEY (`unverified_question_id`) REFERENCES `open_questions_unverified` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_unverified_files`
--

LOCK TABLES `open_questions_unverified_files` WRITE;
/*!40000 ALTER TABLE `open_questions_unverified_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_unverified_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_wages`
--

DROP TABLE IF EXISTS `open_questions_wages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_wages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_wages_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_wages_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_wages`
--

LOCK TABLES `open_questions_wages` WRITE;
/*!40000 ALTER TABLE `open_questions_wages` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_wages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `open_questions_yearwork`
--

DROP TABLE IF EXISTS `open_questions_yearwork`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `open_questions_yearwork` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `open_question_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `open_questions_yearwork_open_question_id_foreign` (`open_question_id`),
  CONSTRAINT `open_questions_yearwork_open_question_id_foreign` FOREIGN KEY (`open_question_id`) REFERENCES `open_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `open_questions_yearwork`
--

LOCK TABLES `open_questions_yearwork` WRITE;
/*!40000 ALTER TABLE `open_questions_yearwork` DISABLE KEYS */;
/*!40000 ALTER TABLE `open_questions_yearwork` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pdf_backgrounds`
--

DROP TABLE IF EXISTS `pdf_backgrounds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pdf_backgrounds` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `extension` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `pdf_backgrounds_account_id_foreign` (`account_id`),
  CONSTRAINT `pdf_backgrounds_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pdf_backgrounds`
--

LOCK TABLES `pdf_backgrounds` WRITE;
/*!40000 ALTER TABLE `pdf_backgrounds` DISABLE KEYS */;
/*!40000 ALTER TABLE `pdf_backgrounds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `abilities` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `personal_access_tokens`
--

LOCK TABLES `personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `personal_access_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qualified_signing_users`
--

DROP TABLE IF EXISTS `qualified_signing_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qualified_signing_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `created_at` timestamp NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `qualified_signing_users_account_id_foreign` (`account_id`),
  KEY `qualified_signing_users_account_service_id_foreign` (`account_service_id`),
  KEY `qualified_signing_users_user_id_foreign` (`user_id`),
  CONSTRAINT `qualified_signing_users_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `qualified_signing_users_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `qualified_signing_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qualified_signing_users`
--

LOCK TABLES `qualified_signing_users` WRITE;
/*!40000 ALTER TABLE `qualified_signing_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `qualified_signing_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ribbon_notification_emails`
--

DROP TABLE IF EXISTS `ribbon_notification_emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ribbon_notification_emails` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ribbon_notification_id` bigint unsigned NOT NULL,
  `type` varchar(80) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ribbon_notification_emails_ribbon_notification_id_foreign` (`ribbon_notification_id`),
  CONSTRAINT `ribbon_notification_emails_ribbon_notification_id_foreign` FOREIGN KEY (`ribbon_notification_id`) REFERENCES `ribbon_notifications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ribbon_notification_emails`
--

LOCK TABLES `ribbon_notification_emails` WRITE;
/*!40000 ALTER TABLE `ribbon_notification_emails` DISABLE KEYS */;
/*!40000 ALTER TABLE `ribbon_notification_emails` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ribbon_notification_recipients`
--

DROP TABLE IF EXISTS `ribbon_notification_recipients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ribbon_notification_recipients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ribbon_notification_id` bigint unsigned NOT NULL,
  `recipient` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ribbon_notification_recipients_recipient_foreign` (`recipient`),
  KEY `ribbon_notification_recipients_ribbon_notification_id_foreign` (`ribbon_notification_id`),
  CONSTRAINT `ribbon_notification_recipients_recipient_foreign` FOREIGN KEY (`recipient`) REFERENCES `roles` (`name`),
  CONSTRAINT `ribbon_notification_recipients_ribbon_notification_id_foreign` FOREIGN KEY (`ribbon_notification_id`) REFERENCES `ribbon_notifications` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ribbon_notification_recipients`
--

LOCK TABLES `ribbon_notification_recipients` WRITE;
/*!40000 ALTER TABLE `ribbon_notification_recipients` DISABLE KEYS */;
/*!40000 ALTER TABLE `ribbon_notification_recipients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ribbon_notifications`
--

DROP TABLE IF EXISTS `ribbon_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ribbon_notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `message_nl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` enum('web','email') NOT NULL DEFAULT 'web',
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `severity` enum('info','warning','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ribbon_notifications_created_by_foreign` (`created_by`),
  KEY `ribbon_notifications_updated_by_foreign` (`updated_by`),
  CONSTRAINT `ribbon_notifications_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `ribbon_notifications_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ribbon_notifications`
--

LOCK TABLES `ribbon_notifications` WRITE;
/*!40000 ALTER TABLE `ribbon_notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `ribbon_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_user`
--

DROP TABLE IF EXISTS `role_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_user` (
  `role_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`,`user_id`),
  KEY `role_user_user_id_foreign` (`user_id`),
  CONSTRAINT `role_user_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_user`
--

LOCK TABLES `role_user` WRITE;
/*!40000 ALTER TABLE `role_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `role_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_unique` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'company-manager','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(2,'environment-manager','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(3,'group-manager','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(4,'colleague','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(5,'client-user','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(6,'one-time-signer','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(7,'one-time-question-responder','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL),(8,'secure-share-recipient','2024-03-21 08:41:29',NULL,'2024-03-21 08:41:29',NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sbr_recipients`
--

DROP TABLE IF EXISTS `sbr_recipients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sbr_recipients` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `identifier` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `identifier_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `logo` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sbr_recipients_identifier_identifier_type_category_unique` (`identifier`,`identifier_type`,`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sbr_recipients`
--

LOCK TABLES `sbr_recipients` WRITE;
/*!40000 ALTER TABLE `sbr_recipients` DISABLE KEYS */;
/*!40000 ALTER TABLE `sbr_recipients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sbr_requests`
--

DROP TABLE IF EXISTS `sbr_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sbr_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `service_task_id` int unsigned NOT NULL,
  `recipient_id` int unsigned DEFAULT NULL,
  `external_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `last_supplied_date` timestamp NULL DEFAULT NULL,
  `queued_date` timestamp NULL DEFAULT NULL,
  `tries` int unsigned NOT NULL DEFAULT '0',
  `round` int unsigned DEFAULT NULL,
  `check_after_date` timestamp NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'new',
  `last_checked_date` timestamp NULL DEFAULT NULL,
  `response_code` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `response_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `error_description` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `response_timestamp` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sbr_requests_account_id_foreign` (`account_id`),
  KEY `sbr_requests_service_task_id_foreign` (`service_task_id`),
  KEY `sbr_requests_recipient_id_foreign` (`recipient_id`),
  CONSTRAINT `sbr_requests_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sbr_requests_recipient_id_foreign` FOREIGN KEY (`recipient_id`) REFERENCES `sbr_recipients` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sbr_requests_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sbr_requests`
--

LOCK TABLES `sbr_requests` WRITE;
/*!40000 ALTER TABLE `sbr_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `sbr_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_task_associations`
--

DROP TABLE IF EXISTS `service_task_associations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_task_associations` (
  `uuid` char(36) NOT NULL,
  `service_task_id` int unsigned NOT NULL,
  `order` int unsigned NOT NULL,
  `reference` varchar(255) NOT NULL,
  `sent` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int unsigned NOT NULL,
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `service_task_associations_service_task_id_unique` (`service_task_id`),
  KEY `service_task_associations_created_by_foreign` (`created_by`),
  CONSTRAINT `service_task_associations_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_task_associations_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_task_associations`
--

LOCK TABLES `service_task_associations` WRITE;
/*!40000 ALTER TABLE `service_task_associations` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_task_associations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_task_groups`
--

DROP TABLE IF EXISTS `service_task_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_task_groups` (
  `id` int DEFAULT NULL,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `reference_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `service_task_groups_account_service_id_reference_name_unique` (`account_service_id`,`reference_name`),
  KEY `service_task_groups_account_id_foreign` (`account_id`),
  CONSTRAINT `service_task_groups_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `service_task_groups_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_task_groups`
--

LOCK TABLES `service_task_groups` WRITE;
/*!40000 ALTER TABLE `service_task_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_task_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_task_responses`
--

DROP TABLE IF EXISTS `service_task_responses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_task_responses` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `task_id` int unsigned NOT NULL,
  `round` int unsigned NOT NULL DEFAULT '1',
  `token` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `permission` enum('approve','inform','sign') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `status` enum('approved','declined','revoked','signed') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `message` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `user_agent` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `responded_at` timestamp NULL DEFAULT NULL,
  `reminded_at` timestamp NULL DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  `status_updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_task_responses_token_unique` (`token`),
  UNIQUE KEY `task_id_user_id_round_unique` (`task_id`,`user_id`,`round`),
  KEY `service_task_responses_account_id_foreign` (`account_id`),
  KEY `service_task_responses_user_id_foreign` (`user_id`),
  KEY `service_task_responses_status_updated_by_foreign` (`status_updated_by`),
  CONSTRAINT `service_task_responses_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_task_responses_status_updated_by_foreign` FOREIGN KEY (`status_updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `service_task_responses_task_id_foreign` FOREIGN KEY (`task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_task_responses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_task_responses`
--

LOCK TABLES `service_task_responses` WRITE;
/*!40000 ALTER TABLE `service_task_responses` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_task_responses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_task_signing_requests`
--

DROP TABLE IF EXISTS `service_task_signing_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_task_signing_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `service_task_id` int unsigned NOT NULL,
  `service_id` int unsigned DEFAULT NULL,
  `signed` tinyint(1) NOT NULL DEFAULT '0',
  `signed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_task_signing_requests_user_id_service_task_id_unique` (`user_id`,`service_task_id`),
  KEY `service_task_signing_requests_service_task_id_foreign` (`service_task_id`),
  KEY `service_task_signing_requests_service_id_foreign` (`service_id`),
  CONSTRAINT `service_task_signing_requests_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_task_signing_requests_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_task_signing_requests`
--

LOCK TABLES `service_task_signing_requests` WRITE;
/*!40000 ALTER TABLE `service_task_signing_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_task_signing_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_task_user_permissions`
--

DROP TABLE IF EXISTS `service_task_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_task_user_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `service_task_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `permission` enum('inform','approve') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_task_user_permissions_service_task_id_user_id_unique` (`service_task_id`,`user_id`),
  KEY `service_task_user_permissions_service_task_id_index` (`service_task_id`),
  KEY `service_task_user_permissions_user_id_foreign` (`user_id`),
  CONSTRAINT `service_task_user_permissions_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_task_user_permissions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_task_user_permissions`
--

LOCK TABLES `service_task_user_permissions` WRITE;
/*!40000 ALTER TABLE `service_task_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_task_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_tasks`
--

DROP TABLE IF EXISTS `service_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_tasks` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `account_service_company_id` int unsigned DEFAULT NULL,
  `service_task_group_id` int unsigned DEFAULT NULL,
  `account_service_upload_type_id` int unsigned DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `summary_hash` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `taxonomy_url` varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `external_id` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `refresh_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `round` int DEFAULT '1',
  `signing_round` tinyint unsigned DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `subtitle` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `email_note` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `year` mediumint NOT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `last_retrieval` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `sent_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `group_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_tasks_external_id_unique` (`external_id`),
  KEY `service_tasks_account_service_id_foreign` (`account_service_id`),
  KEY `service_tasks_company_id_foreign` (`company_id`),
  KEY `account_id_foreign` (`account_id`),
  KEY `service_tasks_account_service_company_id_foreign` (`account_service_company_id`),
  KEY `service_tasks_date_start_date_end_index` (`date_start`,`date_end`),
  KEY `service_tasks_type_index` (`type`),
  KEY `service_tasks_service_task_group_id_foreign` (`service_task_group_id`),
  KEY `service_tasks_status_foreign` (`status`),
  KEY `service_tasks_created_at_index` (`created_at`),
  KEY `service_tasks_year_index` (`year`),
  KEY `group_uuid_foreign` (`group_uuid`),
  KEY `service_tasks_account_service_upload_type_id_foreign` (`account_service_upload_type_id`),
  CONSTRAINT `account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_tasks_account_service_company_id_foreign` FOREIGN KEY (`account_service_company_id`) REFERENCES `account_service_companies` (`id`) ON DELETE SET NULL,
  CONSTRAINT `service_tasks_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `service_tasks_account_service_upload_type_id_foreign` FOREIGN KEY (`account_service_upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE SET NULL,
  CONSTRAINT `service_tasks_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `service_tasks_status_foreign` FOREIGN KEY (`status`) REFERENCES `service_tasks_status` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_tasks`
--

LOCK TABLES `service_tasks` WRITE;
/*!40000 ALTER TABLE `service_tasks` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_tasks_pkis_dossiers`
--

DROP TABLE IF EXISTS `service_tasks_pkis_dossiers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_tasks_pkis_dossiers` (
  `pkis_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `service_task_id` int unsigned NOT NULL,
  `round` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`pkis_id`),
  UNIQUE KEY `service_tasks_pkis_dossiers_round_service_task_id_unique` (`round`,`service_task_id`),
  KEY `service_tasks_pkis_dossiers_service_task_id_foreign` (`service_task_id`),
  CONSTRAINT `service_tasks_pkis_dossiers_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_tasks_pkis_dossiers`
--

LOCK TABLES `service_tasks_pkis_dossiers` WRITE;
/*!40000 ALTER TABLE `service_tasks_pkis_dossiers` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_tasks_pkis_dossiers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_tasks_pkis_dossiers_status`
--

DROP TABLE IF EXISTS `service_tasks_pkis_dossiers_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_tasks_pkis_dossiers_status` (
  `pkis_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `decline_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL,
  KEY `service_tasks_pkis_dossiers_status_pkis_id_foreign` (`pkis_id`),
  CONSTRAINT `service_tasks_pkis_dossiers_status_pkis_id_foreign` FOREIGN KEY (`pkis_id`) REFERENCES `service_tasks_pkis_dossiers` (`pkis_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_tasks_pkis_dossiers_status`
--

LOCK TABLES `service_tasks_pkis_dossiers_status` WRITE;
/*!40000 ALTER TABLE `service_tasks_pkis_dossiers_status` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_tasks_pkis_dossiers_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_tasks_signing_users`
--

DROP TABLE IF EXISTS `service_tasks_signing_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_tasks_signing_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned DEFAULT NULL,
  `service_task_id` int unsigned NOT NULL,
  `order` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_tasks_signing_users_service_task_id_order_unique` (`service_task_id`,`order`),
  UNIQUE KEY `service_tasks_signing_users_user_id_service_task_id_unique` (`user_id`,`service_task_id`),
  CONSTRAINT `service_tasks_signing_users_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`),
  CONSTRAINT `service_tasks_signing_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_tasks_signing_users`
--

LOCK TABLES `service_tasks_signing_users` WRITE;
/*!40000 ALTER TABLE `service_tasks_signing_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_tasks_signing_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_tasks_status`
--

DROP TABLE IF EXISTS `service_tasks_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_tasks_status` (
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  UNIQUE KEY `service_tasks_status_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_tasks_status`
--

LOCK TABLES `service_tasks_status` WRITE;
/*!40000 ALTER TABLE `service_tasks_status` DISABLE KEYS */;
INSERT INTO `service_tasks_status` VALUES ('completed'),('declined'),('dms_waiting'),('error_belastingdienst'),('error_kvk'),('error_requesting_party'),('error_signing'),('expired'),('forecast'),('hash_mismatch'),('open'),('pending_belastingdienst'),('pending_kvk'),('pending_requesting_party'),('pending_to_sign'),('ready_afas'),('ready_belastingdienst'),('ready_kvk'),('ready_requesting_party'),('sent');
/*!40000 ALTER TABLE `service_tasks_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `services`
--

DROP TABLE IF EXISTS `services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `services` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `generic_service_id` int unsigned NOT NULL,
  `reference_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '0',
  `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `allow_multiple` tinyint(1) NOT NULL DEFAULT '0',
  `license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `about` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `services_generic_service_id_foreign` (`generic_service_id`),
  CONSTRAINT `services_generic_service_id_foreign` FOREIGN KEY (`generic_service_id`) REFERENCES `generic_services` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `services`
--

LOCK TABLES `services` WRITE;
/*!40000 ALTER TABLE `services` DISABLE KEYS */;
INSERT INTO `services` VALUES (1,1,'twinfield_service_provider','Twinfield','twinfield','/images/services/twinfield.png','#017BC4',1,'{\"component\":\"Twinfield\",\"task_types\":[\"vat_approval\",\"ict_approval\"]}',1,NULL,'service.service_provider.twinfield_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(2,1,'exact_service_provider','Exact','exact','/images/services/exact.svg','#34353C',1,'{\"component\":\"Exact\",\"openid_authorized_route\":\"exact_online.authorized\",\"task_types\":[\"vat_approval\",\"ict_approval\",\"wage_tax_approval\"]}',1,NULL,'service.service_provider.exact_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(3,1,'fiscaal_gemak_service_provider','Fiscaal Gemak','fiscaal_gemak','/images/services/fiscaalgemak.png','#9adcfe',1,'{\"component\":\"FiscaalGemak\",\"task_types\":[\"vat_approval\",\"ict_approval\",\"ihz_approval\",\"vpb_approval\"]}',1,NULL,'service.service_provider.fiscaal_gemak_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(4,1,'nmbrs_service_provider','Visma Nmbrs','nmbrs','/images/services/nmbrs.png','#3095D8',1,'{\"component\":\"Nmbrs\",\"task_types\":[\"wage_tax_approval\"]}',1,NULL,'service.service_provider.nmbrs_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(5,1,'snelstart_service_provider','Snelstart','snelstart_service_provider','/images/services/snelstart.png','#FFFFFF',1,'{\"component\":\"SnelStart\",\"task_types\":[\"vat_approval\",\"ict_approval\"]}',1,NULL,'service.service_provider.snelstart_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(6,1,'yuki_service_provider','Visma Yuki','yuki','/images/services/visma_yuki.png','#34353C',0,'{\"component\":\"Yuki\",\"task_types\":[\"vat_approval\"]}',1,NULL,'service.service_provider.yuki_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(7,1,'continuous_auditor','Continuous Auditor','continuous_auditor','/images/services/continuous_auditor.png','#34353C',1,NULL,0,NULL,NULL,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(8,5,'afas_openid_connect','AFAS','afas_openid_connect','/images/services/afas.png','#99b4dc',1,'{\"component\":\"AfasOpenID\"}',0,NULL,'service.openid_connect.afas_openid_connect.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(9,5,'twinfield_openid_connect','Twinfield','twinfield_openid_connect','/images/services/twinfield_square.png','#A4CD58',1,'{\"component\":\"TwinfieldOpenID\"}',0,'twinfield_openid','service.openid_connect.twinfield_openid_connect.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(10,5,'intellifin_openid_connect','Intellifin','intellifin_openid_connect','/images/services/intellifin.png','#165E6B',1,'{\"component\":\"OpenIDConnect\",\"environment\":false}',0,NULL,'service.openid_connect.intellifin_openid_connect.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(11,5,'noab_forum_openid_connect','NOAB OpenID connect','noab_forum_openid_connect','/images/services/noab.png','#165E6B',1,'{\"component\":\"OpenIDConnect\",\"environment\":false}',0,'custom_noab','service.openid_connect.noab_forum_openid_connect.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(12,5,'generic_openid_connect','OpenID connect','generic_openid_connect','/images/services/openid_connect.png','#165E6B',1,'{\"component\":\"OpenIDConnect\",\"environment\":false}',1,NULL,'service.openid_connect.generic_openid_connect.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(13,2,'manual_tasks','Handmatige taken','manual_tasks','/images/services/manual_task.png','#DDDD00',1,'{\"component\":\"ManualTasks\",\"task_types\":[\"vat_approval\",\"ict_approval\",\"ihz_approval\",\"vpb_approval\",\"document_approval\",\"wage_tax_approval\",\"dividend_tax\"]}',0,NULL,'service.manual_tasks.manual_tasks.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(14,2,'afas_tasks','AFAS','afas_tasks','/images/services/afas.png','#3d5ac1',1,'{\"component\":\"AfasTasks\",\"task_types\":[\"ihz_approval\",\"vpb_approval\"]}',1,NULL,'service.manual_tasks.afas_tasks.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(15,2,'nextens_service_provider','Nextens','nextens','/images/services/nextens.png','#880000',1,'{\"component\":\"Nextens\",\"task_types\":[\"vat_approval\",\"ict_approval\",\"ihz_approval\",\"vpb_approval\"]}',0,NULL,'service.manual_tasks.nextens_service_provider.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(16,2,'loket_tasks','Loket.nl','loket_tasks','/images/services/loket.png','#DDDD00',1,'{\"component\":\"Loket\",\"task_types\":[\"wage_tax_approval\"]}',0,NULL,'service.manual_tasks.loket_tasks.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(17,4,'visionplanner_tasks','Visionplanner','visionplanner_tasks','/images/services/visionplanner.png','#DDDD00',1,'{\"component\":\"Visionplanner\",\"task_types\":[\"jaarwerk_approval\"]}',0,NULL,'service.documents_publication.visionplanner_tasks.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(18,4,'caseware_tasks','CaseWare','caseware_tasks','/images/services/caseware.png','#DDDD00',1,'{\"component\":\"Caseware\",\"task_types\":[\"jaarwerk_approval\"]}',0,NULL,'service.documents_publication.caseware_tasks.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(19,4,'silverfin_tasks','Silverfin','silverfin_tasks','/images/services/silverfin.png','#DDDD00',1,'{\"component\":\"Silverfin\",\"task_types\":[\"jaarwerk_approval\"]}',0,NULL,'service.documents_publication.silverfin_tasks.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(20,4,'manual_year_work','XBRL/PDF upload','manual_year_work','/images/services/manual_year_work.png','#ff6570',1,'{\"component\":\"ManualYearWork\",\"task_types\":[\"publication_document\",\"annual_report\"]}',0,NULL,'service.documents_publication.manual_year_work.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(21,3,'pki_signing','PKIsigning','pki_signing','/images/services/pki_signing.png','#0084FF',1,'{\"component\":\"PkiSigning\"}',0,NULL,'service.signing.pki_signing.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(22,3,'valid_sign','ValidSign','valid_sign','/images/services/valid_sign.png','#0084FF',1,'{\"component\":\"ValidSign\"}',0,NULL,'service.signing.valid_sign.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(23,3,'securelogin_qualified_signing','Hix','securelogin_qualified_signing','/images/services/hix_qualified_signing.png','#0084FF',1,'{\"component\":\"SecureLoginQualifiedSigning\"}',0,NULL,'service.signing.securelogin_qualified_signing.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(24,6,'audit_case','AuditCase','audit_case','/images/services/auditcase.png','#0084FF',1,'{\"component\":\"AuditCase\",\"allow_multiple_connections\":false}',0,NULL,'service.audit_case.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(25,6,'afas_dms','AFAS','afas_dms','/images/services/afas.png','#0084FF',1,'{\"component\":\"AfasDMS\"}',0,NULL,'service.afas_dms.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(26,6,'microsoft_sharepoint_dms','Microsoft SharePoint','microsoft_sharepoint_dms','/images/services/microsoft_sharepoint.png','#0084FF',1,'{\"component\":\"MicrosoftSharePoint\"}',0,NULL,'service.microsoft_sharepoint_dms.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(27,6,'hyarchis_dms','Hyarchis','hyarchis_dms','/images/services/hyarchis_dms.png','#0084FF',1,'{\"component\":\"HyarchisDms\"}',0,NULL,'service.document_management_system.hyarchis_dms.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(28,6,'synergy_dms','Synergy','synergy_dms','/images/services/synergy_dms.png','#0084FF',1,'{\"component\":\"SynergyDms\"}',0,'cus_haak_baak','service.document_management_system.synergy_dms.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(29,6,'alure_on_premise_dms','Alure on premise DMS','alure_on_premise_dms','/images/services/alure_on_premise.png','#0084FF',1,'{\"component\":\"AlureOnPremiseDms\"}',0,NULL,'service.alure_on_premise_dms.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(30,6,'qwoater_dms','Qwoater DMS','qwoater_dms','/images/services/qwoater_dms.png','#0084FF',1,'{\"component\":\"QwoaterDms\",\"show_enable_switch_for_admin_only\":true}',0,NULL,'service.qwoater_dms.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(31,7,'twinfield_open_questions','Twinfield','twinfield_open_questions','/images/services/twinfield.png','#017BC4',1,'{\"component\":\"TwinfieldOpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(32,7,'exact_open_questions','Exact','exact_open_questions','/images/services/exact.svg','#017BC4',1,'{\"component\":\"ExactOpenQuestions\",\"openid_authorized_route\":\"exact_online.authorized\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(33,7,'nmbrs_open_questions','Visma Nmbrs','nmbrs_open_questions','/images/services/nmbrs.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"work_cost_scheme\",\"whk_statement\",\"wbso_statement\",\"insurance_policies\",\"other\"],\"employee\":[\"salary\",\"submitting_documents\",\"on_off_boarding\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(34,7,'loket_open_questions','Loket.nl','loket_open_questions','/images/services/loket.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"work_cost_scheme\",\"whk_statement\",\"wbso_statement\",\"insurance_policies\",\"other\"],\"employee\":[\"salary\",\"submitting_documents\",\"on_off_boarding\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(35,7,'basecone_open_questions','Basecone','basecone_open_questions','/images/services/basecone.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(36,7,'manual_questions','Manual Questions','manual_questions','/images/services/manual_questions.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\",\"work_cost_scheme\",\"whk_statement\",\"wbso_statement\",\"insurance_policies\"],\"employee\":[\"salary\",\"submitting_documents\",\"on_off_boarding\"]}}',0,NULL,'service.open_questions.manual_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(37,7,'file_upload','File Upload','file_upload','/images/services/file_upload.png','#017BC4',1,'{\"component\":\"FileUpload\"}',0,NULL,'service.open_questions.file_upload.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(38,7,'snelstart_open_questions','SnelStart Web','snelstart_open_questions','/images/services/snelstart.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(39,7,'nextens_open_questions','Nextens','nextens_open_questions','/images/services/nextens.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(40,7,'fiscaal_gemak_open_questions','Fiscaal Gemak','fiscaal_gemak_open_questions','/images/services/fiscaalgemak.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"other\"],\"declaration\":[\"declaration_vat\",\"declaration_supplementation\",\"declaration_ict\",\"declaration_ib\",\"declaration_va_ib\",\"declaration_va_vpb\",\"declaration_vpb\",\"declaration_yearwork\",\"declaration_dividend\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(41,7,'boekhoud_gemak_open_questions','Boekhoud Gemak','boekhoud_gemak_open_questions','/images/services/boekhoud_gemak.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(42,7,'xero_open_questions','Xero','xero_open_questions','/images/services/xero.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(43,7,'minox_open_questions','Minox','minox_open_questions','/images/services/minox.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(44,7,'moneybird_open_questions','Moneybird','moneybird_open_questions','/images/services/moneybird.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(45,7,'eboekhouden_open_questions','e-Boekhouden','eboekhouden_open_questions','/images/services/eboekhouden.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"missing_invoice\",\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(46,7,'bizcuit_open_questions','Bizcuit','bizcuit_open_questions','/images/services/bizcuit.png','#017BC4',1,'{\"component\":\"OpenQuestions\"}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(47,7,'mfo_open_questions','MFO','mfo_open_questions','/images/services/mfo.png','#017BC4',1,'{\"component\":\"OpenQuestions\",\"default_types\":{\"company\":[\"private_or_business\",\"other\"]}}',0,NULL,'service.open_questions.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(48,8,'afas_crm','AFAS','afas_crm','/images/services/afas.png','#99b4dc',1,'{\"component\":\"AfasCrm\"}',0,NULL,'service.customer_relationship_management.afas_crm.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(49,8,'afas_noab_crm','AFAS NOAB','afas_noab_crm','/images/services/afas.png','#99b4dc',1,'{\"component\":\"AfasCrm\"}',0,'custom_noab','service.customer_relationship_management.afas_noab_crm.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(50,8,'azure_crm','Azure User Provisioning','azure_crm','/images/services/azure.jpeg','#007fff',1,'{\"component\":\"AzureCrm\"}',0,NULL,'service.customer_relationship_management.azure_crm.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(51,8,'simplicate_crm','Simplicate','simplicate_crm','/images/services/simplicate.png','#007fff',1,'{\"component\":\"SimplicateCrm\"}',0,NULL,'service.customer_relationship_management.simplicate_crm.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(52,8,'microsoft_dynamics_crm','Microsoft Dynamics 365','microsoft_dynamics_crm','/images/services/microsoft_dynamics_crm.png','#007fff',1,'{\"component\":\"MicrosoftDynamicsCrm\"}',0,NULL,'service.customer_relationship_management.microsoft_dynamics_crm.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(53,8,'audit_case_crm','AuditCase CRM','audit_case_crm','/images/services/auditcase.png','#007fff',1,NULL,0,NULL,NULL,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(54,8,'tess_crm','TESS CRM','audit_case_crm','/images/services/tess.png','#007fff',1,NULL,0,NULL,NULL,'2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(55,8,'alure_on_premise_crm','Alure on premise','alure_on_premise_crm','/images/services/alure_on_premise.png','#99b4dc',1,'{\"component\":\"AlureOnPremise\"}',0,NULL,'service.customer_relationship_management.alure_on_premise.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(56,9,'logius_sba','Service Berichten Aanslagen','logius_sba','/images/services/sba.png','#007fff',1,'{\"component\":\"Sba\"}',0,NULL,'service.other.logius_sba.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(57,9,'via_sba_authorization','VIA & SBA Machtigingen','via_sba_authorization','/images/services/via_sba_authorization.png','#007fff',1,'{\"component\":\"ViaSbaAuthorization\"}',0,NULL,'service.other.via_sba_authorization.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(58,9,'secure_share','SecureShare','secure_share','/images/services/secure_share.png','#99b4dc',1,'{\"component\":\"SecureShare\"}',0,NULL,'service.other.secure_share.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(59,9,'whatsapp_business','WhatsApp Business','whatsapp_business','/images/services/whatsapp.png','#99b4dc',1,'{\"component\":\"WhatsApp\",\"hide_enable_switch\":true}',0,'whatsapp_business','service.other.whatsapp_business.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL),(60,9,'alure_on_premise_docs','Alure on premise Docs','alure_on_premise_docs','/images/services/alure_on_premise.png','#0084FF',1,'{\"component\":\"AlureOnPremiseDocs\"}',0,NULL,'service.alure_on_premise_docs.about','2024-03-21 08:41:28',NULL,'2024-03-21 08:41:28',NULL,NULL,NULL);
/*!40000 ALTER TABLE `services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shared_file_audit_logs`
--

DROP TABLE IF EXISTS `shared_file_audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shared_file_audit_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `shared_bundle_uuid` char(36) DEFAULT NULL,
  `shared_file_id` int unsigned DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `action` varchar(100) NOT NULL,
  `message` varchar(1000) NOT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `agent_type` varchar(255) DEFAULT NULL,
  `agent_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `shared_file_audit_logs_account_id_foreign` (`account_id`),
  KEY `shared_file_audit_logs_shared_bundle_uuid_foreign` (`shared_bundle_uuid`),
  KEY `shared_file_audit_logs_shared_file_id_foreign` (`shared_file_id`),
  KEY `shared_file_audit_logs_created_by_foreign` (`created_by`),
  CONSTRAINT `shared_file_audit_logs_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shared_file_audit_logs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `shared_file_audit_logs_shared_bundle_uuid_foreign` FOREIGN KEY (`shared_bundle_uuid`) REFERENCES `shared_file_bundles` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `shared_file_audit_logs_shared_file_id_foreign` FOREIGN KEY (`shared_file_id`) REFERENCES `shared_files` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shared_file_audit_logs`
--

LOCK TABLES `shared_file_audit_logs` WRITE;
/*!40000 ALTER TABLE `shared_file_audit_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `shared_file_audit_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shared_file_bundle_users`
--

DROP TABLE IF EXISTS `shared_file_bundle_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shared_file_bundle_users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `shared_bundle_uuid` char(36) NOT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `status` enum('opened','unopened') NOT NULL,
  PRIMARY KEY (`id`),
  KEY `shared_file_bundle_users_shared_bundle_uuid_foreign` (`shared_bundle_uuid`),
  KEY `shared_file_bundle_users_user_id_foreign` (`user_id`),
  CONSTRAINT `shared_file_bundle_users_shared_bundle_uuid_foreign` FOREIGN KEY (`shared_bundle_uuid`) REFERENCES `shared_file_bundles` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `shared_file_bundle_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shared_file_bundle_users`
--

LOCK TABLES `shared_file_bundle_users` WRITE;
/*!40000 ALTER TABLE `shared_file_bundle_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `shared_file_bundle_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shared_file_bundles`
--

DROP TABLE IF EXISTS `shared_file_bundles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shared_file_bundles` (
  `uuid` char(36) NOT NULL,
  `account_id` int unsigned NOT NULL,
  `title` varchar(255) NOT NULL,
  `expire_at` datetime NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`uuid`),
  KEY `shared_file_bundles_account_id_foreign` (`account_id`),
  KEY `shared_file_bundles_created_by_foreign` (`created_by`),
  CONSTRAINT `shared_file_bundles_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shared_file_bundles_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shared_file_bundles`
--

LOCK TABLES `shared_file_bundles` WRITE;
/*!40000 ALTER TABLE `shared_file_bundles` DISABLE KEYS */;
/*!40000 ALTER TABLE `shared_file_bundles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shared_files`
--

DROP TABLE IF EXISTS `shared_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shared_files` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `shared_bundle_uuid` char(36) NOT NULL,
  `name` varchar(100) NOT NULL,
  `path` varchar(255) NOT NULL,
  `checksum` varchar(32) NOT NULL,
  `size` int unsigned NOT NULL,
  `extension` varchar(5) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `shared_files_account_id_foreign` (`account_id`),
  KEY `shared_files_shared_bundle_uuid_foreign` (`shared_bundle_uuid`),
  CONSTRAINT `shared_files_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shared_files_shared_bundle_uuid_foreign` FOREIGN KEY (`shared_bundle_uuid`) REFERENCES `shared_file_bundles` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shared_files`
--

LOCK TABLES `shared_files` WRITE;
/*!40000 ALTER TABLE `shared_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `shared_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `short_links`
--

DROP TABLE IF EXISTS `short_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `short_links` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `token` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `redirect_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `expiration_date` datetime DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `short_links_token_unique` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `short_links`
--

LOCK TABLES `short_links` WRITE;
/*!40000 ALTER TABLE `short_links` DISABLE KEYS */;
/*!40000 ALTER TABLE `short_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sms_billing_log`
--

DROP TABLE IF EXISTS `sms_billing_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_billing_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `end` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sms_billing_log_account_id_foreign` (`account_id`),
  KEY `sms_billing_log_user_id_foreign` (`user_id`),
  CONSTRAINT `sms_billing_log_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sms_billing_log_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sms_billing_log`
--

LOCK TABLES `sms_billing_log` WRITE;
/*!40000 ALTER TABLE `sms_billing_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sms_billing_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_audit_logs`
--

DROP TABLE IF EXISTS `task_audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_audit_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  `service_task_id` int unsigned NOT NULL,
  `account_service_id` int unsigned DEFAULT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `service_task_response_id` int unsigned DEFAULT NULL,
  `round` int DEFAULT '1',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` varchar(1000) NOT NULL,
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `task_audit_logs_company_id_index` (`company_id`),
  KEY `task_audit_logs_service_task_id_index` (`service_task_id`),
  KEY `task_audit_logs_account_service_id_index` (`account_service_id`),
  KEY `task_audit_logs_user_id_index` (`user_id`),
  KEY `task_audit_logs_account_id_foreign` (`account_id`),
  KEY `task_audit_logs_action_index` (`action`),
  KEY `task_audit_logs_service_task_response_id_foreign` (`service_task_response_id`),
  CONSTRAINT `task_audit_logs_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`),
  CONSTRAINT `task_audit_logs_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  CONSTRAINT `task_audit_logs_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `task_audit_logs_service_task_response_id_foreign` FOREIGN KEY (`service_task_response_id`) REFERENCES `service_task_responses` (`id`) ON DELETE SET NULL,
  CONSTRAINT `task_audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_audit_logs`
--

LOCK TABLES `task_audit_logs` WRITE;
/*!40000 ALTER TABLE `task_audit_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_audit_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_file_background`
--

DROP TABLE IF EXISTS `task_file_background`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_file_background` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `task_file_id` int unsigned NOT NULL,
  `pdf_background_id` bigint unsigned NOT NULL,
  `page` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_file_background_task_file_id_page_unique` (`task_file_id`,`page`),
  KEY `task_file_background_pdf_background_id_foreign` (`pdf_background_id`),
  CONSTRAINT `task_file_background_pdf_background_id_foreign` FOREIGN KEY (`pdf_background_id`) REFERENCES `pdf_backgrounds` (`id`) ON DELETE CASCADE,
  CONSTRAINT `task_file_background_task_file_id_foreign` FOREIGN KEY (`task_file_id`) REFERENCES `task_files` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_file_background`
--

LOCK TABLES `task_file_background` WRITE;
/*!40000 ALTER TABLE `task_file_background` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_file_background` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_file_letter_requests`
--

DROP TABLE IF EXISTS `task_file_letter_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_file_letter_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `letter_request_id` bigint unsigned NOT NULL,
  `task_file_id` int unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `task_file_letter_requests_letter_request_id_foreign` (`letter_request_id`),
  KEY `task_file_letter_requests_task_file_id_foreign` (`task_file_id`),
  CONSTRAINT `task_file_letter_requests_letter_request_id_foreign` FOREIGN KEY (`letter_request_id`) REFERENCES `letter_requests` (`id`) ON DELETE CASCADE,
  CONSTRAINT `task_file_letter_requests_task_file_id_foreign` FOREIGN KEY (`task_file_id`) REFERENCES `task_files` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_file_letter_requests`
--

LOCK TABLES `task_file_letter_requests` WRITE;
/*!40000 ALTER TABLE `task_file_letter_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_file_letter_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_file_paperproof`
--

DROP TABLE IF EXISTS `task_file_paperproof`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_file_paperproof` (
  `uuid` varchar(30) NOT NULL,
  `task_file_id` int unsigned NOT NULL,
  `url` varchar(200) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `task_file_paperproof_task_file_id_unique` (`task_file_id`),
  CONSTRAINT `task_file_paperproof_task_file_id_foreign` FOREIGN KEY (`task_file_id`) REFERENCES `task_files` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_file_paperproof`
--

LOCK TABLES `task_file_paperproof` WRITE;
/*!40000 ALTER TABLE `task_file_paperproof` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_file_paperproof` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_file_placeholders`
--

DROP TABLE IF EXISTS `task_file_placeholders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_file_placeholders` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `task_file_id` int unsigned NOT NULL,
  `group` char(36) DEFAULT NULL,
  `page` mediumint unsigned NOT NULL DEFAULT '1',
  `left` float unsigned NOT NULL,
  `top` float unsigned NOT NULL,
  `right` float unsigned NOT NULL,
  `bottom` float unsigned NOT NULL,
  `title` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `filled_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `filled_at` timestamp NULL DEFAULT NULL,
  `filled_by` int unsigned DEFAULT NULL,
  `auto_fill` tinyint(1) NOT NULL DEFAULT '0',
  `signing_order` tinyint NOT NULL,
  `status` enum('open','filled','queued','applied') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `anchor_tag` varchar(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `task_file_placeholders_task_file_id_foreign` (`task_file_id`),
  KEY `task_file_placeholders_filled_by_foreign` (`filled_by`),
  CONSTRAINT `task_file_placeholders_filled_by_foreign` FOREIGN KEY (`filled_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `task_file_placeholders_task_file_id_foreign` FOREIGN KEY (`task_file_id`) REFERENCES `task_files` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_file_placeholders`
--

LOCK TABLES `task_file_placeholders` WRITE;
/*!40000 ALTER TABLE `task_file_placeholders` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_file_placeholders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_file_trash`
--

DROP TABLE IF EXISTS `task_file_trash`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_file_trash` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `path` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `task_file_id` int unsigned NOT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_file_trash_path_unique` (`path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_file_trash`
--

LOCK TABLES `task_file_trash` WRITE;
/*!40000 ALTER TABLE `task_file_trash` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_file_trash` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_files`
--

DROP TABLE IF EXISTS `task_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_files` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned DEFAULT NULL,
  `service_task_id` int unsigned DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `checksum` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parsed_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `display_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `account_service_upload_type_id` int unsigned DEFAULT NULL,
  `extension` enum('xbrl','pdf','xml') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `taxonomy_url` varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `identifier` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `identifier_type` enum('obnumber','fiscalnumber','bsnnumber','lhnumber','kvknumber') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `subtitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `group_reference_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `belongs_to_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `order` tinyint unsigned DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `xbrl_account_id_index` (`account_id`),
  KEY `xbrl_account_service_id_foreign` (`account_service_id`),
  KEY `xbrl_service_task_id_foreign` (`service_task_id`),
  KEY `xbrl_belongs_to_id_foreign` (`belongs_to_id`),
  KEY `task_files_account_service_upload_type_id_foreign` (`account_service_upload_type_id`),
  KEY `task_files_created_at_index` (`created_at`),
  CONSTRAINT `task_files_account_service_upload_type_id_foreign` FOREIGN KEY (`account_service_upload_type_id`) REFERENCES `account_service_upload_types` (`id`) ON DELETE SET NULL,
  CONSTRAINT `xbrl_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `xbrl_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `xbrl_belongs_to_id_foreign` FOREIGN KEY (`belongs_to_id`) REFERENCES `task_files` (`id`) ON DELETE CASCADE,
  CONSTRAINT `xbrl_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_files`
--

LOCK TABLES `task_files` WRITE;
/*!40000 ALTER TABLE `task_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `telemetry`
--

DROP TABLE IF EXISTS `telemetry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telemetry` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `generic_widget_id` int unsigned NOT NULL,
  `user_widget_id` int unsigned NOT NULL,
  `type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'extension',
  `data` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `has_error` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `telemetry_user_id_foreign` (`user_id`),
  KEY `telemetry_user_widget_id_foreign` (`user_widget_id`),
  KEY `telemetry_generic_widget_id_foreign` (`generic_widget_id`),
  KEY `has_error_index` (`has_error`),
  CONSTRAINT `telemetry_generic_widget_id_foreign` FOREIGN KEY (`generic_widget_id`) REFERENCES `generic_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `telemetry_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `telemetry_user_widget_id_foreign` FOREIGN KEY (`user_widget_id`) REFERENCES `user_widgets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `telemetry`
--

LOCK TABLES `telemetry` WRITE;
/*!40000 ALTER TABLE `telemetry` DISABLE KEYS */;
/*!40000 ALTER TABLE `telemetry` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `temp_notifications`
--

DROP TABLE IF EXISTS `temp_notifications`;
/*!50001 DROP VIEW IF EXISTS `temp_notifications`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `temp_notifications` AS SELECT 
 1 AS `id`,
 1 AS `firstname`,
 1 AS `lastname`,
 1 AS `account_id`,
 1 AS `language`,
 1 AS `manager`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `tokens`
--

DROP TABLE IF EXISTS `tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tokens` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `token` varchar(3038) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  `type` varchar(30) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL,
  `expiration_date` timestamp NULL DEFAULT NULL,
  `consumer_id` int unsigned DEFAULT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `account_id` int unsigned DEFAULT NULL,
  `account_service_id` int unsigned DEFAULT NULL,
  `user_widget_id` int unsigned DEFAULT NULL,
  `data` longtext CHARACTER SET ascii COLLATE ascii_general_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET ascii COLLATE ascii_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_token_per_account_service` (`token`,`type`,`account_service_id`),
  KEY `tokens_user_id_foreign` (`user_id`),
  KEY `tokens_consumer_id_foreign` (`consumer_id`),
  KEY `tokens_account_id_foreign` (`account_id`),
  KEY `tokens_account_service_id_foreign` (`account_service_id`),
  KEY `tokens_user_widget_id_foreign` (`user_widget_id`),
  CONSTRAINT `tokens_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tokens_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tokens_consumer_id_foreign` FOREIGN KEY (`consumer_id`) REFERENCES `consumers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tokens_user_widget_id_foreign` FOREIGN KEY (`user_widget_id`) REFERENCES `user_widgets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=ascii;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tokens`
--

LOCK TABLES `tokens` WRITE;
/*!40000 ALTER TABLE `tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `top10_users_by_return_days`
--

DROP TABLE IF EXISTS `top10_users_by_return_days`;
/*!50001 DROP VIEW IF EXISTS `top10_users_by_return_days`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `top10_users_by_return_days` AS SELECT 
 1 AS `id`,
 1 AS `firstname`,
 1 AS `lastname`,
 1 AS `account name`,
 1 AS `use_days`,
 1 AS `avg_return_days`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `tracking_pixels`
--

DROP TABLE IF EXISTS `tracking_pixels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracking_pixels` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tracking_id` varchar(56) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `email_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `email_subject` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `remote_address` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `http_user_agent` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `service_task_id` int unsigned DEFAULT NULL,
  `service_task_response_id` int unsigned DEFAULT NULL,
  `tracked_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  `smtp_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `smtp_host` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `smtp_status` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `smtp_timestamp` timestamp NULL DEFAULT NULL,
  `to` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tracking_pixels_tracking_id_unique` (`tracking_id`),
  KEY `tracking_pixels_user_id_foreign` (`user_id`),
  KEY `tracking_pixels_service_task_id_foreign` (`service_task_id`),
  KEY `tracking_pixels_service_task_response_id_foreign` (`service_task_response_id`),
  CONSTRAINT `tracking_pixels_service_task_id_foreign` FOREIGN KEY (`service_task_id`) REFERENCES `service_tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tracking_pixels_service_task_response_id_foreign` FOREIGN KEY (`service_task_response_id`) REFERENCES `service_task_responses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tracking_pixels_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tracking_pixels`
--

LOCK TABLES `tracking_pixels` WRITE;
/*!40000 ALTER TABLE `tracking_pixels` DISABLE KEYS */;
/*!40000 ALTER TABLE `tracking_pixels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `translations`
--

DROP TABLE IF EXISTS `translations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `translations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned DEFAULT NULL,
  `language` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `translations_account_id_foreign` (`account_id`),
  CONSTRAINT `translations_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `translations`
--

LOCK TABLES `translations` WRITE;
/*!40000 ALTER TABLE `translations` DISABLE KEYS */;
/*!40000 ALTER TABLE `translations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `twinfield_usage`
--

DROP TABLE IF EXISTS `twinfield_usage`;
/*!50001 DROP VIEW IF EXISTS `twinfield_usage`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `twinfield_usage` AS SELECT 
 1 AS `account_id`,
 1 AS `name`,
 1 AS `support_email`,
 1 AS `widget_id`,
 1 AS `user_widget_count`,
 1 AS `widget_type`,
 1 AS `advanced`,
 1 AS `language`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `upload_service_companies`
--

DROP TABLE IF EXISTS `upload_service_companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `upload_service_companies` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `company_id` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `upload_service_companies_account_id_foreign` (`account_id`),
  KEY `upload_service_companies_account_service_id_foreign` (`account_service_id`),
  KEY `upload_service_companies_company_id_foreign` (`company_id`),
  CONSTRAINT `upload_service_companies_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `upload_service_companies_account_service_id_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `upload_service_companies_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `upload_service_companies`
--

LOCK TABLES `upload_service_companies` WRITE;
/*!40000 ALTER TABLE `upload_service_companies` DISABLE KEYS */;
/*!40000 ALTER TABLE `upload_service_companies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `uploaded_file_company_dms_preferences`
--

DROP TABLE IF EXISTS `uploaded_file_company_dms_preferences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `uploaded_file_company_dms_preferences` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int unsigned NOT NULL,
  `account_service_id` int unsigned NOT NULL,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_at` timestamp NOT NULL,
  `created_by` int unsigned DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `updated_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `uploaded_files_dms_company_foreign` (`company_id`),
  KEY `uploaded_files_dms_account_service_foreign` (`account_service_id`),
  CONSTRAINT `uploaded_files_dms_account_service_foreign` FOREIGN KEY (`account_service_id`) REFERENCES `account_services` (`id`) ON DELETE CASCADE,
  CONSTRAINT `uploaded_files_dms_company_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `uploaded_file_company_dms_preferences`
--

LOCK TABLES `uploaded_file_company_dms_preferences` WRITE;
/*!40000 ALTER TABLE `uploaded_file_company_dms_preferences` DISABLE KEYS */;
/*!40000 ALTER TABLE `uploaded_file_company_dms_preferences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `uploaded_file_dms_entries`
--

DROP TABLE IF EXISTS `uploaded_file_dms_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `uploaded_file_dms_entries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uploaded_file_id` int unsigned NOT NULL,
  `status` enum('success','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `sent_by` int unsigned DEFAULT NULL,
  `link_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `uploaded_file_dms_entry_foreign` (`uploaded_file_id`),
  CONSTRAINT `uploaded_file_dms_entry_foreign` FOREIGN KEY (`uploaded_file_id`) REFERENCES `uploaded_files` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `uploaded_file_dms_entries`
--

LOCK TABLES `uploaded_file_dms_entries` WRITE;
/*!40000 ALTER TABLE `uploaded_file_dms_entries` DISABLE KEYS */;
/*!40000 ALTER TABLE `uploaded_file_dms_entries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `uploaded_files`
--

DROP TABLE IF EXISTS `uploaded_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `uploaded_files` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `upload_id` int unsigned NOT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hash` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `file_size` int unsigned NOT NULL,
  `extension` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `uploaded_files_upload_id_foreign` (`upload_id`),
  KEY `uploaded_files_deleted_by_foreign` (`deleted_by`),
  CONSTRAINT `uploaded_files_deleted_by_foreign` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `uploaded_files_upload_id_foreign` FOREIGN KEY (`upload_id`) REFERENCES `uploads` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `uploaded_files`
--

LOCK TABLES `uploaded_files` WRITE;
/*!40000 ALTER TABLE `uploaded_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `uploaded_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `uploads`
--

DROP TABLE IF EXISTS `uploads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `uploads` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `company_id` int unsigned DEFAULT NULL,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `note` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `verified_at` timestamp NULL DEFAULT NULL,
  `expire_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uploads_uuid_unique` (`uuid`),
  KEY `uploads_account_id_foreign` (`account_id`),
  KEY `uploads_company_id_foreign` (`company_id`),
  CONSTRAINT `uploads_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `uploads_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `uploads`
--

LOCK TABLES `uploads` WRITE;
/*!40000 ALTER TABLE `uploads` DISABLE KEYS */;
/*!40000 ALTER TABLE `uploads` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_communication_channels`
--

DROP TABLE IF EXISTS `user_communication_channels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_communication_channels` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `channel` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_communication_channels_user_id_foreign` (`user_id`),
  KEY `user_communication_channels_channel_foreign` (`channel`),
  CONSTRAINT `user_communication_channels_channel_foreign` FOREIGN KEY (`channel`) REFERENCES `communication_channels` (`name`) ON DELETE CASCADE,
  CONSTRAINT `user_communication_channels_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_communication_channels`
--

LOCK TABLES `user_communication_channels` WRITE;
/*!40000 ALTER TABLE `user_communication_channels` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_communication_channels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_communication_channels_preferences`
--

DROP TABLE IF EXISTS `user_communication_channels_preferences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_communication_channels_preferences` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `communication_channel_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_communication_channels_preferences_account_id_foreign` (`account_id`),
  KEY `user_communication_channels_preferences_user_id_foreign` (`user_id`),
  KEY `communication_channel_relation` (`communication_channel_id`),
  CONSTRAINT `communication_channel_relation` FOREIGN KEY (`communication_channel_id`) REFERENCES `account_communication_channels` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_communication_channels_preferences_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_communication_channels_preferences_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_communication_channels_preferences`
--

LOCK TABLES `user_communication_channels_preferences` WRITE;
/*!40000 ALTER TABLE `user_communication_channels_preferences` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_communication_channels_preferences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_expiration`
--

DROP TABLE IF EXISTS `user_expiration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_expiration` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `expire_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_expiration_user_id_unique` (`user_id`),
  CONSTRAINT `user_expiration_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_expiration`
--

LOCK TABLES `user_expiration` WRITE;
/*!40000 ALTER TABLE `user_expiration` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_expiration` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_reactivation_requests`
--

DROP TABLE IF EXISTS `user_reactivation_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_reactivation_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `user_id` int unsigned NOT NULL,
  `status` enum('open','approved','declined','expired') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `agent_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_reactivation_requests_account_id_foreign` (`account_id`),
  KEY `user_reactivation_requests_user_id_foreign` (`user_id`),
  CONSTRAINT `user_reactivation_requests_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_reactivation_requests_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_reactivation_requests`
--

LOCK TABLES `user_reactivation_requests` WRITE;
/*!40000 ALTER TABLE `user_reactivation_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_reactivation_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_widgets`
--

DROP TABLE IF EXISTS `user_widgets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_widgets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `parent_id` int unsigned NOT NULL,
  `generic_widget_id` int unsigned DEFAULT NULL,
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `properties` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `order` int DEFAULT NULL,
  `row` int DEFAULT NULL,
  `col` int DEFAULT NULL,
  `prio` int DEFAULT NULL,
  `size` enum('small','medium','large') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `unfolded` tinyint(1) NOT NULL DEFAULT '1',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `delete_after` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_widgets_user_id_foreign` (`user_id`),
  KEY `user_widgets_parent_id_foreign` (`parent_id`),
  KEY `user_widgets_generic_widget_id_foreign` (`generic_widget_id`),
  CONSTRAINT `user_widgets_generic_widget_id_foreign` FOREIGN KEY (`generic_widget_id`) REFERENCES `generic_widgets` (`id`),
  CONSTRAINT `user_widgets_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `context_widgets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_widgets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_widgets`
--

LOCK TABLES `user_widgets` WRITE;
/*!40000 ALTER TABLE `user_widgets` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_widgets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `auth_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `auth_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `auth_secret_expiration_date` timestamp NULL DEFAULT NULL,
  `requested_password_recovery_at` timestamp NULL DEFAULT NULL,
  `auth_method` enum('secret','totp','sms_otp') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `otp_secret` blob,
  `otp_secret_unverified` blob,
  `requested_totp_recovery_at` timestamp NULL DEFAULT NULL,
  `masterkey` blob,
  `masterkey_mac` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `masterkey_mac_salt` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `masterkey_sent` timestamp NULL DEFAULT NULL,
  `send_masterkey` tinyint(1) NOT NULL DEFAULT '0',
  `firstname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `lastname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email_modified_at` datetime DEFAULT NULL,
  `email_verified_at` date DEFAULT NULL,
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `language` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `home` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `usage_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `custom_field_values` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `interactive_login_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `activation_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `activation_expiry` datetime DEFAULT NULL,
  `reminder_count` int NOT NULL DEFAULT '0',
  `session_lifetime` int DEFAULT NULL,
  `ip_whitelist` varchar(2047) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `single_session` tinyint(1) DEFAULT NULL,
  `current_session` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `status` enum('new','active','blocked','deactivated','archived','verified') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'new',
  `level` int DEFAULT NULL,
  `manager_level` int DEFAULT NULL,
  `is_external` tinyint(1) NOT NULL DEFAULT '0',
  `external_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `billing_type_verified_at` timestamp NULL DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_login_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `previous_login` datetime DEFAULT NULL,
  `previous_login_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `use_days` int DEFAULT NULL,
  `avg_return_days` decimal(7,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `deleted_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `remember_me_masterkey` blob,
  `activated_at` timestamp NULL DEFAULT NULL,
  `front_auth_method` enum('sms_otp','totp','none') DEFAULT NULL,
  `internal_client_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_auth_id_account_id_unique` (`auth_id`,`account_id`),
  KEY `users_account_id_foreign` (`account_id`),
  KEY `users_status_index` (`status`),
  KEY `users_auth_method_index` (`auth_method`),
  KEY `users_last_login_index` (`last_login`),
  KEY `users_previous_login_index` (`previous_login`),
  KEY `users_use_days_index` (`use_days`),
  KEY `users_avg_return_days_index` (`avg_return_days`),
  KEY `users_created_at_index` (`created_at`),
  KEY `users_updated_at_index` (`updated_at`),
  KEY `users_deleted_at_index` (`deleted_at`),
  KEY `users_remember_token_index` (`remember_token`),
  KEY `users_internal_client_id_index` (`internal_client_id`),
  CONSTRAINT `users_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,1,'admin','$2y$12$l1QVJc4OMnSUhNpVS5cay.2UZ2PTh4eKK5ocj53Z0b82rANg98btG',NULL,NULL,'secret',NULL,NULL,NULL,_binary 'eyJpdiI6InVpd3Y1V1lVQ25ha0tPdnFURzZ6enc9PSIsInZhbHVlIjoiaFhpcWpSamJ6Qk5wZGhUdkJkcDJNYTJzWE9qUVdKcUJJVUFIODN4TDNDVDlFRFlpL1BqQWtCNHAzQklyMEdRRiIsIm1hYyI6IjU2NzU1NGY2MGE1YjE2OWI4YjZiNmI2OWViNzc3NDAwYjQzZWNjODdlMWI3MTlmYWFlNGI4YjQwYWJmNGNlNzkiLCJ0YWciOiIifQ==','a9ddb3e6f4733ca30e51c18802cc6a8c7948337c9a080fcc6419ed1ec9a00aa9271a3cc258482b66d273d9dc9f5adf215ecad7066fbf305a38d6950da10eff8f','uq42CKyjmXHIm7TpL2LT632MJAME59DWA7hKKmJn','2024-03-21 08:43:35',0,'Admin','SecureLogin','<EMAIL>','2016-05-17 08:55:35','2016-05-17','','nl','',NULL,NULL,'',NULL,1,NULL,NULL,0,480,'',NULL,NULL,'active',-1,-1,0,NULL,NULL,'2024-03-21 08:43:35','172.18.0.1','2018-05-31 11:10:59','127.0.0.1',4,2.00,'2016-05-17 08:55:35',NULL,'2024-03-21 07:43:35','1',NULL,NULL,NULL,NULL,'2021-11-22 12:00:27',NULL,NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `users_per_generic_widget`
--

DROP TABLE IF EXISTS `users_per_generic_widget`;
/*!50001 DROP VIEW IF EXISTS `users_per_generic_widget`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `users_per_generic_widget` AS SELECT 
 1 AS `reference_name`,
 1 AS `users_count`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `webhook_conditions`
--

DROP TABLE IF EXISTS `webhook_conditions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhook_conditions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `webhook_id` int unsigned NOT NULL,
  `account_id` int unsigned NOT NULL,
  `event` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `webhook_conditions_webhook_id_foreign` (`webhook_id`),
  KEY `webhook_conditions_account_id_foreign` (`account_id`),
  KEY `webhook_conditions_event_index` (`event`),
  CONSTRAINT `webhook_conditions_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `webhook_conditions_webhook_id_foreign` FOREIGN KEY (`webhook_id`) REFERENCES `webhooks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `webhook_conditions`
--

LOCK TABLES `webhook_conditions` WRITE;
/*!40000 ALTER TABLE `webhook_conditions` DISABLE KEYS */;
/*!40000 ALTER TABLE `webhook_conditions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `webhooks`
--

DROP TABLE IF EXISTS `webhooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhooks` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `url` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `webhooks_account_id_foreign` (`account_id`),
  KEY `webhooks_created_by_foreign` (`created_by`),
  CONSTRAINT `webhooks_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `webhooks_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `webhooks`
--

LOCK TABLES `webhooks` WRITE;
/*!40000 ALTER TABLE `webhooks` DISABLE KEYS */;
/*!40000 ALTER TABLE `webhooks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `whatsapp_templates`
--

DROP TABLE IF EXISTS `whatsapp_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `whatsapp_templates` (
  `id` bigint NOT NULL,
  `name` varchar(255) NOT NULL,
  `components` text NOT NULL,
  `language` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `whatsapp_templates`
--

LOCK TABLES `whatsapp_templates` WRITE;
/*!40000 ALTER TABLE `whatsapp_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `whatsapp_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `whatsapp_user_last_message`
--

DROP TABLE IF EXISTS `whatsapp_user_last_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `whatsapp_user_last_message` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `status` enum('unread','read') NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `whatsapp_user_last_message_user_id_foreign` (`user_id`),
  CONSTRAINT `whatsapp_user_last_message_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `whatsapp_user_last_message`
--

LOCK TABLES `whatsapp_user_last_message` WRITE;
/*!40000 ALTER TABLE `whatsapp_user_last_message` DISABLE KEYS */;
/*!40000 ALTER TABLE `whatsapp_user_last_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `widget_templates`
--

DROP TABLE IF EXISTS `widget_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `widget_templates` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `description` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `version_number` int NOT NULL,
  `version_date` date NOT NULL,
  `content_template` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `properties` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `settings` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `script_dependencies` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `style_dependencies` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `status` enum('alpha','beta','active','deprecated','blocked') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'beta',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `widget_templates`
--

LOCK TABLES `widget_templates` WRITE;
/*!40000 ALTER TABLE `widget_templates` DISABLE KEYS */;
INSERT INTO `widget_templates` VALUES (1,'access',NULL,1,'2021-12-21','widget_template.access.default',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"preview\":true,\"hidden_widget_setup_static_link\":true},\"properties[description]\":{\"type\":\"description\",\"title\":\"context_widget.field_description\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"preview\":true,\"hidden_widget_setup_static_link\":true},\"properties[image]\":{\"type\":\"image\",\"title\":\"context_widget.field_image\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"validation\":\"image\",\"preview\":true},\"auto_populate\":{\"type\":\"auto_populate\",\"title\":\"account_widget.field_auto_populate_title\",\"on_change_warning\":\"account_widget.field_auto_populate_onchange_warning\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"overwritable\":true},\"properties[allow_copy_password]\":{\"type\":\"switch\",\"default_value\":false,\"title\":\"account_widget.allow_copy_password.title\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"notification\":{\"translation\":\"account_widget.allow_copy_password.warning\",\"type\":\"warning\",\"value_equals_to\":true},\"overwritable\":false,\"account_manager_only\":true},\"properties[private_window]\":{\"type\":\"switch\",\"default_value\":false,\"title\":\"private_window\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"notification\":{\"translation\":\"account_widget.allow_private_window.warning\",\"type\":\"warning\",\"value_equals_to\":true},\"overwritable\":false}}',NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2024-03-21 08:41:18',NULL),(2,'form',NULL,1,'2015-11-03','widget_template.form.default',NULL,NULL,NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2018-05-23 09:00:54',NULL),(3,'standard',NULL,1,'2016-06-23','context_widget.cards.communication',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"preview\":true}}',NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2024-03-21 08:41:18',NULL),(4,'support',NULL,1,'2016-08-31','widget_template.form.support',NULL,NULL,NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2018-05-23 09:00:54',NULL),(5,'blank',NULL,1,'2016-09-06','widget_template.blank.default',NULL,NULL,NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2018-05-23 09:00:54',NULL),(6,'uri',NULL,1,'2020-05-27','widget_template.access.uri',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"preview\":true,\"hidden_widget_setup_static_link\":true},\"properties[description]\":{\"type\":\"description\",\"title\":\"context_widget.field_description\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"preview\":true,\"hidden_widget_setup_static_link\":true},\"properties[image]\":{\"type\":\"image\",\"title\":\"context_widget.field_image\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"validation\":\"image\",\"preview\":true},\"auto_populate\":{\"type\":\"auto_populate\",\"title\":\"account_widget.field_auto_populate_title\",\"on_change_warning\":\"account_widget.field_auto_populate_onchange_warning\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"overwritable\":true},\"allow_external_start\":{\"type\":\"allow_external_start\",\"title\":\"account_widget.field_allow_external_start_title\",\"note\":\"account_widget.field_allow_external_start_title_note\",\"switch_on_js\":\"$(\'input[name=\\\"allow_external_start\\\"]:visible\').closest(\'.input-group\').parent().closest(\'.input-group\').find(\'#allow_external_start_url\').show(); generate_start_widget_url($(\'input[name=\\\"allow_external_start\\\"]:visible\'))\",\"switch_off_js\":\"\\n                     swal({\\n                      title: \'{{static_link.turn_off_swal_title}}\',\\n                      html: \'{{static_link.turn_off_swal_message}}\',\\n                      icon: \'warning\',\\n                      confirmButtonClass: \'bgm-red\',\\n                      allowOutsideClick: false,\\n                      allowEscapeKey: false,\\n                      showCancelButton: true,\\n                      confirmButtonText: \'{{common.ok}}\',\\n                      cancelButtonText: \'{{common.cancel}}\',\\n                    }).then(function(result){\\n                      if (result.value){\\n                        $(\'input[name=\\\"allow_external_start\\\"]:visible\').closest(\'.input-group\').parent().closest(\'.input-group\').find(\'#allow_external_start_url\').hide();\\n                        generate_start_widget_url_close($(\'input[name=\\\"allow_external_start\\\"]:visible\'));\\n                      } else {\\n                        $(\'input[name=\\\"allow_external_start\\\"]:visible\').prop(\'checked\', true);\\n                      }\\n                    });\",\"data_url\":\"generateStartWidgetUrl\",\"scope\":[\"account\"],\"obligatory\":false,\"overwritable\":false,\"hidden_before_create\":true,\"license\":\"static_link\"}}',NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2024-03-21 08:41:18',NULL),(7,'access_plus',NULL,1,'2017-04-05','widget_template.access.default',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"overwritable\":true,\"preview\":true,\"hidden_widget_setup_static_link\":true},\"properties[description]\":{\"type\":\"description\",\"title\":\"context_widget.field_description\",\"scope\":[\"account\",\"context\",\"user\"],\"obligatory\":false,\"overwritable\":true,\"preview\":true,\"hidden_widget_setup_static_link\":true},\"properties[image]\":{\"type\":\"image\",\"title\":\"context_widget.field_image\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"preview\":true},\"auto_populate\":{\"type\":\"auto_populate\",\"title\":\"account_widget.field_auto_populate_title\",\"on_change_warning\":\"account_widget.field_auto_populate_onchange_warning\",\"scope\":[\"account\",\"context\"],\"obligatory\":false,\"overwritable\":true},\"allow_external_start\":{\"type\":\"allow_external_start\",\"title\":\"account_widget.field_allow_external_start_title\",\"note\":\"account_widget.field_allow_external_start_title_note\",\"switch_on_js\":\"$(\'input[name=\\\"allow_external_start\\\"]:visible\').closest(\'.input-group\').parent().closest(\'.input-group\').find(\'#allow_external_start_url\').show(); generate_start_widget_url($(\'input[name=\\\"allow_external_start\\\"]:visible\'))\",\"switch_off_js\":\"\\n                     swal({\\n                      title: \'{{static_link.turn_off_swal_title}}\',\\n                      html: \'{{static_link.turn_off_swal_message}}\',\\n                      icon: \'warning\',\\n                      confirmButtonClass: \'bgm-red\',\\n                      allowOutsideClick: false,\\n                      allowEscapeKey: false,\\n                      showCancelButton: true,\\n                      confirmButtonText: \'{{common.ok}}\',\\n                      cancelButtonText: \'{{common.cancel}}\',\\n                    }).then(function(result){\\n                      if (result.value){\\n                        $(\'input[name=\\\"allow_external_start\\\"]:visible\').closest(\'.input-group\').parent().closest(\'.input-group\').find(\'#allow_external_start_url\').hide();\\n                        generate_start_widget_url_close($(\'input[name=\\\"allow_external_start\\\"]:visible\'));\\n                      } else {\\n                        $(\'input[name=\\\"allow_external_start\\\"]:visible\').prop(\'checked\', true);\\n                      }\\n                    });\",\"data_url\":\"generateStartWidgetUrl\",\"scope\":[\"account\"],\"obligatory\":false,\"overwritable\":false,\"hidden_before_create\":true,\"license\":\"static_link\"}}',NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2024-03-21 08:41:18',NULL),(8,'currency_converter',NULL,1,'2018-01-23','widget_template.communication.currency_converter.default',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\"],\"obligatory\":false}}',NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2018-05-23 09:00:54',NULL),(9,'twitter',NULL,1,'2018-01-23','widget_template.communication.twitter.default',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\"],\"obligatory\":false}}',NULL,NULL,'beta','2018-05-23 09:00:54',NULL,'2018-05-23 09:00:54',NULL),(10,'messagebird_sms_otp',NULL,1,'2019-05-01','widget_template.communication.messagebird_sms_otp.default',NULL,'{\"properties[display_name]\":{\"type\":\"title\",\"title\":\"context_widget.field_display_name\",\"scope\":[\"account\",\"context\"],\"obligatory\":false}}',NULL,NULL,'beta','2024-03-21 08:41:19',NULL,'2024-03-21 08:41:19',NULL);
/*!40000 ALTER TABLE `widget_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zzz_view_acc_stats`
--

DROP TABLE IF EXISTS `zzz_view_acc_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `zzz_view_acc_stats` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL,
  `year` int unsigned NOT NULL,
  `month` int unsigned NOT NULL,
  `user_count` int unsigned NOT NULL,
  `activated_user_count` int unsigned NOT NULL DEFAULT '0',
  `external_user_count` int unsigned NOT NULL,
  `internal_user_count` int unsigned DEFAULT NULL,
  `activated_external_user_count` int unsigned NOT NULL DEFAULT '0',
  `active_user_count` int unsigned NOT NULL DEFAULT '0',
  `avg_return_days` float unsigned DEFAULT NULL,
  `login_count` int unsigned NOT NULL DEFAULT '0',
  `sms_count` int unsigned NOT NULL,
  `virtual_number_count` int DEFAULT NULL,
  `notification_count` int unsigned NOT NULL DEFAULT '0',
  `active_external_company_users_count` int unsigned NOT NULL DEFAULT '0',
  `companies_with_active_external_users_count` int unsigned NOT NULL DEFAULT '0',
  `companies_with_vat_service_count` int DEFAULT NULL,
  `companies_count` int unsigned NOT NULL DEFAULT '0',
  `billable_companies` int unsigned DEFAULT NULL,
  `billable_tasks` int unsigned DEFAULT NULL,
  `micro_small_yearwork` int unsigned DEFAULT NULL,
  `medium_large_yearwork` int unsigned DEFAULT NULL,
  `qualified_signing_users` int DEFAULT NULL,
  `free_external_sso_users` int unsigned DEFAULT NULL,
  `billable_external_sso_users` int unsigned DEFAULT NULL,
  `connected_company_users` int unsigned DEFAULT NULL,
  `billable_companies_open_questions` int unsigned DEFAULT NULL,
  `billable_companies_file_uploads` int DEFAULT NULL,
  `custom_domain` tinyint(1) DEFAULT NULL,
  `external_idp` tinyint(1) DEFAULT NULL,
  `dossier_files_storage_usage` bigint unsigned DEFAULT '0',
  `signed_docs` int unsigned NOT NULL DEFAULT '0',
  `secure_share_count` int unsigned NOT NULL DEFAULT '0',
  `whatsapp_users_count` int unsigned NOT NULL,
  `sbr_tasks_count` int unsigned NOT NULL DEFAULT '0',
  `sba_tasks_count` int unsigned NOT NULL DEFAULT '0',
  `logius_authorization_count` int unsigned NOT NULL DEFAULT '0',
  `postbode_usage_cost` decimal(6,2) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `zzz_view_acc_stats_account_id_year_month_unique` (`account_id`,`year`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zzz_view_acc_stats`
--

LOCK TABLES `zzz_view_acc_stats` WRITE;
/*!40000 ALTER TABLE `zzz_view_acc_stats` DISABLE KEYS */;
/*!40000 ALTER TABLE `zzz_view_acc_stats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'securelogin'
--

--
-- Dumping routines for database 'securelogin'
--
/*!50003 DROP FUNCTION IF EXISTS `column_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` FUNCTION `column_exists`(
        tname VARCHAR(64),
        cname VARCHAR(64)
      ) RETURNS tinyint(1)
    READS SQL DATA
BEGIN
          RETURN 0 < (SELECT COUNT(*)
                      FROM `INFORMATION_SCHEMA`.`COLUMNS`
                      WHERE `TABLE_SCHEMA` = SCHEMA()
                            AND `TABLE_NAME` = tname
                            AND `COLUMN_NAME` = cname);
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP FUNCTION IF EXISTS `escape_json_string` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` FUNCTION `escape_json_string`(stringToEscape text) RETURNS text CHARSET utf8mb4
    READS SQL DATA
    DETERMINISTIC
BEGIN
          RETURN CONCAT('\"',REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(stringToEscape,CHAR(92 using utf8mb4),'\\\\'),CHAR(34 using utf8mb4),'\\\"'),CHAR(13 using utf8mb4),'\\r'),CHAR(12 using utf8mb4),'\\f'),CHAR(10 using utf8mb4),'\\n'),CHAR(9 using utf8mb4),'\\t'),CHAR(8 using utf8mb4),'\\b'),'\"');
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP FUNCTION IF EXISTS `foreign_key_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` FUNCTION `foreign_key_exists`(
        tname VARCHAR(64),
        keyname VARCHAR(64)
      ) RETURNS tinyint(1)
    READS SQL DATA
BEGIN
          RETURN 0 < (SELECT COUNT(*)
                      FROM `INFORMATION_SCHEMA`.`KEY_COLUMN_USAGE`
                      WHERE `TABLE_SCHEMA` = SCHEMA()
                            AND `TABLE_NAME` = tname
                            AND `CONSTRAINT_NAME` = keyname);
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP FUNCTION IF EXISTS `key_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` FUNCTION `key_exists`(
        tname VARCHAR(64),
        keyname VARCHAR(64)
      ) RETURNS tinyint(1)
    READS SQL DATA
BEGIN
          RETURN 0 < (SELECT COUNT(*)
                      FROM `INFORMATION_SCHEMA`.`STATISTICS`
                      WHERE `TABLE_SCHEMA` = SCHEMA()
                            AND `TABLE_NAME` = tname
                            AND `INDEX_NAME` = keyname);
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `add_key_if_not_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `add_key_if_not_exists`(
        tname VARCHAR(64),
        keyname VARCHAR(64),
        keycolumns VARCHAR(255),
        is_unique BOOLEAN
      )
BEGIN
          IF NOT key_exists(tname, keyname)
          THEN
            SET @add_key_if_not_exists = CONCAT('ALTER TABLE `', tname, '` ADD ',IF(is_unique, 'UNIQUE', ''),' KEY `', keyname, '` (`',keycolumns, '`);');
            PREPARE add_query FROM @add_key_if_not_exists;
            EXECUTE add_query;
          END IF;
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `drop_column_if_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `drop_column_if_exists`(
        tname VARCHAR(64),
        cname VARCHAR(64)
      )
BEGIN
          IF column_exists(tname, cname)
          THEN
            SET @drop_column_if_exists = CONCAT('ALTER TABLE `', tname, '` DROP COLUMN `', cname, '`');
            PREPARE drop_query FROM @drop_column_if_exists;
            EXECUTE drop_query;
          END IF;
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `drop_foreign_key_if_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `drop_foreign_key_if_exists`(
          tname VARCHAR(64),
          keyname VARCHAR(64)
        )
BEGIN
            IF foreign_key_exists(tname, keyname)
            THEN
              SET @drop_foreign_key_if_exists = CONCAT('ALTER TABLE `', tname, '` DROP FOREIGN KEY `', keyname, '`');
              PREPARE drop_query FROM @drop_foreign_key_if_exists;
              EXECUTE drop_query;
            END IF;
          END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `drop_key_if_exists` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb3 */ ;
/*!50003 SET character_set_results = utf8mb3 */ ;
/*!50003 SET collation_connection  = utf8mb3_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`%` PROCEDURE `drop_key_if_exists`(
        tname VARCHAR(64),
        keyname VARCHAR(64)
      )
BEGIN
          IF key_exists(tname, keyname)
          THEN
            SET @drop_key_if_exists = CONCAT('ALTER TABLE `', tname, '` DROP INDEX `', keyname, '`');
            PREPARE drop_query FROM @drop_key_if_exists;
            EXECUTE drop_query;
          END IF;
        END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Current Database: `securelogin`
--

USE `securelogin`;

--
-- Final view structure for view `acc_managers`
--

/*!50001 DROP VIEW IF EXISTS `acc_managers`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `acc_managers` AS select 1 AS `user_id`,1 AS `firstname`,1 AS `lastname`,1 AS `email`,1 AS `auth_method`,1 AS `created_at`,1 AS `last_login`,1 AS `account_id`,1 AS `account_name`,1 AS `account_type` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `acc_stats_history`
--

/*!50001 DROP VIEW IF EXISTS `acc_stats_history`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `acc_stats_history` AS select 1 AS `id`,1 AS `name`,1 AS `status`,1 AS `created_at`,1 AS `user_count m-9`,1 AS `active_user_count m-9`,1 AS `login_count m-9`,1 AS `user_count m-8`,1 AS `active_user_count m-8`,1 AS `login_count m-8`,1 AS `user_count m-7`,1 AS `active_user_count m-7`,1 AS `login_count m-7`,1 AS `user_count m-6`,1 AS `active_user_count m-6`,1 AS `login_count m-6`,1 AS `user_count m-5`,1 AS `active_user_count m-5`,1 AS `login_count m-5`,1 AS `user_count m-4`,1 AS `active_user_count m-4`,1 AS `login_count m-4`,1 AS `user_count m-3`,1 AS `active_user_count m-3`,1 AS `login_count m-3`,1 AS `user_count m-2`,1 AS `active_user_count m-2`,1 AS `login_count m-2`,1 AS `user_count m-1`,1 AS `active_user_count m-1`,1 AS `login_count m-1`,1 AS `user_count m(cached)`,1 AS `active_user_count m(cached)`,1 AS `login_count m(cached)`,1 AS `sms_count m-1` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `acc_stats_live`
--

/*!50001 DROP VIEW IF EXISTS `acc_stats_live`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `acc_stats_live` AS select 1 AS `id`,1 AS `name`,1 AS `status`,1 AS `type`,1 AS `created_at`,1 AS `users`,1 AS `activated`,1 AS `active`,1 AS `internal`,1 AS `internal_weekly`,1 AS `internal_5_widgets`,1 AS `internal_weekly_5_widgets`,1 AS `external`,1 AS `external_activated`,1 AS `users_with_sms_otp`,1 AS `users_with_totp`,1 AS `identity_attributes`,1 AS `licenses` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `custom_link_domains`
--

/*!50001 DROP VIEW IF EXISTS `custom_link_domains`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `custom_link_domains` AS select 1 AS `domain`,1 AS `Number OF Context Widgets` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `generic_widget_per_user_widget`
--

/*!50001 DROP VIEW IF EXISTS `generic_widget_per_user_widget`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `generic_widget_per_user_widget` AS select 1 AS `user_widget_id`,1 AS `user_id`,1 AS `generic_widget_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `global_stats`
--

/*!50001 DROP VIEW IF EXISTS `global_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `global_stats` AS select 1 AS `year`,1 AS `month`,1 AS `user_count`,1 AS `active_user_count`,1 AS `login_count`,1 AS `sms_count`,1 AS `notification_count` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `logins_per_day`
--

/*!50001 DROP VIEW IF EXISTS `logins_per_day`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `logins_per_day` AS select 1 AS `date`,1 AS `login_actions`,1 AS `unique_users` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `logins_per_week`
--

/*!50001 DROP VIEW IF EXISTS `logins_per_week`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `logins_per_week` AS select 1 AS `year`,1 AS `week`,1 AS `login_actions`,1 AS `unique_users` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `temp_notifications`
--

/*!50001 DROP VIEW IF EXISTS `temp_notifications`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `temp_notifications` AS select 1 AS `id`,1 AS `firstname`,1 AS `lastname`,1 AS `account_id`,1 AS `language`,1 AS `manager` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `top10_users_by_return_days`
--

/*!50001 DROP VIEW IF EXISTS `top10_users_by_return_days`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `top10_users_by_return_days` AS select 1 AS `id`,1 AS `firstname`,1 AS `lastname`,1 AS `account name`,1 AS `use_days`,1 AS `avg_return_days` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `twinfield_usage`
--

/*!50001 DROP VIEW IF EXISTS `twinfield_usage`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `twinfield_usage` AS select 1 AS `account_id`,1 AS `name`,1 AS `support_email`,1 AS `widget_id`,1 AS `user_widget_count`,1 AS `widget_type`,1 AS `advanced`,1 AS `language` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `users_per_generic_widget`
--

/*!50001 DROP VIEW IF EXISTS `users_per_generic_widget`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `users_per_generic_widget` AS select 1 AS `reference_name`,1 AS `users_count` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Current Database: `securelogin`
--

USE `securelogin`;

--
-- Final view structure for view `acc_managers`
--

/*!50001 DROP VIEW IF EXISTS `acc_managers`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `acc_managers` AS select 1 AS `user_id`,1 AS `firstname`,1 AS `lastname`,1 AS `email`,1 AS `auth_method`,1 AS `created_at`,1 AS `last_login`,1 AS `account_id`,1 AS `account_name`,1 AS `account_type` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `acc_stats_history`
--

/*!50001 DROP VIEW IF EXISTS `acc_stats_history`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `acc_stats_history` AS select 1 AS `id`,1 AS `name`,1 AS `status`,1 AS `created_at`,1 AS `user_count m-9`,1 AS `active_user_count m-9`,1 AS `login_count m-9`,1 AS `user_count m-8`,1 AS `active_user_count m-8`,1 AS `login_count m-8`,1 AS `user_count m-7`,1 AS `active_user_count m-7`,1 AS `login_count m-7`,1 AS `user_count m-6`,1 AS `active_user_count m-6`,1 AS `login_count m-6`,1 AS `user_count m-5`,1 AS `active_user_count m-5`,1 AS `login_count m-5`,1 AS `user_count m-4`,1 AS `active_user_count m-4`,1 AS `login_count m-4`,1 AS `user_count m-3`,1 AS `active_user_count m-3`,1 AS `login_count m-3`,1 AS `user_count m-2`,1 AS `active_user_count m-2`,1 AS `login_count m-2`,1 AS `user_count m-1`,1 AS `active_user_count m-1`,1 AS `login_count m-1`,1 AS `user_count m(cached)`,1 AS `active_user_count m(cached)`,1 AS `login_count m(cached)`,1 AS `sms_count m-1` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `acc_stats_live`
--

/*!50001 DROP VIEW IF EXISTS `acc_stats_live`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `acc_stats_live` AS select 1 AS `id`,1 AS `name`,1 AS `status`,1 AS `type`,1 AS `created_at`,1 AS `users`,1 AS `activated`,1 AS `active`,1 AS `internal`,1 AS `internal_weekly`,1 AS `internal_5_widgets`,1 AS `internal_weekly_5_widgets`,1 AS `external`,1 AS `external_activated`,1 AS `users_with_sms_otp`,1 AS `users_with_totp`,1 AS `identity_attributes`,1 AS `licenses` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `custom_link_domains`
--

/*!50001 DROP VIEW IF EXISTS `custom_link_domains`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `custom_link_domains` AS select 1 AS `domain`,1 AS `Number OF Context Widgets` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `generic_widget_per_user_widget`
--

/*!50001 DROP VIEW IF EXISTS `generic_widget_per_user_widget`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `generic_widget_per_user_widget` AS select 1 AS `user_widget_id`,1 AS `user_id`,1 AS `generic_widget_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `global_stats`
--

/*!50001 DROP VIEW IF EXISTS `global_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `global_stats` AS select 1 AS `year`,1 AS `month`,1 AS `user_count`,1 AS `active_user_count`,1 AS `login_count`,1 AS `sms_count`,1 AS `notification_count` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `logins_per_day`
--

/*!50001 DROP VIEW IF EXISTS `logins_per_day`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `logins_per_day` AS select 1 AS `date`,1 AS `login_actions`,1 AS `unique_users` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `logins_per_week`
--

/*!50001 DROP VIEW IF EXISTS `logins_per_week`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `logins_per_week` AS select 1 AS `year`,1 AS `week`,1 AS `login_actions`,1 AS `unique_users` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `temp_notifications`
--

/*!50001 DROP VIEW IF EXISTS `temp_notifications`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `temp_notifications` AS select 1 AS `id`,1 AS `firstname`,1 AS `lastname`,1 AS `account_id`,1 AS `language`,1 AS `manager` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `top10_users_by_return_days`
--

/*!50001 DROP VIEW IF EXISTS `top10_users_by_return_days`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `top10_users_by_return_days` AS select 1 AS `id`,1 AS `firstname`,1 AS `lastname`,1 AS `account name`,1 AS `use_days`,1 AS `avg_return_days` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `twinfield_usage`
--

/*!50001 DROP VIEW IF EXISTS `twinfield_usage`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `twinfield_usage` AS select 1 AS `account_id`,1 AS `name`,1 AS `support_email`,1 AS `widget_id`,1 AS `user_widget_count`,1 AS `widget_type`,1 AS `advanced`,1 AS `language` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `users_per_generic_widget`
--

/*!50001 DROP VIEW IF EXISTS `users_per_generic_widget`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb3_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `users_per_generic_widget` AS select 1 AS `reference_name`,1 AS `users_count` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-03-21 13:54:04
