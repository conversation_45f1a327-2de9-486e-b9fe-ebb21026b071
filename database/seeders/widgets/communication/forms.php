<?php

$widgets[] = [
    'reference_name' => 'forms',
    'display_name' => 'Forms',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2017-07-17',
    'order' => 0,
    'template_id' => 3,
    'licenses' => 'securelogin|custom_forms',
    'properties' => [
        'vue_template' => 'Form',
        'vue3_template' => 'FormWidget',
        'forms' => [
            [
                'group' => 'drv',
                'title' => 'Aanvraag Scanstraat Lyanthe',
                'form_template' => "form.drv.forms.scanstraat_lyanthe",
            ],
        ],
    ],
    'content' => '
          <div class="listview lv-lg">
            <div class="lv-body">
              @if(!empty($properties["wizards"]))
              @foreach($properties["wizards"] as $wizard_id => $wizard)
              <div class="lv-item media">
                <div class="media-body">
                  <div class="lv-title">{{$wizard[\'title\']}}</div>
                  <div class="lv-actions actions dropdown">
                    <a class="btn btn-default btn-xs btn-icon waves-effect waves-circle waves-float" data-href="{{route(\'showWizard\',[$wizard[\'id\']])}}" onclick="showModal(\'showWizard\',this)" role="button">
                      <i class="zmdi zmdi-accounts-list"></i>
                    </a>
                  </div>
                </div>
              </div>
              @endforeach
              @endif
              @foreach($properties["forms"] as $form_id => $form)
              <div class="lv-item media">
                <div class="media-body">
                  <div class="lv-title">{{$form[\'title\']}}</div>
                  <div class="lv-actions actions dropdown">
                    <a class="btn btn-default btn-xs btn-icon waves-effect waves-circle waves-float" data-href="{{route(\'showForm\',[$widget->id, $form_id])}}" onclick="showModal(\'showForm{{$widget->id."-".$form_id}}\',this)" role="button">
                      <i class="zmdi zmdi-accounts-list"></i>
                    </a>
                  </div>
                </div>
              </div>
              @endforeach
            </div>
          </div>',

    'settings' => [
        'properties[email]' => [
            'type' => 'email',
        ],
    ],
    'view' => 'broadcast', //visible
    'heredity' => 'inheritable', //can be added to an account
];
