<?php
$widgets[] = [
    'reference_name' => 'hera_life_adviseur',
    'migrate_from' => '66d82388c876c_hera_life_adviseur',
    'display_name' => 'Hera Life adviseur',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-09-26',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.19',
        'clear_cookies' => 'mijn.heralife.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mijn.heralife.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/mijn\.heralife\.nl\/login',
                    'instructions' => [
                        ['selector' => 'input[id="content.userId"]', 'func' => 'wait_for_element',],
                        ['selector' => 'input[id="content.userId"]', 'func' => 'setvalue', 'value' => '{{$username}}',],
                        ['selector' => 'html > body > form > div > div > div > div > main > div > div > div > span > input[type="password"]', 'func' => 'wait_for_element',],
                        ['selector' => 'html > body > form > div > div > div > div > main > div > div > div > span > input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}',],
                        ['selector' => 'html > body > form > div > div > div > div > main > div > div > div > button', 'func' => 'wait_for_element',],
                        ['selector' => 'html > body > form > div > div > div > div > main > div > div > div > button', 'func' => 'click',],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];