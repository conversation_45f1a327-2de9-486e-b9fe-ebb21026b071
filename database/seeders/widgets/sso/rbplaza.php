<?php

$widgets[] = [
    'reference_name' => 'rbplaza',
    'display_name' => 'RB Plaza',
    'description' => 'Register belasting adviseurs',
    'category' => ['fiscal'],
    'status' => 'beta',
    'version_date' => '2024-12-30',
    'order' => 0,
    'licenses' => 'accounting_ext_nl',
    'uptime_monitor_url' => 'https://rb.nl/login.php',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://rb.nl/login.php',
            'pages' => [
                'loginPage' => [
                    'pattern' => '^https:\/\/mijn.rb.nl\/login',
                    'instructions' => [
                        ['selector' => 'input[id$="username"]', 'func' => 'wait_for_element'],
                        ['selector' => 'input[id$="username"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input[id$="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input[id$="password"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => '.actions afas-button', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[post][data][memberLogin]' => [
            'type' => 'username',
        ],
        'properties[post][data][memberPassword]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][memberLogin]'
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][memberPassword]'
        ],
    ],
];
