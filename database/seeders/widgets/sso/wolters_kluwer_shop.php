<?php

$widgets[] = [
    'reference_name' => 'wolters_kluwer_shop',
    'display_name' => 'Wolters Kluwer Shop',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-11-14',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://shop.wolterskluwer.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://shop.wolterskluwer.nl/umbraco/surface/loginhandler/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.wolterskluwer\.eu\/as\/authorization\.oauth2',
                    'instructions' => [
                        ['selector' => 'form input#username-field', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#username-field', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => 500],
                        ['selector' => 'form button#federated_login_button', 'func' => 'click'],
                        ['selector' => 'form input[type=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['func' => 'wait', 'value' => 500],
                        ['selector' => 'form button#login_submit', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
