<?php

$widgets[] = [
    'reference_name' => 'bnp_pariba_cardiff',
    'display_name' => 'BNP Paribas Cardiff',
    'description' => 'Finagora',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-05-04',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.finagora.nl/web/cardif/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/www\.finagora\.nl\/web\/cardif\/login',
                    'instructions' => [
                        ['selector' => 'form input#_com_liferay_login_web_portlet_LoginPortlet_login', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 200],
                        ['selector' => 'form input#_com_liferay_login_web_portlet_LoginPortlet_login', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => 200],
                        ['selector' => 'form input#_com_liferay_login_web_portlet_LoginPortlet_password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#_com_liferay_login_web_portlet_LoginPortlet_password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['func' => 'wait', 'value' => 200],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
