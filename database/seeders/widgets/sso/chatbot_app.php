<?php

$widgets[] = [
    'reference_name' => 'chatbot_app',
    'display_name' => 'Chatbot App',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-09-03',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://chat.chatbotapp.ai',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://chat.chatbotapp.ai/landing/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/chat\.chatbotapp\.ai\/landing\/login$',
                    'instructions' => [
                        ['selector' => 'button._button-email_1yqv0_86', 'func' => 'wait_for_element'],
                        ['selector' => 'button._button-email_1yqv0_86', 'func' => 'click'],
                        ['selector' => 'form input[name="email"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[name="email"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[name="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[name="password"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
