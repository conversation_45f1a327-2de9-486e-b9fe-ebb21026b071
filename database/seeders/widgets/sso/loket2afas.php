<?php

$widgets[] = [
    'reference_name' => 'loket2afas',
    'migrate_from' => 'loket2_a_f_a_s',
    'display_name' => 'Loket2AFAS',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-04-28',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.17',
        'clear_cookies' => 'loket2afas.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://loket2afas.nl/account/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/loket2afas\.nl\/account\/login',
                    'instructions' => [
                        ['selector' => 'form input#Email', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#Email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[pin1]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    0,
                    1
                ]
            ],
        ],
        'properties[pin2]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    1,
                    1
                ]
            ],
        ],
        'properties[pin3]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    2,
                    1
                ]
            ],
        ],
        'properties[pin4]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    3,
                    1
                ]
            ],
        ],
        'properties[pin5]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    4,
                    1
                ]
            ],
        ],
        'properties[pin6]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    5,
                    1
                ]
            ],
        ],
        'properties[invisiblehand][pages][2fapage]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                [
                    'pattern' => '^https:\/\/loket2afas\.nl\/Account\/VerifyCode',
                    'instructions' => [
                        ['selector' => 'input#Code_1', 'func' => 'wait_for_element'],
                        ['selector' => 'input#Code_1', 'func' => 'setvalue', 'value' => '{{$pin1}}'],
                        ['selector' => 'input#Code_2', 'func' => 'setvalue', 'value' => '{{$pin2}}'],
                        ['selector' => 'input#Code_3', 'func' => 'setvalue', 'value' => '{{$pin3}}'],
                        ['selector' => 'input#Code_4', 'func' => 'setvalue', 'value' => '{{$pin4}}'],
                        ['selector' => 'input#Code_5', 'func' => 'setvalue', 'value' => '{{$pin5}}'],
                        ['selector' => 'input#Code_6', 'func' => 'setvalue', 'value' => '{{$pin6}}'],
                        ['func' => 'wait', 'value' => '500'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                    ],
                ]
            ]
        ]
    ]
];
