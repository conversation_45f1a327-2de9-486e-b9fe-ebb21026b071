<?php

$widgets[] = [
    'reference_name' => 'it_client_portal',
    'display_name' => 'IT client portal',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-05-22',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://{{$domain}}',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://{{$domain}}/ClientPortal/Login.aspx',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/{{$domain}}\/ClientPortal\/Login\.aspx$',
                    'instructions' => [
                        ['selector' => 'form input#clientAccessUserLogin_UserName', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#clientAccessUserLogin_UserName', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form input#clientAccessUserLogin_LoginButton', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[domain]' => [
            'type' => 'domain',
            'sanitize' => 'domain',
        ],
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]'
                    ]
                ]
            ]
        ],
        'properties[invisiblehand][pages][2fapage]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]',
                ],
                [
                    'pattern' => '^https:\/\/{{$domain}}\.itclientportal\.com\/',
                    'instructions' => [
                        ['selector' => 'input#OnetimePassword', 'func' => 'wait_for_element'],
                        ['selector' => 'input#OnetimePassword', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                    ]
                ],
            ]
        ]
    ],
];
