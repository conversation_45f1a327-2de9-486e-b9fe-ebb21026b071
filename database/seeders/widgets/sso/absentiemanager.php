<?php

$widgets[] = [
    'reference_name' => 'absentiemanager',
    'display_name' => 'Absentiemanager',
    'description' => 'Online verzuimmanagement',
    'category' => ['wages'],
    'status' => 'beta',
    'version_date' => '2021-02-17',
    'order' => 110,
    'licenses' => 'accounting_nl',
    'contact_detail' => [
        'name' => '<PERSON> Stern',
        'email' => '<EMAIL>',
    ],
    'properties' => [
        'clear_cookies' => 'https://{{$domain}}',
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://login.absentiemanager.nl/{{$domain}}',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.absentiemanager\.nl\/{{$domain}}',
                    'instructions' => [
                        ['selector' => 'input[name=CMP_NAME]', 'func' => 'wait_for_element'],
                        ['selector' => 'input[name=CMP_NAME]', 'func' => 'setvalue', 'value' => '{{$company_code}}'],
                        ['selector' => 'input[name=username]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input[name=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input[name=password]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'input[name=SUBMIT]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][CMP_NAME]' => [
            'type' => 'organisation',
            'title' => 'access_widget.company_code',
            'scope' => ['account', 'context', 'user'],
        ],
        'properties[post][data][username]' => [
            'type' => 'username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
        'properties[domain]' => [
            'type' => 'text',
            'title' => 'widget.absentiemanager.domain',
            'obligatory' => false,
            'note' => 'access_widget.field_domain_optional_note',
            'scope' => ['account'],
        ],
    ],
    'onstart_operations' => [
        'properties[company_code]' => [
            'property' => 'properties[post][data][CMP_NAME]'
        ],
        'properties[username]' => [
            'property' => 'properties[post][data][username]'
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]'
        ],
        'properties[domain]' => [
            'if' => [
                'empty' => [
                    'property' => 'properties[domain]',
                ],
                'native' => 'abs'
            ],
        ],
    ],
];
