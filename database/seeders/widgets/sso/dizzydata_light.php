<?php

$widgets[] = [
    'reference_name' => 'dizzydata_light',
    'display_name' => 'DizzyData Light',
    'description' => 'Accounting software',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2019-08-20',
    'order' => 100,
    'licenses' => 'accounting_nl|accounting_be',
    'contact_detail' => [
        'name' => 'Tim Paymans',
    ],
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.2.0',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://app.dizzydata.com/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/app\.dizzydata\.com\/login/?$',
                    'instructions' => [
                        'step0' => ['selector' => 'form#login input#loginFormUsername', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form#login input#loginFormUsername', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form#login input#loginFormPassword', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form#login input#loginFormPassword', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form#login button', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
