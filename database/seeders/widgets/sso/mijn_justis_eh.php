<?php

$widgets[] = [
    'reference_name' => 'mijn_justis_eh',
    'display_name' => 'Mijn Justis - eHerkenning',
    'description' => 'Inloggen met e-Herkenning',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2019-10-10',
    'order' => 100,
    'licenses' => 'eherkenning',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.4',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mijn.justis.nl/vog/vogorganisatie/inloggen.htm',
            'pages' => [
                'include' => 'eherkenning.brokers',
                'choice_page' => [
                    'pattern' => '^https:\/\/mijn\.justis\.nl\/vog\/vogorganisatie\/inloggen\.htm',
                    'instructions' => [
                        ['selector' => 'input[name$="eherkenning_inloggen"]', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => '300'],
                        ['selector' => 'input[name$="eherkenning_inloggen"]', 'func' => 'click'],
                    ],
                ],
                'ehpage' => [
                    'pattern' => '^https:\/\/brk\.eid\.kpn\.com\/brk\/EID1BBroker',
                    'instructions' => [
                        'step0' => ['selector' => 'form select#leverancier', 'func' => 'wait_for_element'],
                        'step1' => ['func' => 'wait', 'value' => '300'],
                        'step2' => ['selector' => 'form select#leverancier', 'func' => 'setvalue', 'value' => '{{$brokerid}}'],
                        'step3' => ['selector' => 'form input[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'include' => 'eherkenning.settings',
    ],
    'onstart_operations' => [
        'include' => 'eherkenning.onstart_operations',
    ],
];
