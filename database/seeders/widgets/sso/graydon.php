<?php

$widgets[] = [
    'reference_name' => 'graydon',
    'display_name' => 'Graydon',
    'description' => 'Kredietrapportage',
    'category' => ['accountancy', 'general'],
    'status' => 'beta',
    'version_date' => '2024-01-10',
    'order' => 60,
    'licenses' => 'accounting_nl',
    'properties' => [
        'clear_cookies' => 'https://graydon-insights.com/',
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://graydon-insights.com/apps/home',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/graydon-insights\.com\/auth',
                    'instructions' => [
                        ['selector' => 'form input#username', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form input#kc-login', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][wps.portlets.userid]' => [
            'type' => 'username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][wps.portlets.userid]',
        ], 'properties[password]' => [
            'property' => 'properties[post][data][password]',
        ],
    ]
];
