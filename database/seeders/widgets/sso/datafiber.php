<?php
$widgets[] = [
    'reference_name' => 'datafiber',
    'migrate_from' => '64788f2459cff_datafiber',
    'display_name' => 'Datafiber',
    'description' => '',
    'category' => [
        0 => 'other',
    ],
    'status' => 'beta',
    'version_date' => '2023-06-02',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.7',
        'clear_cookies' => 'partner.voipgrid.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://partner.voipgrid.nl/user/login/',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\\/\\/partner\\.voipgrid\\.nl\\/user\\/login\\/',
                    'instructions' => [
                        0 => [
                            'selector' => 'input#id_auth-username',
                            'func' => 'wait_for_element',
                        ],
                        1 => [
                            'selector' => 'input#id_auth-username',
                            'func' => 'setvalue',
                            'value' => '{{$username}}',
                        ],
                        2 => [
                            'selector' => 'input#id_auth-password',
                            'func' => 'wait_for_element',
                        ],
                        3 => [
                            'selector' => 'input#id_auth-password',
                            'func' => 'setvalue',
                            'value' => '{{$password}}',
                        ],
                        4 => [
                            'selector' => 'html > body > main > div > form > fieldset > button[type="submit"]',
                            'func' => 'wait_for_element',
                        ],
                        5 => [
                            'selector' => 'html > body > main > div > form > fieldset > button[type="submit"]',
                            'func' => 'click',
                        ],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];