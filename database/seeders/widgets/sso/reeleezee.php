<?php

$widgets[] = [
    'reference_name' => 'reeleezee',
    'display_name' => 'Reeleezee',
    'description' => 'Portaal',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2025-02-19',
    'order' => 108,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.2',
        'hybrid' => false,
        'clear_cookies' => [
            'https://apps.reeleezee.nl'
        ],
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://apps.reeleezee.nl/',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/apps\.reeleezee\.nl\/ma\/mobileaccounting[a-zA-Z0-9]+\/login',
                    'instructions' => [
                        ['selector' => 'input#inputUsername', 'func' => 'wait_for_element'],
                        ['selector' => 'input#inputUsername', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => '500'],
                        ['selector' => 'button[id="loginform.login"]', 'func' => 'click'],
                        ['selector' => 'input#inputPassword', 'func' => 'wait_for_element'],
                        ['selector' => 'input#inputPassword', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#inputPassword', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['func' => 'wait', 'value' => '500'],
                        ['selector' => 'button[id="loginform.login"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'title' => 'totp_secret_optional'
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[invisiblehand][pages][2fa_page]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                [
                    'pattern' => '^https:\/\/apps\.reeleezee\.nl\/ma\/mobileaccounting[a-zA-Z0-9]+\/',
                    'instructions' => [
                        ['selector' => 'form input#rlz-vasco', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#rlz-vasco', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ]
            ]
        ]
    ]
];
