<?php

$widgets[] = [
    'reference_name' => 'liquidfiles',
    'display_name' => 'LiquidFiles',
    'description' => 'Datakluis',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-11-06',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://{{$domain}}',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://{{$domain}}',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/{{$domain}}',
                    'instructions' => [
                        ['selector' => 'form input#user_email', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#user_email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#user_password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#user_password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form input#login-button-large', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[domain]' => [
            'type' => 'domain',
            'sanitize' => 'domain',
            'scope' => ['account', 'context', 'user'],
        ],
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
