<?php

$widgets[] = [
    'reference_name' => 'portbase',
    'display_name' => 'Portbase',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-09-11',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => ['https://login.iamconnected.eu', 'https://www.iamconnected.eu'],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.iamconnected.eu/',
            'pages' => [
                'homepage' => [
                    'pattern' => '^https:\/\/www\.iamconnected\.eu\/',
                    'instructions' => [
                        ['selector' => 'button.mat-focus-indicator.mat-flat-button.mat-button-base.mat-accent.ng-star-inserted', 'func' => 'wait_for_element'],
                        ['selector' => 'button.mat-focus-indicator.mat-flat-button.mat-button-base.mat-accent.ng-star-inserted', 'func' => 'click'],
                    ],
                ],
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.iamconnected\.eu',
                    'instructions' => [
                        ['selector' => 'input#okta-signin-username', 'func' => 'wait_for_element'],
                        ['selector' => 'input#okta-signin-username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#okta-signin-password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#okta-signin-submit', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'title' => 'totp_secret_optional'
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[invisiblehand][pages][2fa_page]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                [
                    'pattern' => '^https:\/\/login\.iamconnected\.eu',
                    'instructions' => [
                        ['selector' => 'form input[name="answer"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[name="answer"]', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'form input[type="submit"]', 'func' => 'click'],
                    ],
                ]
            ]
        ]
    ]
];
