<?php

$widgets[] = [
    'reference_name' => 'cmsi',
    'display_name' => 'CMSI360',
    'description' => 'Debiteurenbeheer',
    'category' => ['accountancy', 'general'],
    'status' => 'beta',
    'version_date' => '2020-03-02',
    'order' => 120,
    'licenses' => 'accounting_nl|accounting_be',
    'properties' => [
        'client_id' => 'FvqM7ooPSSUVB2mtyHm2'
    ],
    'settings' => [
        'properties[token]' => [
            'type' => 'api_key',
            'instructions' => [
                'title1' => 'widget.cmsi.field_api_key_instruction_title',
                'step1' => 'widget.cmsi.field_api_key_instruction_step1',
                'step2' => 'widget.cmsi.field_api_key_instruction_step2',
                'step3' => 'widget.cmsi.field_api_key_instruction_step3',
                'step4' => 'widget.cmsi.field_api_key_instruction_step4',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[redirect_url]' => [
            'http_post' => [
                'https://api.ha-digi.com/authorize',
                [],
                'json',
                'build_array' => [
                    'native0' => 'client_id',
                    'property0' => 'properties[client_id]',
                    'native1' => 'token',
                    'property1' => 'properties[token]',
                ],
                'url',
                'json',
                1
            ],
        ]
    ],
    'onstart_checks' => [
        'incorrect_api_key' => [
            'not_empty' => [
                'property' => 'properties[redirect_url]',
            ],
        ],
    ],
];
