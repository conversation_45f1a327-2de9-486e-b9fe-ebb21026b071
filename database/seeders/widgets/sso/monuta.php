<?php

$widgets[] = [
    'reference_name' => 'monuta',
    'display_name' => 'Monuta',
    'description' => 'Extranet',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-08-06',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://atp.monutanet.nl/',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/atp\.monutanet\.nl',
                    'instructions' => [
                        ['func' => 'wait', 'value' => '1000'],
                        ['selector' => 'div[class="form-group mx-loginidtextbox no-columns mx-name-loginIdTextBoxGebruikersnaam"] input[type="text"]', 'func' => 'wait_for_element'],
                        ['selector' => 'div[class="form-group mx-loginidtextbox no-columns mx-name-loginIdTextBoxGebruikersnaam"] input[type="text"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'div[class="form-group mx-passwordtextbox no-columns mx-name-passwordTextBoxWachtwoord"] input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'div[class="form-group mx-passwordtextbox no-columns mx-name-passwordTextBoxWachtwoord"] input[type="password"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'button#mxui_widget_LoginButton_0', 'func' => 'click']
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
