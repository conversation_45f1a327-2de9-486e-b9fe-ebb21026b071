<?php

$widgets[] = [
    'reference_name' => 'belastingdienst_eu_btw',
    'display_name' => 'Belastingdienst EU BTW',
    'description' => 'Teruggaaf BTW EU landen',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2022-07-12',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://eubtw.belastingdienst.nl/netp/',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/eubtw\.belastingdienst\.nl\/netp\/',
                    'instructions' => [
                        ['selector' => 'form input#user_login', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#user_login', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#user_pass', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#user_pass', 'func' => 'attr', 'value' => ['type', 'hidden']],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[btw]' => [
            'obligatory' => false,
            'type' => 'text',
            'title' => 'vat_number_optional',
        ]
    ],
    'onstart_operations' => [
        'properties[invisiblehand][pages][loginpage][instructions]' => [
            'case' => [
                'empty' => [
                    'property' => 'properties[btw]'
                ],
                'array_merge' => [
                    'property' => 'properties[invisiblehand][pages][loginpage][instructions]',
                    [
                        ['selector' => 'form input[type="submit"]', 'func' => 'click'],
                    ]
                ]
            ],
            'case2' => [
                'not_empty' => [
                    'property' => 'properties[btw]'
                ],
                'array_merge' => [
                    'property' => 'properties[invisiblehand][pages][loginpage][instructions]',
                    [
                        ['selector' => 'form input#user_represented', 'func' => 'setvalue', 'value' => '{{$btw}}'],
                        ['selector' => 'form input[type="submit"]', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ]
];
