<?php

$widgets[] = [
    'reference_name' => 'unleashed',
    'display_name' => 'Unleashed',
    'description' => 'Inventory management',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2021-07-08',
    'order' => 100,
    'licenses' => 'accounting_uk',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.4',
        'clear_cookies' => 'https://go.unleashedsoftware.com',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://go.unleashedsoftware.com/v2/Account/LogOn',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/[a-zA-Z0-9]+\.unleashedsoftware\.com\/v2\/Account\/LogOn',
                    'instructions' => [
                        'step0' => ['selector' => 'form input#username', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form input#btnLogOn', 'func' => 'click'],
                    ],
                ],
                '2fapage' => [
                    'pattern' => '^https:\/\/go\.unleashedsoftware\.com\/v2\/Mfa?.*',
                    'instructions' => [
                        ['selector' => 'div#mfa form input.inputBox', 'func' => 'wait_for_element'],
                        ['selector' => 'div#mfa form input.inputBox', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'div#mfa form input[type=submit]', 'func' => 'click'],
                    ]
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.unleashed.totp_instructions.title',
                'step1' => 'widget.unleashed.totp_instructions.step1',
                'step2' => 'widget.unleashed.totp_instructions.step2',
                'step3' => 'widget.unleashed.totp_instructions.step3',
                'step4' => 'widget.unleashed.totp_instructions.step4',
                'step5' => 'widget.unleashed.totp_instructions.step5'
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]'
                    ]
                ]
            ]
        ]
    ],
];
