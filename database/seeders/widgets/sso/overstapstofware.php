<?php
$widgets[] = [
    'reference_name' => 'overstapstofware',
    'migrate_from' => '632ac0015ba98_overstapservice',
    'display_name' => 'Overstapstofware',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-04-14',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://solution.vandam.visieintoekomst.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://solution.vandam.visieintoekomst.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/solution\.vandam\.visieintoekomst\.nl\/login',
                    'instructions' => [
                        ['selector' => 'form input[type="text"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type="text"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[type="password"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];