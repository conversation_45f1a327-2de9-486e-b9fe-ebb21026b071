<?php

$widgets[] = [
    'reference_name' => 'powerall',
    'display_name' => 'Powerall',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-05-02',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.0',
        'clear_cookies' => 'https://account.powerall.app',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://account.powerall.app/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/account\.powerall\.app\/login',
                    'instructions' => [
                        ['selector' => 'input#Username', 'func' => 'wait_for_element'],
                        ['selector' => 'input#Username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                    ]
                ],
                'passwordpage' => [
                    'pattern' => 'https:\/\/account\.powerall\.app\/login-password',
                    'instructions' => [
                        ['selector' => 'input#Password', 'func' => 'wait_for_element'],
                        ['selector' => 'input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click']
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
