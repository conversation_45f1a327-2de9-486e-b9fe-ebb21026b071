<?php

$widgets[] = [
    'reference_name' => 'vip_calculus',
    'display_name' => 'VIP Calculus',
    'description' => 'Software voor huisartsenpraktijken',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-12-17',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://platform.viplive.nl/#/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/platform\.viplive\.nl',
                    'instructions' => [
                        ['selector' => 'form input[data-test-id="login-gebruikersnaam-input"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[data-test-id="login-gebruikersnaam-input"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[data-test-id="login-wachtwoord-input"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[data-test-id="login-wachtwoord-input"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[data-test-id="login-inloggen-button"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
