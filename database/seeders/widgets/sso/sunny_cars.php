<?php

$widgets[] = [
    'reference_name' => 'sunny_cars',
    'display_name' => 'Sunny Cars',
    'description' => 'Online reservation system',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2021-12-08',
    'order' => 100,
    'licenses' => 'custom_tioga_tours',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://service.sunnycars.com',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://service.sunnycars.com/b2b/Login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/service\.sunnycars\.com\/b2b\/\(S\([a-zA-Z0-9]+\)\)\/Login',
                    'instructions' => [
                        ['selector' => 'form#Form input#LoginName', 'func' => 'wait_for_element'],
                        ['value' => '500', 'func' => 'wait'],
                        ['selector' => 'form#Form input#LoginName', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form#Form input#LoginPassword', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form#Form input#LoginPassword', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form#Form span#BtnNext_label', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
