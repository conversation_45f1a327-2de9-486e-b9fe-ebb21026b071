<?php

$widgets[] = [
    'reference_name' => 'payt_debtor',
    'display_name' => 'Payt Debiteurenbeheer',
    'description' => 'Geautomatiseerde debiteurenbeheer',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2022-04-11',
    'order' => 100000,
    'licenses' => 'accounting_nl|accounting_be|accounting_uk',
    'uptime_monitor_url' => 'https://app.paytsoftware.com/',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'clear_cookies' => 'https://app.paytsoftware.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://app.paytsoftware.com/',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/app\.paytsoftware\.com',
                    'instructions' => [
                        ['selector' => 'form input[name="username"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[name="username"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[name="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[name="password"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][user[username]]' => [
            'type' => 'username',
        ],
        'properties[post][data][user[password]]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.payt.totp_instructions.title',
                'step1' => 'widget.payt.totp_instructions.step1',
                'step2' => 'widget.payt.totp_instructions.step2',
                'step3' => 'widget.payt.totp_instructions.step3',
                'step4' => 'widget.payt.totp_instructions.step4',
                'step5' => 'widget.payt.totp_instructions.step5',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][user[username]]',
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][user[password]]',
        ],
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ]
        ],
        'properties[invisiblehand][pages][loginpage][instructions]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'array_merge' => [
                    'property' => 'properties[invisiblehand][pages][loginpage][instructions]',
                    [
                        ['selector' => 'form input[name="token"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[name="token"]', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'form button[type=submit]', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ],
];
