<?php

$widgets[] = [
    'reference_name' => 'blue10',
    'display_name' => 'Blue10',
    'description' => 'Scannen & herkennen',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2021-06-01',
    'order' => 150,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://login.blue10.com/',
            'pages' => [
                [
                    'pattern' => '^https:\/\/login\.blue10\.com',
                    'instructions' => [
                        ['selector' => 'form input[id="email"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[id="email"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type="password"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form input[type="submit"]', 'func' => 'click'],
                    ]
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][login]' => [
            'type' => 'email_as_username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ]
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][login]',
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]',
        ],
    ]
];
