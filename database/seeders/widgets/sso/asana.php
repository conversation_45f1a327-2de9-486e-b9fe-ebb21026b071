<?php

$widgets[] = [
    'reference_name' => 'asana',
    'display_name' => 'Asana',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-05-28',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => [
            'https://app.asana.com',
            'https://asana.com',
            'app.asana.com',
            'asana.com'
        ],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://app.asana.com/-/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/app\.asana\.com\/-\/login$',
                    'instructions' => [
                        ['selector' => 'form input[type=email]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type=email]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form div.LoginButton', 'func' => 'click'],
                        ['value' => 2000, 'func' => 'wait'],
                        ['selector' => 'form input[type=password]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => '#rc-anchor-container', 'func' => 'click'],
                        ['value' => 2000, 'func' => 'wait'],
                        ['selector' => 'form div.LoginButton', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
