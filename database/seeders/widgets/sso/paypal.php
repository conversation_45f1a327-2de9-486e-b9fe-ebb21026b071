<?php

$widgets[] = [
    'reference_name' => 'paypal',
    'display_name' => 'Paypal',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-10-13',
    'order' => 100,
    'licenses' => 'accounting_nl|accounting_uk',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://paypal.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.paypal.com/signin',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/www\.paypal\.com\/signin',
                    'instructions' => [
                        ['selector' => '#email', 'func' => 'wait_for_element'],
                        ['selector' => '#email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => '1000'],
                        ['selector' => '#btnNext', 'func' => 'click'],
                        ['selector' => '#password', 'func' => 'wait_for_element'],
                        ['selector' => '#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => '#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['func' => 'wait', 'value' => '1000'],
                        ['selector' => '#btnLogin', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title' => 'widget.paypal.totp_instructions.title',
                'step1' => 'widget.paypal.totp_instructions.step1',
                'step2' => 'widget.paypal.totp_instructions.step2',
                'step3' => 'widget.paypal.totp_instructions.step3',
                'step4' => 'widget.paypal.totp_instructions.step4',
                'step5' => 'widget.paypal.totp_instructions.step5',
                'step6' => 'widget.paypal.totp_instructions.step6',
                'step7' => 'widget.paypal.totp_instructions.step7',
            ]
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[pin1]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    0,
                    1
                ]
            ],
        ],
        'properties[pin2]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    1,
                    1
                ]
            ],
        ],
        'properties[pin3]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    2,
                    1
                ]
            ],
        ],
        'properties[pin4]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    3,
                    1
                ]
            ],
        ],
        'properties[pin5]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    4,
                    1
                ]
            ],
        ],
        'properties[pin6]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    5,
                    1
                ]
            ],
        ],
        'properties[invisiblehand][pages][2fapage]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                [
                    'pattern' => '^https:\/\/www\.paypal\.com\/authflow\/twofactor',
                    'instructions' => [
                        ['selector' => 'input#ci-otpCode-0', 'func' => 'wait_for_element'],
                        ['selector' => 'input#ci-otpCode-0', 'func' => 'setvalue', 'value' => '{{$pin1}}'],
                        ['selector' => 'input#ci-otpCode-1', 'func' => 'setvalue', 'value' => '{{$pin2}}'],
                        ['selector' => 'input#ci-otpCode-2', 'func' => 'setvalue', 'value' => '{{$pin3}}'],
                        ['selector' => 'input#ci-otpCode-3', 'func' => 'setvalue', 'value' => '{{$pin4}}'],
                        ['selector' => 'input#ci-otpCode-4', 'func' => 'setvalue', 'value' => '{{$pin5}}'],
                        ['selector' => 'input#ci-otpCode-5', 'func' => 'setvalue', 'value' => '{{$pin6}}'],
                        ['func' => 'wait', 'value' => '500'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                    ],
                ]
            ]
        ]
    ]
];
