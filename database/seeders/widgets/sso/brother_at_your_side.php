<?php
$widgets[] = [
    'reference_name' => 'brother_at_your_side',
    'migrate_from' => '64785e840a05c_brother_at_your_side',
    'display_name' => 'Brother at your side',
    'description' => '',
    'category' => [
        0 => 'other',
    ],
    'status' => 'beta',
    'version_date' => '2023-06-02',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.7',
        'clear_cookies' => 'brotheratyourside.b2clogin.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://atyourside.brother.pt/api/sitecore/B2CLogin/SignIn',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\\/\\/brotheratyourside\\.b2clogin\\.com\\/brotheratyourside\\.onmicrosoft\\.com\\/b2c_1a_brotherstore_signuporsignin\\/oauth2\\/v2\\.0\\/authorize',
                    'instructions' => [
                        0 => [
                            'selector' => 'input#signInName',
                            'func' => 'wait_for_element',
                        ],
                        1 => [
                            'selector' => 'input#signInName',
                            'func' => 'setvalue',
                            'value' => '{{$username}}',
                        ],
                        2 => [
                            'selector' => 'input#password',
                            'func' => 'wait_for_element',
                        ],
                        3 => [
                            'selector' => 'input#password',
                            'func' => 'setvalue',
                            'value' => '{{$password}}',
                        ],
                        4 => [
                            'selector' => 'button#next',
                            'func' => 'wait_for_element',
                        ],
                        5 => [
                            'selector' => 'button#next',
                            'func' => 'click',
                        ],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];