<?php

$widgets[] = [
    'reference_name' => 'twincone',
    'migrate_from' => '65098eaeb852b_twincode',
    'display_name' => 'Twincone',
    'description' => '',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2023-10-03',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.10',
        'clear_cookies' => 'twincone.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://twincone.nl/app/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/twincone\.nl\/app\/login',
                    'instructions' => [
                        [
                            'selector' => 'input[type="email"]',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input[type="email"]',
                            'func' => 'setvalue',
                            'value' => '{{$username}}'
                        ],
                        [
                            'selector' => 'input[type="password"]',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input[type="password"]',
                            'func' => 'setvalue',
                            'value' => '{{$password}}'
                        ],
                        [
                            'selector' => 'button[type="submit"]',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'button[type="submit"]',
                            'func' => 'click'
                        ]
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
    'onstart_operations' => null,
    'onstart_checks' => null
];
