<?php

$widgets[] = [
    'reference_name' => 'afas_klantportal',
    'display_name' => 'AFAS Klantportal',
    'description' => 'Afas Klantportal',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2025-06-11',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://klant.afas.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/klant\.afas\.nl\/login.*$',
                    'instructions' => [
                        ['shadowRoot' => 'afas-text-input[id$=username]','selector' => 'input[name$=username]', 'func' => 'wait_for_element'],
                        ['shadowRoot' => 'afas-text-input[id$=username]','selector' => 'input[name$=username]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['shadowRoot' => 'afas-password-input','selector' => 'input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['value' => 500, 'func' => 'wait'],
                        ['selector' => '[data-webbutton-id=login', 'func' => 'click'],
                    ],
                ],
            ],
        ],
        'totp_instructions' => [
            'step5' => ['selector' => 'div.confirmTotp input', 'func' => 'wait_for_element'],
            'step6' => ['selector' => 'div.confirmTotp input', 'func' => 'setvalue', 'value' => '{{$totp}}'],
            'step7' => ['value' => 500, 'func' => 'wait'],
            'step8' => ['selector' => 'div.confirmTotp button', 'func' => 'click'],
        ]
    ],
    'settings' => [
        'properties[post][data][user]' => [
            'type' => 'username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.afas_klantportal.totp_instructions.title',
                'step1' => 'widget.afas_klantportal.totp_instructions.step1',
                'step2' => 'widget.afas_klantportal.totp_instructions.step2',
                'step3' => 'widget.afas_klantportal.totp_instructions.step3',
                'step4' => 'widget.afas_klantportal.totp_instructions.step4',
                'step5' => 'widget.afas_klantportal.totp_instructions.step5',
                'step6' => 'widget.afas_klantportal.totp_instructions.step6',
                'step7' => 'widget.afas_klantportal.totp_instructions.step7',
                'step8' => 'widget.afas_klantportal.totp_instructions.step8',
                'step9' => 'widget.afas_klantportal.totp_instructions.step9',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][user]',
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]',
        ],
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ]
        ],
        'properties[invisiblehand][pages][loginpage][instructions]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]',
                ],
                'array_merge' => [
                     'property' => 'properties[invisiblehand][pages][loginpage][instructions]',
                     'property1' => 'properties[totp_instructions]'
                ],
            ],
        ],
    ],
];
