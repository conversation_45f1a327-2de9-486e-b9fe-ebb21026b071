<?php

$widgets[] = [
    'reference_name' => 'asr_aov',
    'migrate_from' => '6363c8ee1efdf_a_s_r_a_o_v',
    'display_name' => 'ASR AOV',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-11-03',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.14',
        'clear_cookies' => 'login.asr.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://login.asr.nl/mijnasr/login/',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/login\.asr\.nl\/mijnasr\/login\/',
                    'instructions' => [
                        ['selector' => 'input#username', 'func' => 'wait_for_element'],
                        ['selector' => 'input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#password', 'func' => 'wait_for_element'],
                        ['selector' => 'input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'button#loginFormUsernameAndPasswordButton', 'func' => 'wait_for_element'],
                        ['selector' => 'button#loginFormUsernameAndPasswordButton', 'func' => 'click']
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
