<?php

$widgets[] = [
    'reference_name' => 'creditsafe',
    'display_name' => 'Creditsafe',
    'description' => 'Bedrijfsinformatie',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2025-04-24',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'uptime_monitor_url' => 'https://mylogin.creditsafe.com/nl-nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mylogin.creditsafe.com/nl-nl',
            'pages' => [
                'loginPage' => [
                    'pattern' => '^https:\/\/mylogin\.creditsafe\.com\/nl-nl\/?$',
                    'instructions' => [
                        ['func' => 'wait', 'value' => '500'],
                        ['selector' => 'form input#Username', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#Username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type=submit]', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[post][data][Username]' => [
            'type' => 'username',
        ],
        'properties[post][data][Password]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][Username]'
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][Password]'
        ]
    ]
];
