<?php

$widgets[] = [
    'reference_name' => 'hmrc',
    'display_name' => 'HMRC',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2021-09-28',
    'order' => 100,
    'licenses' => 'accounting_uk',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://www.gov.uk',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.tax.service.gov.uk/account',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/www\.access\.service\.gov\.uk\/login\/signin\/creds\/?$',
                    'instructions' => [
                        ['selector' => 'form#loginForm input#user_id', 'func' => 'wait_for_element'],
                        ['selector' => 'form#loginForm input#user_id', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form#loginForm input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form#loginForm input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form#loginForm', 'func' => 'submit'],
                    ],
                ],
                '2fapage' => [
                    'pattern' => '^https:\/\/www\.access\.service\.gov\.uk\/multi-factor\/challenge-totp\/ayp\/tbf\/',
                    'instructions' => [
                        ['selector' => 'form input#oneTimePassword', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#oneTimePassword', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'form button#continue', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.hmrc.totp_instructions.title',
                'step1' => 'widget.hmrc.totp_instructions.step1',
                'step2' => 'widget.hmrc.totp_instructions.step2',
                'step3' => 'widget.hmrc.totp_instructions.step3',
                'step4' => 'widget.hmrc.totp_instructions.step4',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ]
        ],
    ],
];

