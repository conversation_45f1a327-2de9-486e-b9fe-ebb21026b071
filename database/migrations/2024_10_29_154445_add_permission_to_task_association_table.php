<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_association_user_permissions', function (Blueprint $table) {
            $table->uuid()->primary();
            $table->uuid('service_task_association_uuid');
            $table->unsignedInteger('user_id')->nullable(false);
            $table->enum('permission', ['inform', 'approve', 'none'])->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent();

            $table->foreign('service_task_association_uuid', 'fk_service_task_assoc')
                ->references('uuid')
                ->on('service_task_associations')
                ->onDelete('cascade');

            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_association_user_permissions');
    }
};
