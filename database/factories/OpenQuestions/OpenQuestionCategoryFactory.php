<?php

namespace Database\Factories\OpenQuestions;

use App\Models\OpenQuestions\OpenQuestionCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class OpenQuestionCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = OpenQuestionCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
