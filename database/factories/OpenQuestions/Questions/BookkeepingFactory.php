<?php

namespace Database\Factories\OpenQuestions\Questions;

use App\Models\OpenQuestions\Questions\Bookkeeping;
use Illuminate\Database\Eloquent\Factories\Factory;

class BookkeepingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Bookkeeping::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
