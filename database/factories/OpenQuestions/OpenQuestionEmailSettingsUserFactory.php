<?php

namespace Database\Factories\OpenQuestions;

use App\Models\OpenQuestions\OpenQuestionEmailSettingsUser;
use Illuminate\Database\Eloquent\Factories\Factory;

class OpenQuestionEmailSettingsUserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = OpenQuestionEmailSettingsUser::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
