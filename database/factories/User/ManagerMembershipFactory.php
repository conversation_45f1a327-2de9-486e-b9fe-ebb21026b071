<?php

namespace Database\Factories\User;

use App\ManagerMembership;
use App\Membership;
use Illuminate\Database\Eloquent\Factories\Factory;

class ManagerMembershipFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ManagerMembership::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return Membership::factory()->make([
            Membership::TYPE => Membership::TYPE_MANAGER
        ]);
    }
}
