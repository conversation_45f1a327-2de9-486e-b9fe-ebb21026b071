<?php

namespace Database\Factories\CustomRules;

use App\Models\CustomRules\CustomRuleCondition;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomRuleConditionFactory extends Factory
{
    protected $model = CustomRuleCondition::class;

    public function definition(): array
    {
        return [
            'event' => $this->faker->word() . '.' . $this->faker->word() . '-' . $this->faker->word(),
        ];
    }
}
