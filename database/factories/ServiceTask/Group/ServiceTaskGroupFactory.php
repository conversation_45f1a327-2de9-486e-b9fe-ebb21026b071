<?php

namespace Database\Factories\ServiceTask\Group;

use App\ServiceTaskGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

class ServiceTaskGroupFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ServiceTaskGroup::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
