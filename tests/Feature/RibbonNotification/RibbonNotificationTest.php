<?php

namespace Tests\Feature\RibbonNotification;

use App\Models\RibbonNotification\RibbonNotification;
use App\Services\Account\RibbonNotification\RibbonNotificationService;
use Tests\Support\Factories\RibbonNotificationFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class RibbonNotificationTest extends TestCase
{
    public function testSaveForbidden()
    {
        $user = UserFactory::createManager();
        $this->be($user);
        $account = $user->account;

        $response = $this->post($account->route('new.system.ribbon_notification.save'));
        $response->assertForbidden();
    }

    public function testGetForUser()
    {
        $user = UserFactory::createCompanyManager();
        $this->be($user);
        $account = $user->account;
        RibbonNotificationFactory::create(RibbonNotification::TYPE_WEB);

        $response = $this->get($account->route('new.ribbon_notification.get_for_user'));
        $response->assertOk();
        $response->assertJsonStructure(
            [
                'data' => [
                    'id',
                    'message',
                    'message_no_tags',
                    'severity',
                    'cookieName'
                ]
            ]
        );
    }
}