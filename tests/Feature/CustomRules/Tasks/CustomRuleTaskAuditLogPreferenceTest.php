<?php

namespace Tests\Feature\CustomRules\Tasks;

use App\Enums\RouteName;
use App\Models\TaskFile;
use App\Services\Dossiers\DossierFileService;
use App\ServiceTask;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\CustomRule\TaskAuditLogPreferenceFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class CustomRuleTaskAuditLogPreferenceTest extends TestCase
{
    public function testViewingAuditLogAsClientUserAllowed()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;
        $values = [
            ['event' => ServiceTask::TYPE_VAT_APPROVAL,]
        ];
        TaskAuditLogPreferenceFactory::create($account, $values);

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);

        $this->be($user);

        $this->get($account->route(RouteName::TASK_FILE_SERVE_TYPE, [
            'task' => $task->id,
            'type' => TaskFile::TYPE_AUDIT_LOG,
            'filename' => 'audit-log.pdf'
        ]))->assertOk();
        ob_start();
    }

    public function testViewingAuditLogAsClientUserNotAllowed()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;
        $values = [
            ['event' => ServiceTask::TYPE_DOCUMENT_APPROVAL,]
        ];
        TaskAuditLogPreferenceFactory::create($account, $values);

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);

        $this->be($user);

        $this->get($account->route(RouteName::TASK_FILE_SERVE_TYPE, [
            'task' => $task->id,
            'type' => TaskFile::TYPE_AUDIT_LOG,
            'filename' => 'audit-log.pdf'
        ]))->assertNotFound();
    }

    public function testViewingAuditLogAsClientUserNoCustomRuleSet()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);

        $this->be($user);

        $this->get($account->route(RouteName::TASK_FILE_SERVE_TYPE, [
            'task' => $task->id,
            'type' => TaskFile::TYPE_AUDIT_LOG,
            'filename' => 'audit-log.pdf'
        ]))->assertOk();
        ob_start();
    }

        public function testCopyToDossierAllowed()
        {
            $user = UserFactory::createClientUser();
            $account = $user->account;
            $values = [
                ['event' => ServiceTask::TYPE_VAT_APPROVAL,]
            ];
            TaskAuditLogPreferenceFactory::create($account, $values);

            $accountService = AccountServiceFactory::createManualTasks($account);
            $company = CompanyFactory::create($account);
            CompanyUserFactory::create($company, $user);

            $task = ServiceTaskFactory::createVatApproval($accountService, $company);
            $task->status = ServiceTask::STATUS_COMPLETED;
            $task->save();

            $dossierFileService = resolve(DossierFileService::class);
            $files = $dossierFileService->copyTaskFiles($task);
            $this->assertCount(2, $files);
        }

    public function testCopyToDossierNotAllowed()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;
        $values = [
            ['event' => ServiceTask::TYPE_DOCUMENT_APPROVAL,]
        ];
        TaskAuditLogPreferenceFactory::create($account, $values);

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);
        $task->status = ServiceTask::STATUS_COMPLETED;
        $task->save();

        $dossierFileService = resolve(DossierFileService::class);
        $files = $dossierFileService->copyTaskFiles($task);
        $this->assertCount(1, $files);
    }

    public function testCopyToDossierAllowedNoCustomRuleSet()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);
        $task->status = ServiceTask::STATUS_COMPLETED;
        $task->save();

        $dossierFileService = resolve(DossierFileService::class);
        $files = $dossierFileService->copyTaskFiles($task);
        $this->assertCount(2, $files);
    }

    public function testShowAuditLogEndpointAllowed()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;
        $values = [
            ['event' => ServiceTask::TYPE_VAT_APPROVAL,]
        ];
        TaskAuditLogPreferenceFactory::create($account, $values);

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);

        $this->be($user);

        $response = $this->get($account->route(RouteName::TASK_SHOW_AUDIT_LOG, ['task_id' => $task->id,]));
        $response->assertOk();
        $this->assertTrue($response->json()['data']['show_audit_log']);
    }

    public function testShowAuditLogEndpointNotAllowed()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;
        $values = [
            ['event' => ServiceTask::TYPE_DOCUMENT_APPROVAL,]
        ];
        TaskAuditLogPreferenceFactory::create($account, $values);

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);

        $this->be($user);

        $response = $this->get($account->route(RouteName::TASK_SHOW_AUDIT_LOG, ['task_id' => $task->id,]));
        $response->assertOk();
        $this->assertFalse($response->json()['data']['show_audit_log']);
    }

    public function testShowAuditLogEndpointCustomRuleNotSet()
    {
        $user = UserFactory::createClientUser();
        $account = $user->account;

        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $task = ServiceTaskFactory::createVatApproval($accountService, $company);

        $this->be($user);

        $response = $this->get($account->route(RouteName::TASK_SHOW_AUDIT_LOG, ['task_id' => $task->id,]));
        $response->assertOk();
        $this->assertTrue($response->json()['data']['show_audit_log']);
    }
}