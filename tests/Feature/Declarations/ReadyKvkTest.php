<?php

namespace Tests\Feature\Declarations;

use App\AccountService;
use App\Company;
use App\Http\Responses\Response;
use App\ServiceTask;
use Tests\Support\Builders\AccountServiceBuilder;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceCompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
// use Tests\Support\Factories\TaskFiles\XbrlFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class ReadyKvkTest extends TestCase
{
    public function testSendToKvkWithManualTasks(): void
    {
        $this->markTestSkipped('SL-3566');
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setYearWork()
            ->setLogiusConnection(AccountService::KVK_MANUAL)
            ->build();
        $accountServiceCompany = AccountServiceCompanyFactory::create($accountService);
        $serviceTask = ServiceTaskBuilder::new()
            ->setAccountServiceCompany($accountServiceCompany)
            ->setType(ServiceTask::TYPE_YEARWORK_APPROVAL)
            ->setCompanySize(Company::SMALL)
            ->setStatus(ServiceTask::STATUS_READY_FOR_KVK)
            ->build();

        //XbrlFactory::createPublicationDocumentReport($serviceTask);
        $company = $accountServiceCompany->company;
        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $this->be($internal);
        $response = $this->post(
            'https://' . $account->hostname . '/task/send_to_requesting_party',
            ['task_id' => $serviceTask->id]
        );

        // Assert Equals.
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $serviceTask = $serviceTask->refresh();

        $this->assertEquals(
            ServiceTask::STATUS_PENDING_TO_KVK,
            $serviceTask->status
        );
        $this->assertEquals(
            1,
            $serviceTask->round
        );
    }

    public function testReopenWithManualTasks(): void
    {
        $this->markTestSkipped('SL-3566');
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setYearWork()
            ->setLogiusConnection(AccountService::KVK_MANUAL)
            ->build();
        $accountServiceCompany = AccountServiceCompanyFactory::create($accountService);
        $serviceTask = ServiceTaskBuilder::new()
            ->setAccountServiceCompany($accountServiceCompany)
            ->setType(ServiceTask::TYPE_YEARWORK_APPROVAL)
            ->setCompanySize(Company::SMALL)
            ->setStatus(ServiceTask::STATUS_READY_FOR_KVK)
            ->build();

        //XbrlFactory::createPublicationDocumentReport($serviceTask);

        $company = $accountServiceCompany->company;
        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $this->be($internal);
        $response = $this->post(
            'https://' . $account->hostname . '/task/refresh',
            ['task_id' => $serviceTask->id]
        );

        // Assert Equals.
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $serviceTask = $serviceTask->refresh();

        $this->assertEquals(
            ServiceTask::STATUS_OPEN,
            $serviceTask->status
        );
        $this->assertEquals(
            2,
            $serviceTask->round
        );
    }
}
