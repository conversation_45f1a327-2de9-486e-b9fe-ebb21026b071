<?php

namespace Tests\Feature\Account;

use App\Account;
use App\Mail\Account\ContractOfferMail;
use App\Mail\Account\TrialAccountDeletedMail;
use App\Models\Billing\AccountBilling;
use App\Models\Billing\AccountBillingProduct;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

/**
 * @runTestsInSeparateProcesses
 * @preserveGlobalState  disabled
 */
class AccountTrialTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    public function testSignUpAsDebtor()
    {
        $account = AccountFactory::create();
        $account->status = Account::STATUS_TRIAL;
        $account->save();

        $user = UserFactory::createManager($account);
        $this->be($user);

        $response = $this->post($account->route('account_billing.accept_offer'), [
            'kvk_number' => '********',
            'vat_number' => ' NL********9B10',
            'address' => 'Test Avenue 1',
            'postal_code' => '1234AB',
            'city' => 'Zoetermeer',
            'country' => 'NL',
            'email' => '<EMAIL>',
            'iban' => 'NL69INGB0********9',
            'direct_debit' => true,
            'terms_accepted' => true,
            'summary' => [
                [
                    "product" => "Hix Docs",
                    "sub_products" => [
                        [
                            "name" => "administrations",
                            "sub_product" => "Administrations",
                            "code" => "P0034",
                            "amount" => "123"
                        ],
                        [
                            "name" => "income_tax",
                            "sub_product" => "Income Tax",
                            "code" => "P0035",
                            "amount" => "32"
                        ],
                        [
                            "name" => "corporate_tax",
                            "sub_product" => "Corporate Tax",
                            "code" => "P0035",
                            "amount" => "0"
                        ],
                        [
                            "name" => "micro_small",
                            "sub_product" => "Micro/small",
                            "code" => "P0036",
                            "amount" => "33"
                        ],
                        [
                            "name" => "sbr",
                            "sub_product" => "SBR Assurance",
                            "code" => "P0037",
                            "amount" => "32"
                        ]
                    ]
                ],
                [
                    "product" => "Hix Questions",
                    "sub_products" => [
                        [
                            "name" => "administrations",
                            "sub_product" => "Administrations",
                            "code" => "P0038",
                            "amount" => "123"
                        ]
                    ]
                ]
            ],
            'period' => 'annual_monthly_price'
        ]);
        if ($response->status() === 400) {
            $this->markTestSkipped($response->json('title'));
        }
        $response->assertOk();
        $account->refresh();
        $this->assertNotEmpty($account->debtor_number);
        $this->assertMatchesRegularExpression('/^DB[0-9]{5}$/i', $account->debtor_number);
        $this->assertEquals(Account::STATUS_ACTIVE, $account->status);
        $this->assertNotEmpty($account->activated_at);
        Mail::assertQueued(ContractOfferMail::class);
        $accountBillings = AccountBilling::query()->where(AccountBilling::ACCOUNT_ID, $account->id)->get();
        $allProducts = AccountBillingProduct::all();
        // We need to remove 2 subscription products and at the time (20-11-2023) we have 1 product not yet ready
        $this->assertEquals($allProducts->count() - 3, $accountBillings->count());
        foreach ($accountBillings as $billing) {
            $this->assertEquals(
                $billing->price,
                $allProducts->where(AccountBillingProduct::CODE, $billing->code)->first()->annual_monthly_price
            );
        }
    }

    public function testSignUpAsDebtorOnlyWithRequiredFields()
    {
        $account = AccountFactory::create();
        $account->status = Account::STATUS_TRIAL;
        $account->save();

        $user = UserFactory::createManager($account);
        $this->be($user);

        $response = $this->post($account->route('account_billing.accept_offer'), [
            'email' => '<EMAIL>',
            'terms_accepted' => true,
            'country' => 'NL',
            'summary' => [
                [
                    "product" => "Hix Docs",
                    "sub_products" => [
                        [
                            "name" => "administrations",
                            "sub_product" => "Administrations",
                            "code" => "P0034",
                            "amount" => "123"
                        ],
                        [
                            "name" => "income_tax",
                            "sub_product" => "Income Tax",
                            "code" => "P0035",
                            "amount" => "32"
                        ],
                        [
                            "name" => "corporate_tax",
                            "sub_product" => "Corporate Tax",
                            "code" => "P0035",
                            "amount" => "21"
                        ],
                        [
                            "name" => "micro_small",
                            "sub_product" => "Micro/small",
                            "code" => "P0036",
                            "amount" => "33"
                        ],
                        [
                            "name" => "sbr",
                            "sub_product" => "SBR Assurance",
                            "code" => "P0037",
                            "amount" => "32"
                        ],
                        [
                            "name" => "qualified_signing",
                            "sub_product" => "Qualified signing",
                            "code" => "P0040",
                            "amount" => "3"
                        ]
                    ]
                ],
                [
                    "product" => "Hix Questions",
                    "sub_products" => [
                        [
                            "name" => "administrations",
                            "sub_product" => "Administrations",
                            "code" => "P0038",
                            "amount" => "123"
                        ]
                    ]
                ]
            ],
            'period' => 'annual_monthly_price'
        ]);
        $data = $response->json('data');
        if ($response->status() === 400 && !Str::contains($data, 'Debiteur aanmaken mislukt.')) {
            $this->markTestSkipped($response->json('title'));
        }
        $response->assertOk();
        $account->refresh();
        $this->assertNotEmpty($account->debtor_number);
        $this->assertMatchesRegularExpression('/^DB[0-9]{5}$/i', $account->debtor_number);
        $this->assertEquals(Account::STATUS_ACTIVE, $account->status);
        $this->assertNotEmpty($account->activated_at);
        Mail::assertQueued(ContractOfferMail::class);
    }

    public function testSignUpAsDebtorNotTrial()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createManager($account);
        $this->be($user);
        $response = $this->post($account->route('account_billing.accept_offer'));
        $response->assertForbidden();
    }

    public function testSignUpAsDebtorNotManager()
    {
        $account = AccountFactory::create();
        $account->status = Account::STATUS_TRIAL;
        $account->save();

        $user = UserFactory::createColleague($account);
        $this->be($user);
        $response = $this->post($account->route('account_billing.accept_offer'));
        $response->assertForbidden();
    }

    public function testSendOfferNotTrial()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createManager($account);
        $this->be($user);
        $response = $this->post($account->route('account_billing.send_offer'));
        $response->assertForbidden();
    }

    public function testSendOfferNotManager()
    {
        $account = AccountFactory::create();
        $account->status = Account::STATUS_TRIAL;
        $account->save();

        $user = UserFactory::createColleague($account);
        $this->be($user);
        $response = $this->post($account->route('account_billing.send_offer'));
        $response->assertForbidden();
    }

    public function testSendOffer()
    {
        $account = AccountFactory::create();
        $account->status = Account::STATUS_TRIAL;
        $account->save();

        $user = UserFactory::createManager($account);
        $this->be($user);
        $payload = [
            'summary' => [
                [
                    "product" => "Hix Docs",
                    "sub_products" => [
                        [
                            "name" => "administrations",
                            "sub_product" => "Administrations",
                            "code" => "P0034",
                            "amount" => "123"
                        ],
                        [
                            "name" => "income_tax",
                            "sub_product" => "Income Tax",
                            "code" => "P0035",
                            "amount" => "32"
                        ],
                        [
                            "name" => "corporate_tax",
                            "sub_product" => "Corporate Tax",
                            "code" => "P0035",
                            "amount" => "21"
                        ],
                        [
                            "name" => "micro_small",
                            "sub_product" => "Micro/small",
                            "code" => "P0036",
                            "amount" => "33"
                        ],
                        [
                            "name" => "sbr",
                            "sub_product" => "SBR Assurance",
                            "code" => "P0037",
                            "amount" => "32"
                        ],
                        [
                            "name" => "qualified_signing",
                            "sub_product" => "Qualified signing",
                            "code" => "P0040",
                            "amount" => "3"
                        ]
                    ]
                ],
                [
                    "product" => "Hix Questions",
                    "sub_products" => [
                        [
                            "name" => "administrations",
                            "sub_product" => "Administrations",
                            "code" => "P0038",
                            "amount" => "123"
                        ]
                    ]
                ]
            ],
            'period' => 'annual_monthly_price'
        ];
        $response = $this->post($account->route('account_billing.send_offer'), $payload);
        $response->assertOk();
        Mail::assertQueued(ContractOfferMail::class);
    }

    public function testUpdateNameForbidden()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);
        $response = $this->post($account->route('new.account.update_name'));
        $response->assertForbidden();
    }

    public function testUpdateName()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createManager($account);
        $this->be($user);
        $response = $this->post($account->route('new.account.update_name'), ['name' => 'New name']);
        $response->assertOk();
        $account->refresh();
        $this->assertEquals('New name', $account->name);
    }

    public function testSetToBeFogottenForbidden()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);
        $response = $this->post($account->route('new.account.set_to_be_forgotten'));
        $response->assertForbidden();
    }

    public function testSetToBeFogotten()
    {
        $account = AccountFactory::create();
        $account->status = Account::STATUS_TRIAL;
        $account->save();
        $user = UserFactory::createManager($account);
        $this->be($user);
        $response = $this->post($account->route('new.account.set_to_be_forgotten'));
        $response->assertOk();
        $account->refresh();
        $this->assertEquals(Account::STATUS_TOBEFORGOTTEN, $account->status);
        Mail::assertQueued(TrialAccountDeletedMail::class);
    }
}
