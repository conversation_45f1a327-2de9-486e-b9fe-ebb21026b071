<?php

namespace Tests\Feature\EmailTemplate\Preview;

use App\Models\EmailTemplate\EmailTemplate;
use App\Services\EmailTemplate\EmailTemplateService;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class TaskForceApprovedTest extends TestCase
{
    private ?string $templateUserInvitationJson = '{
            "id": null,
            "language": "nl",
            "name": "Variant 3",
            "type": "task_customer_force_approved",
            "fields": [
                {
                    "content": "Iaak is geforceerd goegekeurd voor :company",
                    "key": "subject"
                },
                {
                    "content": "Hoi :firstname!",
                    "key": "greetings"
                },
                {
                    "content": "Gebruiker :approver heeft taak :title goedgekeurd voor :company",
                    "key": "intro"
                },
                {
                    "content": "Doei!!!<br><br>:account",
                    "key": "regards"
                }
            ],
            "conditions": [
                {
                    "available": true,
                    "event": "task_vat_force_approved",
                    "label": "BTW",
                    "selected": true
                }
            ]
        }';

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testTaskForceApproved()
    {
        $user = UserFactory::createAdminUser();
        $this->be($user);
        $user->firstname = 'Gørdå';
        $user->lastname = 'van der Testenberg';
        $user->language = 'nl';
        $user->save();

        $url = $user->account->route('settings.email.template.store');
        $response = $this->postJson($url, json_decode($this->templateUserInvitationJson, true));
        $response->assertOk();
        $responseArray = json_decode($response->getContent(), true, flags: JSON_THROW_ON_ERROR);
        $responseTemplate = $responseArray['data'];
        $this->assertGreaterThanOrEqual(1, $responseTemplate['id']);

        /** @var EmailTemplateService $service */
        $service = resolve(EmailTemplateService::class);
        $template = EmailTemplate::findOrFail($responseTemplate['id']);
        $mail = $service->testMail($user, $template, false);

        $mail->assertSeeInHtml('Hoi ' . $user->firstname . '!');
        $mail->assertSeeInHtml('Gebruiker ' . $user->firstname . ' ' . $user->lastname. ' heeft taak Testaangifte');
        $mail->assertSeeInHtml(' voor Testbedrijf BV');
    }
}
