<?php

namespace Tests\Feature\Services\Caseware;

use App\AccountService;
use App\CompanyIdentifier;
use App\Http\Responses\Response;
use App\Models\ServiceTask\DocumentApprovalTask;
use App\Models\ServiceTask\YearworkApprovalTask;
use App\Models\TaskFile;
use App\Models\TaskFile\Placeholder;
use App\Services\Gateway\Caseware\CasewareService;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyIdentifierFactory;
use Tests\Support\TestCase;

class CreateTaskTest extends TestCase
{
    private const PAYLOAD_FILE = 'tests/Support/assets/Services/Caseware/payload.json';
    private const PAYLOAD_MISSING_FILE = 'tests/Support/assets/Services/Caseware/payload-missing-statement-file.json';

    public function testCreateTask()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createCaseware($account);
        $company = CompanyFactory::create($account);
        CompanyIdentifierFactory::create($company, '********', CompanyIdentifier::TYPE_KVK);

        $payload = json_decode(file_get_contents(self::PAYLOAD_FILE), 1);

        $response = $this->putJson(
            'https://' . $account->getHostnameAttribute() . '/api/services/caseware/declarations',
            $payload,
            [
                'x-api-key' => $accountService->properties[CasewareService::PROPERTY_API_KEY],
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals(1, $accountService->taskFiles()->count());
    }

    public function testCreateTaskMediumMissingStatementFile()
    {
        $accountService = AccountServiceFactory::createCaseware();
        $payload = json_decode(file_get_contents(self::PAYLOAD_MISSING_FILE), 1);
        $account = $accountService->account;

        $response = $this->putJson(
            'https://' . $account->getHostnameAttribute() . '/api/services/caseware/declarations',
            $payload,
            [
                'x-api-key' => $accountService->properties[CasewareService::PROPERTY_API_KEY],
            ]
        );

        $this->assertEquals(Response::HTTP_PRECONDITION_FAILED, $response->getStatusCode());
    }

    public function testCreateTaskWithoutApiKey()
    {
        $accountService = AccountServiceFactory::createCaseware();
        $payload = json_decode(file_get_contents(self::PAYLOAD_FILE), 1);
        $account = $accountService->account;

        $response = $this->putJson(
            'https://' . $account->getHostnameAttribute() . '/api/services/caseware/declarations',
            $payload
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testCreateYearworkApprovalTaskWithAnchors()
    {
        require_once('vendor-local/setasign/SetaPDF/Autoload.php');
        $payload = [
            'chamberofCommerceId' => '1234567', // same as in XBRL
            'documentType' => 'jaarrekening-klein',
            'documentFile' => base64_encode($this->asset('logius/xbrl/yearwork-2019.xbrl')),
            'statementFile' => '',
            'signatureFile' => '',
            'pdfFile' => base64_encode($this->asset('Services/Caseware/TestAnchor.pdf')),
            'financialYear' => [
                'startDate' => '2020-01-01',
                'endDate' => '2020-12-31',
                'year' => '2020'
            ],
            'attachments' => [],
            'source' => 'CaseWare',
            'bank' => ''
        ];

        $accountService = AccountServiceFactory::createCaseware();
        $company = CompanyFactory::create($accountService->account);
        CompanyIdentifierFactory::create($company, '1234567', CompanyIdentifier::TYPE_KVK);
        $account = $accountService->account;

        $response = $this->putJson(
            'https://' . $account->getHostnameAttribute() . '/api/services/caseware/declarations',
            $payload,
            [
                'x-api-key' => $accountService->properties[CasewareService::PROPERTY_API_KEY],
            ]
        );
        $json = json_decode($response->getContent(), true, JSON_THROW_ON_ERROR);
        $pubdoc = TaskFile::findOrFail($json['id']);
        $this->assertTrue($pubdoc instanceof TaskFile\Xbrl);
        $task = $pubdoc->task;
        $this->assertTrue($task instanceof YearworkApprovalTask);

        list($pdfTaskFile) = $task->getFiles('pdf');
        $this->assertTrue($pdfTaskFile instanceof TaskFile\Pdf);
        $newPlaceholders = Placeholder::where(Placeholder::TASK_FILE_ID, $pdfTaskFile->id)->get();

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals(2, $accountService->taskFiles()->count());
        $this->assertEquals(11, $newPlaceholders->count());
    }

    public function testCreateDocumentApprovalTaskWithAnchors()
    {
        require_once('vendor-local/setasign/SetaPDF/Autoload.php');
        $payload = [
            'chamberofCommerceId' => '********',
            'documentType' => 'opdrachtbevestiging',
            'documentFile' => '',
            'statementFile' => '',
            'signatureFile' => '',
            'pdfFile' => base64_encode($this->asset('Services/Caseware/TestAnchor.pdf')),
            'financialYear' => [
                'startDate' => '2020-01-01',
                'endDate' => '2020-12-31',
                'year' => '2020'
            ],
            'attachments' => [],
            'source' => 'CaseWare',
            'bank' => ''
        ];

        $accountService = AccountServiceFactory::createCaseware();

        $company = CompanyFactory::create($accountService->account);
        CompanyIdentifierFactory::create($company, '********', CompanyIdentifier::TYPE_KVK);

        $account = $accountService->account;

        $response = $this->putJson(
            'https://' . $account->getHostnameAttribute() . '/api/services/caseware/declarations',
            $payload,
            [
                'x-api-key' => $accountService->properties[CasewareService::PROPERTY_API_KEY],
            ]
        );

        $response->assertOk();
        $json = json_decode($response->getContent(), true, JSON_THROW_ON_ERROR);
        $pdfTaskFile = TaskFile::findOrFail($json['id']);
        $this->assertTrue($pdfTaskFile->task instanceof DocumentApprovalTask);
        $this->assertTrue($pdfTaskFile instanceof TaskFile\Pdf);
        $newPlaceholders = Placeholder::where(Placeholder::TASK_FILE_ID, $pdfTaskFile->id)->get();

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals(1, $accountService->taskFiles()->count());
        $this->assertEquals(11, $newPlaceholders->count());
    }
}
