<?php

namespace Tests\Feature\Services\WhatsApp;

use App\AccountService;
use App\Company;
use App\Http\Responses\Response;
use App\Models\Mongo\WhatsApp\WhatsAppMessage;
use App\Models\WhatsApp\WhatsAppUserLastMessage;
use App\Repositories\CompanyUserRepository;
use App\Repositories\UserRepository;
use App\Repositories\WhatsApp\WhatsAppMessageRepository;
use App\Repositories\WhatsApp\WhatsAppUserLastMessageRepository;
use App\Services\Service\WhatsApp\WhatsAppWebhookService;
use App\Services\WhatsAppBusinessService;
use App\User;
use App\ValueObject\Services\WhatsApp\Webhooks\WebhookEntry;
use App\ValueObject\Services\WhatsApp\Webhooks\WebhookMessage;
use App\ValueObject\Services\WhatsApp\Webhooks\WebhookStatusMessage;
use App\WhatsApp\BroadcastEvents\MessageReceivedBroadcastEvent;
use App\WhatsApp\BroadcastEvents\UpdateStatusBroadcastEvent;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\MongoTestCase;

/**
 * @runTestsInSeparateProcesses
 * @preserveGlobalState disabled
 */
class WhatsAppWebhooksTest extends MongoTestCase
{
    protected WhatsAppWebhookService $whatsAppWebhookService;
    protected WhatsAppMessageRepository $whatsAppMessageRepository;
    protected WhatsAppUserLastMessageRepository $whatsAppUserLastMessageRepository;
    protected UserRepository $userRepository;
    protected User $user;
    protected ?AccountService $accountService = null;

    protected const WEBHOOK_TOKEN = 'webhook_token';
    protected const WHATSAPP_APP_SECRET = 'app_secret';
    protected const SHA256_HEADER = 'sha256=6c0cb9eaeeed2fdb797913c6b19c8b139f0f990aefd61ea035575cacc48564ef'; // phpcs:ignore token generated with $this->getMessageContent($this->getMessageValue())
    protected const SHA256_HEADER_STATUS = 'sha256=6aba1260bcf5ef98cb7dc35e8574c0539211abf44b5f7a8de7b5447cf2bf35a4'; // phpcs:ignore token generated with $this->getMessageContent($this->getStatusMessageValue())
    protected const WHATS_APP_ID = '************';
    protected const WHATS_APP_MESSAGE_ID = 'wamid.ID';

    /**
     * @throws \Throwable
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->userRepository = resolve(UserRepository::class);
        $user = UserFactory::createClientUser($this->account);
        $this->userRepository->update(
            $user,
            [
                User::MOBILE => '+' . self::WHATS_APP_ID
            ]
        );
        $this->user = $user;
        $this->whatsAppUserLastMessageRepository = resolve(WhatsAppUserLastMessageRepository::class);
        $this->whatsAppMessageRepository = Mockery::mock(WhatsAppMessageRepository::class);
        $this->whatsAppMessageRepository->makePartial();
        $this->whatsAppMessageRepository->shouldAllowMockingProtectedMethods();
        $this->whatsAppMessageRepository->shouldReceive('getDatabaseName')->andReturn('test_account'); // phpcs:ignore

        $this->whatsAppWebhookService = Mockery::mock(WhatsAppWebhookService::class, [
            $this->whatsAppMessageRepository,
            $this->whatsAppUserLastMessageRepository,
            $this->userRepository
        ]);
        $this->whatsAppWebhookService->makePartial();
        $this->whatsAppWebhookService->shouldAllowMockingProtectedMethods();
        $this->whatsAppWebhookService->shouldReceive('getWebhookToken')->andReturn(self::WEBHOOK_TOKEN); // phpcs:ignore
        $this->whatsAppWebhookService->shouldReceive('getWhatsAppAppSecret')->andReturn(self::WHATSAPP_APP_SECRET);

        $this->app->instance(WhatsAppWebhookService::class, $this->whatsAppWebhookService);

        $this->accountService = AccountServiceFactory::createWhatsAppBusiness($this->account);
        $this->accountService->setProperty(WhatsAppBusinessService::PROPERTY_MOBILE_ID, 'PHONE_NUMBER_ID');
        $this->accountService->save();
    }

    public function testVerifyToken(): void
    {
        $this->assertTrue($this->whatsAppWebhookService->verifyWebhookToken(WhatsAppWebhookService::WEBHOOK_VERIFY_MODE, self::WEBHOOK_TOKEN)); // phpcs:ignore
        $this->assertFalse($this->whatsAppWebhookService->verifyWebhookToken('', self::WEBHOOK_TOKEN));
        $this->assertFalse($this->whatsAppWebhookService->verifyWebhookToken(WhatsAppWebhookService::WEBHOOK_VERIFY_MODE, 'wrong')); // phpcs:ignore
    }

    public function testVerifyPayload(): void
    {
        $content = json_encode($this->getMessageContent($this->getMessageValue()), JSON_THROW_ON_ERROR);
        $this->assertTrue($this->whatsAppWebhookService->validatePayload($content, self::SHA256_HEADER));
        $this->assertFalse($this->whatsAppWebhookService->validatePayload('{wrong_content:true]}', self::SHA256_HEADER)); // phpcs:ignore
        $this->assertFalse($this->whatsAppWebhookService->validatePayload($content, 'wrong_key'));
    }

    public function testMessageReceived(): void
    {
        Event::fake();

        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getMessageValue()));
        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertFalse($whatsAppMessage->sent_by_service);

        $whatsAppUserLastMessage = $this->whatsAppUserLastMessageRepository->getByUser(
            $this->user
        );

        $this->assertInstanceOf(WhatsAppUserLastMessage::class, $whatsAppUserLastMessage);
        $this->assertEquals($whatsAppUserLastMessage->status, WhatsAppUserLastMessage::STATUS_UNREAD);
        $this->assertEquals($whatsAppUserLastMessage->updated_at->toDateTimeString(), $whatsAppMessage->updated_at->toDateTimeString()); // phpcs:ignore

        Event::assertDispatched(MessageReceivedBroadcastEvent::class);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testUpdateStatus(): void
    {
        Event::fake();

        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getMessageValue()));
        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertEquals(WhatsAppMessage::STATUS_SENT, $whatsAppMessage->status); // phpcs:ignore

        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getStatusMessageValue()));
        $whatsAppMessageWithStatusChanged = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessageWithStatusChanged);
        $this->assertEquals($whatsAppMessage->getWhatsAppMessageId(), $whatsAppMessageWithStatusChanged->getWhatsAppMessageId()); // phpcs:ignore
        $this->assertEquals(WhatsAppMessage::STATUS_DELIVERED, $whatsAppMessageWithStatusChanged->status); // phpcs:ignore

        Event::assertDispatched(UpdateStatusBroadcastEvent::class);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testExternalMessageReceived(): void
    {
        Event::fake();

        $this->be($this->user);

        $response = $this->postJson(
            $this->account->route('whatsapp.webhook.post'),
            $this->getMessageContent($this->getMessageValue()),
            [
                WhatsAppWebhookService::SHA256_HEADER_KEY => self::SHA256_HEADER
            ]
        );

        $response->assertStatus(Response::HTTP_OK);

        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertEquals(WhatsAppMessage::STATUS_SENT, $whatsAppMessage->status); // phpcs:ignore

        $whatsAppUserLastMessage = $this->whatsAppUserLastMessageRepository->getByUser(
            $this->user
        );

        $this->assertInstanceOf(WhatsAppUserLastMessage::class, $whatsAppUserLastMessage);
        $this->assertEquals($whatsAppUserLastMessage->status, WhatsAppUserLastMessage::STATUS_UNREAD);
        $this->assertEquals($whatsAppUserLastMessage->updated_at->toDateTimeString(), $whatsAppMessage->updated_at->toDateTimeString()); // phpcs:ignore

        Event::assertDispatched(MessageReceivedBroadcastEvent::class);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testUnreadMessagesForCompaniesCount(): void
    {
        Event::fake();

        // create company and add company users
        $company = new Company();
        $company->name = 'Test Inc.';
        $company->account_id = $this->account->id;
        $company->save();
        $companyUserRepository = resolve(CompanyUserRepository::class);
        $companyUserRepository->addUsers([$this->user->id], $company);

        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getMessageValue()));
        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getMessageValue()));

        $whatsAppBusinessService = resolve(WhatsAppBusinessService::class);
        $unreadMessages = $whatsAppBusinessService->getUnreadWhatsAppMessagesCountForCompany($company);

        $this->assertEquals(2, $unreadMessages);

        Event::assertDispatched(MessageReceivedBroadcastEvent::class);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testUnreadMessagesForUserCount(): void
    {
        Event::fake();

        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getMessageValue()));
        $this->whatsAppWebhookService->saveEntries($this->getMessageContent($this->getMessageValue()));

        $unreadMessages = $this->whatsAppMessageRepository->getUnreadMessagesCountByUser($this->user);

        $this->assertEquals(2, $unreadMessages);

        Event::assertDispatched(MessageReceivedBroadcastEvent::class);
    }

    public function testExternalMessageWrongHeaderReceived(): void
    {
        $this->be($this->user);

        $response = $this->postJson(
            $this->account->route('whatsapp.webhook.post'),
            $this->getMessageContent($this->getMessageValue()),
            [
                WhatsAppWebhookService::SHA256_HEADER_KEY => 'wrong header'
            ]
        );

        $response->assertStatus(Response::HTTP_NOT_FOUND);
    }


    public function testExternalMessageWrongContentReceived(): void
    {
        $this->be($this->user);

        $response = $this->postJson(
            $this->account->route('whatsapp.webhook.post'),
            [
                'wrong' => true
            ],
            [
                WhatsAppWebhookService::SHA256_HEADER_KEY => self::SHA256_HEADER
            ]
        );

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testExternalStatusMessageReceivedWithCreatedMessage(): void
    {
        Event::fake();
        $this->be($this->user);

        // create message
        $response = $this->postJson(
            $this->account->route('whatsapp.webhook.post'),
            $this->getMessageContent($this->getMessageValue()),
            [
                WhatsAppWebhookService::SHA256_HEADER_KEY => self::SHA256_HEADER
            ]
        );
        $response->assertStatus(Response::HTTP_OK);

        // check database
        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );
        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertEquals(WhatsAppMessage::STATUS_SENT, $whatsAppMessage->status); // phpcs:ignore

        // change status with next message
        $response = $this->postJson(
            $this->account->route('whatsapp.webhook.post'),
            $this->getMessageContent($this->getStatusMessageValue()),
            [
                WhatsAppWebhookService::SHA256_HEADER_KEY => self::SHA256_HEADER_STATUS
            ]
        );
        $response->assertStatus(Response::HTTP_OK);

        // check database
        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );
        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertEquals(WhatsAppMessage::STATUS_DELIVERED, $whatsAppMessage->status); // phpcs:ignore

        Event::assertDispatched(MessageReceivedBroadcastEvent::class);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testExternalStatusMessageReceivedWithoutCreatedMessage(): void
    {
        Event::fake();
        $this->be($this->user);

        // message received
        $response = $this->postJson(
            $this->account->route('whatsapp.webhook.post'),
            $this->getMessageContent($this->getStatusMessageValue()),
            [
                WhatsAppWebhookService::SHA256_HEADER_KEY => self::SHA256_HEADER_STATUS
            ]
        );
        $response->assertStatus(Response::HTTP_OK);

        // we do nothing because we have no whatsapp message id in database
        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $this->user,
            self::WHATS_APP_MESSAGE_ID
        );
        $this->assertNull($whatsAppMessage);
        Event::assertDispatched(UpdateStatusBroadcastEvent::class);
    }

    public function testExternalVerifyToken(): void
    {
        $this->be($this->user);

        $challenge = 123;

        // message received
        $response = $this->get(
            $this->account->route('whatsapp.webhook.get', [
                'hub.mode' => WhatsAppWebhookService::WEBHOOK_VERIFY_MODE,
                'hub.challenge' => $challenge,
                'hub.verify_token' => self::WEBHOOK_TOKEN
            ])
        );

        // we answer the challenge they sent before if everything is validated
        $response->assertStatus(Response::HTTP_OK);
        $this->assertEquals($response->getContent(), $challenge);
    }

    public function testExternalVerifyTokenWrongRequest(): void
    {
        $this->be($this->user);

        // wrong parameters
        $response = $this->get(
            $this->account->route('whatsapp.webhook.get', [
                'hub.mode' => 'wrong'
            ])
        );

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);

        // wrong token
        $response = $this->get(
            $this->account->route('whatsapp.webhook.get', [
                'hub.mode' => WhatsAppWebhookService::WEBHOOK_VERIFY_MODE,
                'hub.challenge' => 123,
                'hub.verify_token' => 'lalala'
            ])
        );

        // we answer the challenge they sent before if everything is validated
        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testWebhooksValueObjects(): void
    {
        $webhookEntry = new WebhookEntry($this->getMessageContent($this->getMessageValue())['entry'][0]);
        $this->assertIsArray($webhookEntry->getChanges());
        $this->assertCount(1, $webhookEntry->getChanges());
        $this->assertInstanceOf(WebhookMessage::class, $webhookEntry->getChanges()[0]);

        $webhookEntry = new WebhookEntry($this->getMessageContent($this->getStatusMessageValue())['entry'][0]);
        $this->assertIsArray($webhookEntry->getChanges());
        $this->assertCount(1, $webhookEntry->getChanges());
        $this->assertInstanceOf(WebhookStatusMessage::class, $webhookEntry->getChanges()[0]);

        $webhookMessage = new WebhookMessage($this->getMessageValue());
        $this->assertIsArray($webhookMessage->getMessages());
        $this->assertIsArray($webhookMessage->getContacts());

        $webhookMessage = new WebhookStatusMessage($this->getStatusMessageValue());
        $this->assertIsArray($webhookMessage->getStatuses());
    }

    protected function getMessageContent(array $value = []): array
    {
        return [
            'object' => 'whatsapp_business_account',
            'entry' => [
                [
                    'id' => 'WHATSAPP_BUSINESS_ACCOUNT_ID',
                    'changes' => [
                        [
                            'value' => $value,
                            'field' => 'messages'
                        ]
                    ]
                ]
            ]
        ];
    }

    protected function getMessageValue(): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'metadata' => [
                'display_phone_number' => 'PHONE_NUMBER',
                'phone_number_id' => 'PHONE_NUMBER_ID',
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'ambrosio'
                    ],
                    'wa_id' => self::WHATS_APP_ID
                ]
            ],
            'messages' => [
                [
                    'from' => '************',
                    'id' => self::WHATS_APP_MESSAGE_ID,
                    'timestamp' => '**********',
                    'text' => [
                        'body' => 'ambrosiooooo there?'
                    ],
                    'type' => 'text'
                ]
            ]
        ];
    }

    protected function getStatusMessageValue(): array
    {
        return [
            'messaging_product' => 'whatsapp',
            'metadata' => [
                'display_phone_number' => 'PHONE_NUMBER',
                'phone_number_id' => 'PHONE_NUMBER_ID',
            ],
            'contacts' => [
                [
                    'profile' => [
                        'name' => 'ambrosio'
                    ],
                    'wa_id' => self::WHATS_APP_ID
                ]
            ],
            'statuses' => [
                [
                    'id' => self::WHATS_APP_MESSAGE_ID,
                    'status' => 'delivered',
                    'timestamp' => '**********',
                    'recipient_id' => self::WHATS_APP_ID
                ]
            ],
            'conversation' => [
                [
                    'id' => 'CONVERSATION_ID',
                    'expiration_timestamp' => '**********',
                    'origin' => [
                        'type' => 'user_initiated'
                    ]
                ]
            ],
            'pricing' => [
                'pricing' => true,
                'pricing_model' => 'CBP',
                'category' => 'user_initiated'
            ],
        ];
    }
}
