<?php

namespace Tests\Feature\Services\WhatsApp\ServiceTask;

use App;
use App\Mail\ServiceTask\ServiceTaskNotifierFactory;
use App\Mail\ServiceTask\ServiceTaskReminderNotifierFactory;
use App\Notifier\Objects\ServiceTask\ServiceTaskNotifierService;
use App\Notifier\Objects\ServiceTask\Types\SbrYearworkApproval\SbrYearworkApprovalForcedNotifier;
use App\Notifier\Objects\ServiceTask\Types\SbrYearworkApproval\SbrYearworkApprovalNotifier;
use App\Notifier\Objects\ServiceTask\Types\SbrYearworkApproval\SbrYearworkApprovalReminderNotifier;
use App\Notifier\Objects\ServiceTask\Types\SbrYearworkApproval\SbrYearworkApprovedNotifier;
use App\ServiceTask;
use App\ServiceTaskResponse;
use App\WhatsApp\ServiceTask\ForceApprovalWhatsApp;
use App\WhatsApp\ServiceTask\ReminderWhatsApp;
use App\WhatsApp\ServiceTask\TaskCompletedWhatsApp;
use App\WhatsApp\ServiceTask\TaskSentWhatsApp;
use JsonException;
use Tests\Feature\Services\WhatsApp\WhatsAppTestTrait;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\ServiceTaskResponseFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\MongoTestCase;
use Throwable;

class SbrYearworkApprovalTest extends MongoTestCase
{
    use WhatsAppTestTrait;

    protected function setUp(): void
    {
        parent::setUp();
        $this->handleMockCloudApiService();
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendSbrYearworkApprovalToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createSbrYearworkApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(SbrYearworkApprovalNotifier::class, $notifier);
        $this->assertInstanceOf(TaskSentWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendSbrYearworkInformToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createSbrYearworkApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_COMPLETED;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_INFORM;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(SbrYearworkApprovalNotifier::class, $notifier);
        $this->assertInstanceOf(TaskSentWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendSbrYearworkForceApprovalToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createSbrYearworkApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        // Do the setApprove as colleague to trigger force approve;
        $colleague = UserFactory::createColleague($this->account);
        $this->be($colleague);

        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->setApproved();
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(SbrYearworkApprovalForcedNotifier::class, $notifier);
        $this->assertInstanceOf(ForceApprovalWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendSbrYearworkApprovedToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createSbrYearworkApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_COMPLETED;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(SbrYearworkApprovedNotifier::class, $notifier);
        $this->assertInstanceOf(TaskCompletedWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendSbrYearworkApprovalReminderToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createSbrYearworkApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskReminderNotifierFactory::create($serviceTaskResponse);
        $this->assertInstanceOf(SbrYearworkApprovalReminderNotifier::class, $notifier);
        $this->assertInstanceOf(ReminderWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }
}
