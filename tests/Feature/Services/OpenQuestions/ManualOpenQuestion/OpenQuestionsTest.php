<?php

namespace Tests\Feature\Services\OpenQuestions\ManualOpenQuestion;

use App;
use App\Models\OpenQuestions\Questions\Bookkeeping;
use App\Models\OpenQuestions\Questions\Fiscal;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\OpticalCharacterRecognition;
use App\Models\OpenQuestions\Questions\Other;
use App\Models\OpenQuestions\Questions\Wage;
use App\Services\OpenQuestions\OpenQuestionService;
use App\Support\Carbon;
use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\OpenQuestions\OpenQuestionTypeFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\Factories\ValueObject\AgentDataFactory;
use Tests\Support\TestCase;

class OpenQuestionsTest extends TestCase
{
    private ?OpenQuestionService $openQuestionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->openQuestionService = App::make(OpenQuestionService::class);
    }

    public function testCreatingManualBookkeepingQuestion(): void
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createManualQuestions($user->account);

        $company = CompanyFactory::create($user->account);
        $company->users()->attach($user);

        $openQuestionType = OpenQuestionTypeFactory::create($accountService, 'missing_invoice', 'Missing Invoice');
        $attributes = [
            'company_id' => $company->id,
            'name' => 'Test Transaction Holder',
            'title' => 'Test Transaction Holder',
            'amount' => '59',
            'transaction_date' => Carbon::now()->format('Y-m-d'),
            'invoice_number' => '123456',
            'type_id' => $openQuestionType->id,
            'attachment_needed' => false,
            'missing_invoice_type' => 'sale'
        ];

        $this->be($user);
        $agentData = AgentDataFactory::create()->toArray();

        //Creating questions with correct attributes
        $response = $this->post(
            $user->getAccount()->route('open_questions.bookkeeping.manual_question'),
            $attributes,
            $agentData
        );
        $response->assertStatus(Response::HTTP_OK);

        $openQuestion = OpenQuestion::query()
            ->where('company_id', $company->id)
            ->where('title', $attributes['name'])
            ->first();

        $bookkeeping = Bookkeeping::query()
            ->where('open_question_id', $openQuestion->id)
            ->first();

        $this->assertNotNull($bookkeeping);
        $this->assertNotNull($openQuestion);
        $this->assertEquals($attributes['type_id'], $openQuestion->type_id);

        $auditLogs = $this->openQuestionService->auditLogs(
            $openQuestion->id,
            $user
        );
        $this->assertEquals(
            2,
            $auditLogs->count()
        );
    }

    public function testCreatingManualFiscalQuestion(): void
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createManualQuestions($user->account);

        $company = CompanyFactory::create($user->account);
        $company->users()->attach($user);

        $openQuestionType = OpenQuestionTypeFactory::create($accountService, 'missing_invoice', 'Missing Invoice');
        $attributes = [
            'company_id' => $company->id,
            'name' => 'Test Transaction Holder',
            'title' => 'Test Transaction Holder',
            'type_id' => $openQuestionType->id,
            'attachment_needed' => false,
        ];

        $this->be($user);
        $agentData = AgentDataFactory::create()->toArray();

        //Creating questions with correct attributes
        $response = $this->post(
            $user->getAccount()->route('open_questions.create.fiscal.manual_question'),
            $attributes,
            $agentData
        );
        $response->assertStatus(Response::HTTP_OK);

        $openQuestion = OpenQuestion::query()
            ->where('company_id', $company->id)
            ->where('title', $attributes['name'])
            ->first();

        $fiscal = Fiscal::query()
            ->where('open_question_id', $openQuestion->id)
            ->first();

        $this->assertNotNull($fiscal);
        $this->assertNotNull($openQuestion);
        $this->assertEquals($attributes['type_id'], $openQuestion->type_id);

        $auditLogs = $this->openQuestionService->auditLogs(
            $openQuestion->id,
            $user
        );
        $this->assertEquals(
            2,
            $auditLogs->count()
        );
    }

    public function testCreatingManualOcrQuestion(): void
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createManualQuestions($user->account);

        $company = CompanyFactory::create($user->account);
        $company->users()->attach($user);

        $openQuestionType = OpenQuestionTypeFactory::create($accountService, 'missing_invoice', 'Missing Invoice');
        $attributes = [
            'company_id' => $company->id,
            'name' => 'Test Transaction Holder',
            'title' => 'Test Transaction Holder',
            'type_id' => $openQuestionType->id,
            'attachment_needed' => false,
        ];

        $this->be($user);
        $agentData = AgentDataFactory::create()->toArray();

        //Creating questions with correct attributes
        $response = $this->post(
            $user->getAccount()->route('open_questions.create.ocr.manual_question'),
            $attributes,
            $agentData
        );
        $response->assertStatus(Response::HTTP_OK);

        $openQuestion = OpenQuestion::query()
            ->where('company_id', $company->id)
            ->where('title', $attributes['name'])
            ->first();

        $ocr = OpticalCharacterRecognition::query()
            ->where('open_question_id', $openQuestion->id)
            ->first();

        $this->assertNotNull($ocr);
        $this->assertNotNull($openQuestion);
        $this->assertEquals($attributes['type_id'], $openQuestion->type_id);

        $auditLogs = $this->openQuestionService->auditLogs(
            $openQuestion->id,
            $user
        );
        $this->assertEquals(
            2,
            $auditLogs->count()
        );
    }

    public function testCreatingManualOtherQuestion(): void
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createManualQuestions($user->account);

        $company = CompanyFactory::create($user->account);
        $company->users()->attach($user);

        $openQuestionType = OpenQuestionTypeFactory::create($accountService, 'missing_invoice', 'Missing Invoice');
        $attributes = [
            'company_id' => $company->id,
            'name' => 'Test Transaction Holder',
            'title' => 'Test Transaction Holder',
            'type_id' => $openQuestionType->id,
            'attachment_needed' => false,
        ];

        $this->be($user);
        $agentData = AgentDataFactory::create()->toArray();

        //Creating questions with correct attributes
        $response = $this->post(
            $user->getAccount()->route('open_questions.create.other.manual_question'),
            $attributes,
            $agentData
        );
        $response->assertStatus(Response::HTTP_OK);

        $openQuestion = OpenQuestion::query()
            ->where('company_id', $company->id)
            ->where('title', $attributes['name'])
            ->first();

        $other = Other::query()
            ->where('open_question_id', $openQuestion->id)
            ->first();

        $this->assertNotNull($other);
        $this->assertNotNull($openQuestion);
        $this->assertEquals($attributes['type_id'], $openQuestion->type_id);

        $auditLogs = $this->openQuestionService->auditLogs(
            $openQuestion->id,
            $user
        );
        $this->assertEquals(
            2,
            $auditLogs->count()
        );
    }

    public function testCreatingManualWageQuestion(): void
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createManualQuestions($user->account);

        $company = CompanyFactory::create($user->account);
        $company->users()->attach($user);

        $openQuestionType = OpenQuestionTypeFactory::create(
            $accountService,
            OpenQuestion::TYPE_WHK_STATEMENT,
            'Missing Invoice'
        );
        $attributes = [
            'company_id' => $company->id,
            'name' => 'Test Transaction Holder',
            'title' => 'Test Transaction Holder',
            'type_id' => $openQuestionType->id,
            'attachment_needed' => false,
        ];

        $this->be($user);
        $agentData = AgentDataFactory::create()->toArray();

        //Creating questions with correct attributes
        $response = $this->post(
            $user->getAccount()->route('open_questions.create.wage.manual_question'),
            $attributes,
            $agentData
        );
        $response->assertStatus(Response::HTTP_OK);

        $openQuestion = OpenQuestion::query()
            ->where('company_id', $company->id)
            ->where('title', $attributes['name'])
            ->first();

        $wage = Wage::query()
            ->where('open_question_id', $openQuestion->id)
            ->first();

        $this->assertNotNull($wage);
        $this->assertNotNull($openQuestion);
        $this->assertEquals($attributes['type_id'], $openQuestion->type_id);

        $auditLogs = $this->openQuestionService->auditLogs(
            $openQuestion->id,
            $user
        );
        $this->assertEquals(
            2,
            $auditLogs->count()
        );
    }
}
