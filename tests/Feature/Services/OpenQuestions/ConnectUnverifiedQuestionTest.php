<?php

namespace Tests\Feature\Services\OpenQuestions;

use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Carbon\Carbon;
use Tests\Support\Builders\UnverifiedQuestionBuilder;
use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class ConnectUnverifiedQuestionTest extends TestCase
{
    public function testGetAvailableCompanies()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualQuestions($account);
        $user = UserFactory::createColleague($account);
        $this->be($user);
        CompanyUserFactory::createCompaniesForUser($user, 5);

        $response = $this->get($account->route('unverified.questions.available_companies'));
        $response->assertOk();

        $this->assertCount(5, $response->json());
    }

    public function testGetUnconnectedQuestions()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualQuestions($account);
        $user = UserFactory::createColleague($account);
        $this->be($user);
        $company = CompanyFactory::create($account);

        UnverifiedQuestionBuilder::new()->setAccount($account)->setCompany($company)->build();
        UnverifiedQuestionBuilder::new()->setAccount($account)->setVerifiedAt(Carbon::now()->subDay())->build();
        UnverifiedQuestionBuilder::new()->setAccount($account)->build();

        $response = $this->get($account->route('unverified.questions.unconnected_questions'))->assertOk();

        $this->assertCount(1, $response->json());
    }

    public function testConnectToCompanyUnauthorized()
    {
        $user = UserFactory::createColleague();
        $this->be($user);
        $account = $user->account;
        AccountServiceFactory::createManualQuestions($account);

        $question = UnverifiedQuestionBuilder::new()
            ->setAccount($account)
            ->setVerifiedAt(Carbon::now()->subDay())
            ->build();
        $company = CompanyFactory::create($account);
        $company2 = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $response = $this->post(
            $account->route('unverified.questions.connect'),
            [
                'connections' => [
                    [
                        'external' => [
                            'id' => $question->id
                        ],
                        'internal' => [
                            'id' => $company2->id
                        ]
                    ]
                ]
            ]
        );
        $response->assertStatus(Response::HTTP_FORBIDDEN);
        $question->refresh();
        $this->assertNull($question->company_id);
    }

    public function testConnectToCompany()
    {
        $user = UserFactory::createColleague();
        $this->be($user);
        $account = $user->account;
        AccountServiceFactory::createManualQuestions($account);

        $question = UnverifiedQuestionBuilder::new()
            ->setAccount($account)
            ->setVerifiedAt(Carbon::now()->subDay())
            ->build();
        $question2 = UnverifiedQuestionBuilder::new()
            ->setAccount($account)
            ->setVerifiedAt(Carbon::now()->subDay())
            ->build();
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $user);

        $response = $this->post(
            $account->route('unverified.questions.connect'),
            [
                'connections' => [
                    [
                        'external' => [
                            'id' => $question->id
                        ],
                        'internal' => [
                            'id' => $company->id
                        ]
                    ],
                    [
                        'external' => [
                            'id' => $question2->id
                        ],
                        'internal' => [
                            'id' => $company->id
                        ]
                    ],
                ]
            ]
        );
        $response->assertStatus(Response::HTTP_OK);
        $question->refresh();
        $question2->refresh();
        $this->assertEquals($question->company_id, $company->id);
        $this->assertEquals($question2->company_id, $company->id);

        $openQuestions = OpenQuestion::query()
            ->where('company_id', $question->company_id)
            ->where('category', OpenQuestionCategory::CLIENT)
            ->get();
        $this->assertCount(2, $openQuestions);
    }
}
