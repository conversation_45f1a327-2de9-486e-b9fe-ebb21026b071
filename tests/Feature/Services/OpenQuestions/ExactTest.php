<?php

namespace Tests\Feature\Services\OpenQuestions;

use App\Services\Gateway\Exact\ExactOnlineClient;
use App\Token;
use App\Support\Carbon;
use Tests\Support\Builders\TokenBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class ExactTest extends TestCase
{
    public function testConfigureWithoutDeclarationsService()
    {
        $account = AccountFactory::create();
        $openQuestionsAccountService = AccountServiceFactory::createExactOpenQuestions($account);
        $user = UserFactory::createManager($account);
        $this->be($user);

        $response = $this->get($account->route('open_questions.exact.configure', $openQuestionsAccountService->id));
        $response->assertOk();
        $this->assertArrayHasKey('redirect', $response->json('data'));
        $this->assertStringContainsString(
            'https://start.exactonline.nl/api/oauth2/auth?client_id=',
            $response->json('data')['redirect']
        );

        $this->assertStringContainsString(
            'response_type=code&state',
            $response->json('data')['redirect']
        );
    }

    public function testConfigureWithDeclarationsService()
    {
        $account = AccountFactory::create();
        $declarationsAccountService = AccountServiceFactory::createExactDeclarations($account);
        $declarationsRefreshToken = TokenBuilder::new()
            ->setAccountService($declarationsAccountService)
            ->setAccount($account)
            ->setType(ExactOnlineClient::REFRESH_TOKEN_TYPE)
            ->setExpirationDate(Carbon::now()->addDay())
            ->build();

        $declarationsAccessToken = TokenBuilder::new()
            ->setAccountService($declarationsAccountService)
            ->setAccount($account)
            ->setType(ExactOnlineClient::ACCESS_TOKEN_TYPE)
            ->setExpirationDate(Carbon::now()->addDay())
            ->build();
        $openQuestionsAccountService = AccountServiceFactory::createExactOpenQuestions($account);
        $user = UserFactory::createManager($account);
        $this->be($user);

        $response = $this->get($account->route('open_questions.exact.configure', $openQuestionsAccountService->id));

        $response->assertOk();
        $this->assertNull($response->json('data'));

        $openQuestionsRefreshToken = Token::query()
            ->where(Token::ACCOUNT_SERVICE_ID, $openQuestionsAccountService->id)
            ->where(Token::TYPE, ExactOnlineClient::REFRESH_TOKEN_TYPE)
            ->first();

        $openQuestionsAccessToken = Token::query()
            ->where(Token::ACCOUNT_SERVICE_ID, $openQuestionsAccountService->id)
            ->where(Token::TYPE, ExactOnlineClient::ACCESS_TOKEN_TYPE)
            ->first();
        $this->assertEquals($declarationsRefreshToken->token, $openQuestionsRefreshToken->token);
        $this->assertEquals($declarationsAccessToken->token, $openQuestionsAccessToken->token);
    }
}
