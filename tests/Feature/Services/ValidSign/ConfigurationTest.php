<?php

namespace Tests\Feature\Services\ValidSign;

use App\Account;
use App\AccountService;
use App\Http\Responses\Response;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;
class ConfigurationTest extends TestCase
{
    private ?AccountService $accountService;
    private ?Account $account;

    public function setUp(): void
    {
        parent::setUp();
        $this->accountService = AccountServiceFactory::createValidSign();
        $this->account = $this->accountService->account;
        $this->be(UserFactory::createManager($this->account));
    }

    public function testCorrectConfiguration()
    {
        $this->markTestSkipped();
        $email = "<EMAIL>";
        $apiKey = "MHlEI3RxIzY2aU4yVlhkZ2VlNWVQRg==";

        $response = $this->post(
            'https://' . $this->account->getHostnameAttribute() . '/service/' . $this->accountService->id . '/valid_sign/configure', //phpcs:ignore
            [
                'email' => $email,
                'api_key' => $apiKey
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }
}
