<?php

namespace Tests\Feature\Controllers;

use Symfony\Component\HttpFoundation\Response;
use Tests\Helper\DataCreator;
use Tests\Support\OldTestCase;

class NotificationTest extends OldTestCase
{
  protected static $notificationCountEndpoint = 'new.notification.count';

  public function testNotificationCountDoesNotRenewSession(){
    $this->dataCreator = new DataCreator($this->dataCreatorParameters);
    $this->dataCreator->assignLicenses(['new_ui']);
    $this->be($this->dataCreator->getUser());

    $response = $this->assertHttpGet(self::$notificationCountEndpoint, ['user_id' => 1], Response::HTTP_OK);
    $cookie = $response->getSessionCookie();
    $this->assertNull($cookie,"Session cookie should not be set");
  }
}