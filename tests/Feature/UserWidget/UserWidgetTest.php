<?php

namespace Tests\Feature\UserWidget;

use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\Factories\WidgetFactory;
use Tests\Support\TestCase;

class UserWidgetTest extends TestCase
{
    public function testGetUserWidgets()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);

        $userWidget = WidgetFactory::createUserWidget($user);
        $userWidget2 = WidgetFactory::createUserWidget($user);

        $response = $this->get($account->route('new.member.current_widgets'));
        $response->assertOk();

        $widgets = $response->json('widgets');
        $this->assertCount(2, $widgets);
        $this->assertEquals($userWidget->id, $widgets[0]['id']);
        $this->assertEquals($userWidget2->id, $widgets[1]['id']);
    }

    public function testGetUserWidgetsWithGenericWidgetSoftDeleted()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);

        $userWidget = WidgetFactory::createUserWidget($user);
        $userWidget->generic_widget->delete();
        $userWidget2 = WidgetFactory::createUserWidget($user);

        $response = $this->get($account->route('new.member.current_widgets'));
        $response->assertOk();

        $widgets = $response->json('widgets');
        $this->assertCount(1, $widgets);
        $this->assertEquals($userWidget2->id, $widgets[0]['id']);
    }

}