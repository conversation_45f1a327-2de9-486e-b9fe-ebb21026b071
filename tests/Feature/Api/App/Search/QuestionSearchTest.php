<?php

namespace Tests\Feature\Api\App\Search;

use App\Account;
use App\AccountService;
use App\Company;
use App\Enums\Meilisearch\Filters\LastUpdated;
use App\License;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\User;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\OpenQuestions\NotifierUserSettingFactory;
use Tests\Support\Factories\OpenQuestions\OpenQuestionFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\MeilisearchTestCase;

class QuestionSearchTest extends MeilisearchTestCase
{
    public const ENDPOINT = 'api.app.search.questions';

    private ?User $managerUser;
    private ?User $companyManagerUser;
    private ?User $colleagueUser;
    private ?User $clientUser;

    private ?Account $account;
    private ?Company $company;
    private ?AccountService $accountService;
    private ?OpenQuestion $question1;
    private ?OpenQuestion $question2;
    private ?OpenQuestion $question3;

    public function testExecuteAll()
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $this->account = AccountFactory::create();

        $this->managerUser = UserFactory::createManager($this->account);
        $this->companyManagerUser = UserFactory::createCompanyManager($this->account);
        $this->colleagueUser = UserFactory::createColleague($this->account);
        $this->clientUser = UserFactory::createClientUser($this->account);


        $this->company = CompanyFactory::create($this->account);
        $this->company->name = 'Meilisearch Company';
        $this->company->save();

        CompanyUserFactory::create($this->company, $this->managerUser);
        CompanyUserFactory::create($this->company, $this->companyManagerUser);
        CompanyUserFactory::create($this->company, $this->colleagueUser);
        $clientCompanyUser = CompanyUserFactory::create($this->company, $this->clientUser);
        NotifierUserSettingFactory::create($clientCompanyUser);

        $this->accountService = AccountServiceFactory::create($this->account);
        $this->accountService->display_name = 'Meilisearch Account Service';
        $this->accountService->save();

        $this->question1 = OpenQuestionFactory::create($this->company, $this->accountService);
        $this->question1->status = OpenQuestion::STATUS_COMPLETED;
        $this->question1->title = 'Meilisearch Question';
        $this->question1->updated_at = now()->subDay();
        $this->question1->save();

        $this->question2 = OpenQuestionFactory::create($this->company, $this->accountService);
        $this->question2->category = OpenQuestionCategory::FISCAL;
        $this->question2->title = 'Meilisearch Question';
        $this->question2->save();

        $this->question3 = OpenQuestionFactory::create($this->company, $this->accountService);
        $this->question3->title = 'Meilisearch Question';
        $this->question3->updated_at = now()->subDays(2);
        $this->question3->save();

        $this->waitForMeilisearchTasksToFinish();

        $this->questionSearchData();
        $this->questionSearchDateFilter();
        $this->questionSearchCustomDateFilter();
        $this->questionSearchInvalidDateFilter();
        $this->questionSearchStatusFilter();
        $this->questionSearchInvalidStatusFilter();
        $this->questionSearchCategoryFilter();
        $this->questionSearchInvalidTypeFilter();
        $this->companyManagerSearching();
        $this->colleagueUserSearching();
        $this->clientUserSearching();
        $this->withoutParams();
        $this->unauthorized();
        $this->withoutLicense();
    }

    public function questionSearchData()
    {
        $this->be($this->managerUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch'
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(3, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question1->id, $data[0]['id']);
        $this->assertEquals($this->question1->title, $data[0]['title']);

        $this->assertNotNull($data[1]);
        $this->assertEquals($this->question2->id, $data[1]['id']);
        $this->assertEquals($this->question2->title, $data[1]['title']);

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question3->id, $data[2]['id']);
        $this->assertEquals($this->question3->title, $data[2]['title']);
    }

    public function questionSearchDateFilter()
    {
        $this->be($this->managerUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'last_updated' => LastUpdated::YESTERDAY
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(1, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question1->id, $data[0]['id']);
        $this->assertEquals($this->question1->title, $data[0]['title']);
    }

    public function questionSearchCustomDateFilter()
    {
        $this->be($this->managerUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'last_updated' => LastUpdated::CUSTOM,
            'start_date' => now()->subDays(2)->format('Y-m-d'),
            'end_date' => now()->subDays(2)->format('Y-m-d')
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(1, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question3->id, $data[0]['id']);
        $this->assertEquals($this->question3->title, $data[0]['title']);
    }

    public function questionSearchInvalidDateFilter()
    {
        $this->be($this->managerUser);

        $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'last_updated' => 'invalid',
            'start_date' => 'invalid',
            'end_date' => 'invalid'
        ]))->assertUnprocessable();
    }

    public function questionSearchStatusFilter()
    {
        $this->be($this->managerUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'statuses' => [OpenQuestion::STATUS_COMPLETED],
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(1, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question1->id, $data[0]['id']);
        $this->assertEquals($this->question1->title, $data[0]['title']);
    }

    public function questionSearchInvalidStatusFilter()
    {
        $this->be($this->managerUser);

        $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'statuses' => ['invalid'],
        ]))->assertUnprocessable();
    }

    public function questionSearchCategoryFilter()
    {
        $this->be($this->managerUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'categories' => [OpenQuestionCategory::FISCAL],
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(1, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question2->id, $data[0]['id']);
        $this->assertEquals($this->question2->title, $data[0]['title']);
    }

    public function questionSearchInvalidTypeFilter()
    {
        $this->be($this->managerUser);

        $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch',
            'categories' => ['invalid'],
        ]))->assertUnprocessable();
    }

    public function companyManagerSearching()
    {
        auth()->logout();

        $this->be($this->companyManagerUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch'
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(3, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question1->id, $data[0]['id']);
        $this->assertEquals($this->question1->title, $data[0]['title']);

        $this->assertNotNull($data[1]);
        $this->assertEquals($this->question2->id, $data[1]['id']);
        $this->assertEquals($this->question2->title, $data[1]['title']);

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question3->id, $data[2]['id']);
        $this->assertEquals($this->question3->title, $data[2]['title']);
    }

    public function colleagueUserSearching()
    {
        auth()->logout();

        $this->be($this->colleagueUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch'
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(3, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question1->id, $data[0]['id']);
        $this->assertEquals($this->question1->title, $data[0]['title']);

        $this->assertNotNull($data[1]);
        $this->assertEquals($this->question2->id, $data[1]['id']);
        $this->assertEquals($this->question2->title, $data[1]['title']);

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question3->id, $data[2]['id']);
        $this->assertEquals($this->question3->title, $data[2]['title']);
    }

    public function clientUserSearching()
    {
        auth()->logout();

        $this->be($this->clientUser);

        $response = $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch'
        ]))->assertOk();

        $data = $response->json('data');

        $this->assertEquals(1, count($data));

        $this->assertNotNull($data[0]);
        $this->assertEquals($this->question3->id, $data[0]['id']);
        $this->assertEquals($this->question3->title, $data[0]['title']);
    }

    public function withoutParams()
    {
        auth()->logout();

        $this->be($this->managerUser);

        $this->get($this->account->route(self::ENDPOINT))->assertUnprocessable();
    }

    public function unauthorized()
    {
        auth()->logout();

        $this->get($this->account->route(self::ENDPOINT, [
            'search' => 'Meilisearch'
        ]))->assertFound();
    }

    public function withoutLicense()
    {
        auth()->logout();

        $this->be($this->managerUser);

        $this->account->licenses()->where(License::REFERENCE_NAME, License::MEILISEARCH)->delete();

        $this->get($this->account->route(self::ENDPOINT))->assertForbidden();
    }
}
