<?php

namespace Tests\Feature\Api\App\Auth\Reactivation;

use App\Enums\RouteName;
use App\Services\TokenService;
use App\Token;
use App\User;
use Illuminate\Support\Facades\Mail;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class VerifyTokenTest extends TestCase
{
    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testVerifyTokenWithoutMasterkeySuccess(): void
    {
        Mail::fake();
        // create manager who should receive mail
        $manager = UserFactory::createManager();

        $account = $manager->account;
        $tokenService = resolve(TokenService::class);
        $user = UserFactory::createColleague($account);

        $token = $tokenService->createToken(
            $user,
            Token::TYPE_REACTIVATE_USER,
            [
                'account_auth_methods' => [User::AUTH_METHOD_SMS_OTP, User::AUTH_METHOD_TOTP],
                'user_auth_method' => User::AUTH_METHOD_SMS_OTP
            ]
        );

        $url = $account->route(RouteName::AUTH_REACTIVATION_VERIFY_TOKEN, ['token' => $token->token]);
        $response = $this->get($url);
        $response->assertOk();

        $this->assertNull($response->json('masterkey_sent_date'));
    }

    public function testVerifyTokenWithoutMasterkeyFailure(): void
    {
        // create manager who should receive mail
        $manager = UserFactory::createManager();
        $account = $manager->account;

        $url = $account->route(RouteName::AUTH_REACTIVATION_VERIFY_TOKEN, ['token' => random_string(20)]);
        $response = $this->get($url);
        $response->assertNotFound();
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testVerifyTokenWithMasterkeySuccess(): void
    {
        Mail::fake();
        // create manager who should receive mail
        $manager = UserFactory::createManager();
        $account = $manager->account;
        $tokenService = resolve(TokenService::class);
        $user = UserFactory::createColleague($account);
        $password = 'Test123!';
        $masterkey = $user->renewMasterkey($password);
        $user->sendMasterkey($masterkey);
        $user->save();

        $token = $tokenService->createToken(
            $user,
            Token::TYPE_REACTIVATE_USER,
            [
                'account_auth_methods' => [User::AUTH_METHOD_SMS_OTP, User::AUTH_METHOD_TOTP],
                'user_auth_method' => User::AUTH_METHOD_SMS_OTP
            ]
        );

        $url = $account->route(RouteName::AUTH_REACTIVATION_VERIFY_TOKEN, ['token' => $token->token]);
        $response = $this->get($url);
        $response->assertOk();

        $this->assertEquals(date('d-m-Y'), $response->json('masterkey_sent_date'));
    }
}
