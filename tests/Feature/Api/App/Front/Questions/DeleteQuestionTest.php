<?php

namespace Tests\Feature\Api\App\Front\Questions;

use Symfony\Component\HttpFoundation\Response;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\OpenQuestions\QuestionTypes\ClientQuestionFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class DeleteQuestionTest extends TestCase
{
    public const ENDPOINT = 'api.app.front.questions.delete';

    public function testColleague(): void
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);

        $this->post($account->route(self::ENDPOINT))->assertForbidden();
    }

    public function testDelete(): void
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $user = UserFactory::createClientUser($account);
        $accountService = AccountServiceFactory::createManualQuestions($account);
        CompanyUserFactory::create($company, $user);
        $this->be($user);

        $question1 = ClientQuestionFactory::create($account, $company, $accountService);
        $question2 = ClientQuestionFactory::create($account, $company, $accountService);
        $question3 = ClientQuestionFactory::create($account, $company, $accountService);

        $response = $this->post($account->route(self::ENDPOINT), [
            'questionIds' => [$question1->open_question_id, $question2->open_question_id]
        ]);

        $response->assertOk();

        // These should be deleted
        $question1->refresh();
        $this->assertEquals(OpenQuestion::STATUS_DELETED, $question1->openQuestion->status);
        $question2->refresh();
        $this->assertEquals(OpenQuestion::STATUS_DELETED, $question2->openQuestion->status);

        // This one should still be open
        $question3->refresh();
        $this->assertEquals(OpenQuestion::STATUS_OPEN, $question3->openQuestion->status);
    }
}
