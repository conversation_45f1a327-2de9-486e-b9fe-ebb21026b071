<?php

namespace Tests\Feature\Api\App\Front\Questions;

use Symfony\Component\HttpFoundation\Response;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\OpenQuestions\OpenQuestionFactory;
use Tests\Support\Factories\OpenQuestions\QuestionTypes\ClientQuestionFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class ApproveQuestionTest extends TestCase
{
    public const ENDPOINT = 'api.app.front.questions.approve';

    public function testColleague(): void
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);

        $this->post($account->route(self::ENDPOINT))->assertForbidden();
    }

    public function testWrongStatusCompleted(): void
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $user = UserFactory::createClientUser($account);
        $accountService = AccountServiceFactory::createManualQuestions($account);
        CompanyUserFactory::create($company, $user);
        $this->be($user);

        $openQuestion = OpenQuestionFactory::create($company, $accountService, null, OpenQuestionCategory::CLIENT);
        $openQuestion->status = OpenQuestion::STATUS_COMPLETED;
        $openQuestion->save();
        ClientQuestionFactory::create($account, $company, $accountService, $openQuestion);

        $response = $this->post($account->route(self::ENDPOINT), [
            'questionIds' => [
                $openQuestion->id,
            ]
        ]);

        $response->assertStatus(ResponseAlias::HTTP_PRECONDITION_FAILED);
    }

    public function testWrongStatusDeleted(): void
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $user = UserFactory::createClientUser($account);
        $accountService = AccountServiceFactory::createManualQuestions($account);
        CompanyUserFactory::create($company, $user);
        $this->be($user);

        $openQuestion = OpenQuestionFactory::create($company, $accountService, null, OpenQuestionCategory::CLIENT);
        $openQuestion->status = OpenQuestion::STATUS_DELETED;
        $openQuestion->save();
        ClientQuestionFactory::create($account, $company, $accountService, $openQuestion);

        $response = $this->post($account->route(self::ENDPOINT), [
            'questionIds' => [
                $openQuestion->id,
            ]
        ]);

        $response->assertStatus(ResponseAlias::HTTP_PRECONDITION_FAILED);
    }

    public function testApprove(): void
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $user = UserFactory::createClientUser($account);
        $accountService = AccountServiceFactory::createManualQuestions($account);
        CompanyUserFactory::create($company, $user);
        $this->be($user);

        $openQuestion1 = OpenQuestionFactory::create($company, $accountService, null, OpenQuestionCategory::CLIENT);
        $openQuestion1->status = OpenQuestion::STATUS_OPEN;
        $openQuestion1->save();
        ClientQuestionFactory::create($account, $company, $accountService, $openQuestion1);

        $openQuestion2 = OpenQuestionFactory::create($company, $accountService, null, OpenQuestionCategory::CLIENT);
        $openQuestion2->status = OpenQuestion::STATUS_PENDING;
        $openQuestion2->save();
        ClientQuestionFactory::create($account, $company, $accountService, $openQuestion2);

        $response = $this->post($account->route(self::ENDPOINT), [
            'questionIds' => [
                $openQuestion1->id,
                $openQuestion2->id,
            ]
        ]);

        $response->assertOk();

        // These should be deleted
        $openQuestion1->refresh();
        $this->assertEquals(OpenQuestion::STATUS_COMPLETED, $openQuestion1->status);
        $openQuestion2->refresh();
        $this->assertEquals(OpenQuestion::STATUS_COMPLETED, $openQuestion2->status);
    }
}
