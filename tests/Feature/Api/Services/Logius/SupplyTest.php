<?php

namespace Tests\Feature\Api\Services\Logius;

use App\Models\TaskFile;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class SupplyTest extends TestCase
{
    public function testWithoutManualTasksService()
    {
        $account = AccountFactory::create();
        $xbrl = $this->asset('logius/xbrl/NT17/IHZ-2022/VB-01_bd-rpt-ihz-aangifte-2022.xbrl');
        $data = [
            'files' => [
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($xbrl),
                    'type' => 'declaration'
                ],
            ]
        ];
        $token = $account->createToken('api-token', ['*'])->plainTextToken;
        $response = $this->post(
            $account->route('api.v1.services.logius.supply'),
            $data,
            ['Authorization' => 'Bearer ' . $token]
        );
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }

    public function testWithoutBearerToken()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualTasks($account);
        $user = UserFactory::createManager($account);
        $this->be($user);
        $xbrl = $this->asset('logius/xbrl/NT17/IHZ-2022/VB-01_bd-rpt-ihz-aangifte-2022.xbrl');
        $data = [
            'files' => [
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($xbrl),
                    'type' => 'declaration'
                ],
            ]
        ];
        $response = $this->post(
            $account->route('api.v1.services.logius.supply'),
            $data,
        );
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testEmptyFile()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualTasks($account);
        $data = [
            'files' => [
                [
                    'filename' => 'declaration.xbrl',
                    'content' => '',
                    'type' => 'declaration'
                ],
            ]
        ];
        $token = $account->createToken('api-token', ['*'])->plainTextToken;
        $response = $this->post(
            $account->route('api.v1.services.logius.supply'),
            $data,
            ['Authorization' => 'Bearer ' . $token]
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
    }

    public function testLogiusErrorDeclaration()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualTasks($account);
        $xbrl = $this->asset('logius/xbrl/NT17/IHZ-2022/VB-01_bd-rpt-ihz-aangifte-2022.xbrl');
        $data = [
            'files' => [
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($xbrl),
                    'type' => 'declaration'
                ],
            ]
        ];
        $token = $account->createToken('api-token', ['*'])->plainTextToken;
        $response = $this->post(
            $account->route('api.v1.services.logius.supply'),
            $data,
            ['Authorization' => 'Bearer ' . $token]
        );

        $this->assertEquals(Response::HTTP_BAD_GATEWAY, $response->getStatusCode());
    }

    public function testIncompleteYearwork()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualTasks($account);
        $pubDoc = $this->asset('logius/xbrl/yearwork-2019.xbrl');
        $auditReport = $this->asset('logius/xbrl/audit_reports/nba-beoordelingsverklaring-goedkeurend-NL.xbrl');
        $data = [
            'files' => [
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($pubDoc),
                    'type' => TaskFile::TYPE_PUBLICATION_DOCUMENT
                ],
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($auditReport),
                    'type' => TaskFile::TYPE_AUDIT_REPORT
                ]
            ]
        ];
        $token = $account->createToken('api-token', ['*'])->plainTextToken;
        $response = $this->post(
            $account->route('api.v1.services.logius.supply'),
            $data,
            ['Authorization' => 'Bearer ' . $token]
        );
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }

    public function testLogiusErrorYearwork()
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualTasks($account);
        $pubDoc = $this->asset('logius/xbrl/yearwork-2019.xbrl');
        $auditReport = $this->asset('logius/xbrl/audit_reports/nba-beoordelingsverklaring-goedkeurend-NL.xbrl');
        $signature = $this->asset('logius/xml/yearwork_signature.xml');
        $data = [
            'files' => [
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($pubDoc),
                    'type' => TaskFile::TYPE_PUBLICATION_DOCUMENT
                ],
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($auditReport),
                    'type' => TaskFile::TYPE_AUDIT_REPORT
                ],
                [
                    'filename' => 'declaration.xbrl',
                    'content' => base64_encode($signature),
                    'type' => TaskFile::TYPE_SIGNATURE_XML
                ]
            ]
        ];
        $token = $account->createToken('api-token', ['*'])->plainTextToken;
        $response = $this->post(
            $account->route('api.v1.services.logius.supply'),
            $data,
            ['Authorization' => 'Bearer ' . $token]
        );
        $this->assertEquals(Response::HTTP_BAD_GATEWAY, $response->getStatusCode());
    }
}