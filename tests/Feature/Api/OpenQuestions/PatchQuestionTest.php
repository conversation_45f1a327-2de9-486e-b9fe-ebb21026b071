<?php

namespace Tests\Feature\Api\OpenQuestions;

use App\Models\OpenQuestions\Questions\OpenQuestion;
use Tests\Support\Builders\OpenQuestionBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class PatchQuestionTest extends TestCase
{
    private const ENDPOINT = 'api.v1.open_questions.patch';

    public function testWithoutValidToken(): void
    {
        $account = AccountFactory::create();
        $headers = ['Authorization' => 'Bearer 123|2323'];
        $response = $this->patch($account->route(self::ENDPOINT, ['open_question_id' => 3]), $headers);
        $response->assertUnauthorized();
    }

    public function testWithoutToken(): void
    {
        $account = AccountFactory::create();
        $response = $this->patch($account->route(self::ENDPOINT, ['open_question_id' => 3]));
        $response->assertUnauthorized();
    }

    public function testWithUserFromDifferentAccount(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);
        $account2 = AccountFactory::create();
        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account2);
        $company = CompanyFactory::create($account2);
        $openQuestion = OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        $response = $this->patch($account2->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]));
        $response->assertForbidden();
    }

    public function testWithUserNotInCompany(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);
        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);
        $company = CompanyFactory::create($account);
        $openQuestion = OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        $response = $this->patch($account->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]));
        $response->assertForbidden();
    }

    public function testWithUserSanctumToken(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);
        $company = CompanyFactory::create($account);

        $openQuestion = OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        $this->assertEquals(OpenQuestion::STATUS_OPEN, $openQuestion->status);
        $response = $this->patch(
            $account->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]),
            [OpenQuestion::STATUS => OpenQuestion::STATUS_ON_HOLD],
            ['Authorization' => 'Bearer ' . $account->createToken('api-test')->plainTextToken]
        );
        $response->assertOk();

        $data = $response->json();
        $this->assertNotEmpty($data);
        $this->assertEquals(OpenQuestion::STATUS_ON_HOLD, $data[OpenQuestion::STATUS]);
    }

    public function testCorrectData(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createManager($account);
        $this->be($currentUser);

        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);

        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $currentUser);

        $openQuestion = OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        $this->assertEquals(OpenQuestion::STATUS_OPEN, $openQuestion->status);
        $response = $this->patch(
            $account->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]),
            [OpenQuestion::STATUS => OpenQuestion::STATUS_ON_HOLD]
        );
        $response->assertOk();

        $data = $response->json();
        $this->assertNotEmpty($data);
        $this->assertEquals(OpenQuestion::STATUS_ON_HOLD, $data[OpenQuestion::STATUS]);
    }

    public function testWithWrongStatus(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);

        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $currentUser);

        $openQuestion = OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        $this->assertEquals('open', $openQuestion->status);
        $response = $this->patch(
            $account->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]),
            [OpenQuestion::STATUS => OpenQuestion::STATUS_PENDING]
        );
        $response->assertUnprocessable();
    }

    public function testWrongChange(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);

        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $currentUser);

        $openQuestion = OpenQuestionBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(openQuestion::STATUS_COMPLETED)
            ->build();

        $response = $this->patch(
            $account->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]),
            [OpenQuestion::TITLE => 'TEST']
        );

        $response->assertBadRequest();
    }

    public function testQuestionNotFound(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);

        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);
        $company = CompanyFactory::create($account);
        CompanyUserFactory::create($company, $currentUser);

        $openQuestion = OpenQuestionBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(openQuestion::STATUS_COMPLETED)
            ->build();

        $response = $this->patch(
            $account->route(self::ENDPOINT, ['open_question_id' => $openQuestion->id]),
            [OpenQuestion::TITLE => 'TEST']
        );

        $response->assertBadRequest();
    }

    public function testnonExistingOpenQuestionId(): void
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);

        $response = $this->patch(
            $account->route(self::ENDPOINT, ['open_question_id' => 0]),
        );

        $response->assertNotFound();
    }
}
