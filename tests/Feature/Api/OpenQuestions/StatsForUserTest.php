<?php

namespace Tests\Feature\Api\OpenQuestions;

use App\Http\Responses\Response;
use Tests\Support\Builders\OpenQuestionBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\OpenQuestions\NotifierUserSettingFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class StatsForUserTest extends TestCase
{
    private const ENDPOINT = 'api.v1.open_questions.stats_for_user';

    public function testWithoutValidToken()
    {
        $account = AccountFactory::create();
        $headers = ['Authorization' => 'Bearer 123|2323'];
        $response = $this->get($account->route(self::ENDPOINT), $headers);
        $response->assertUnauthorized();
    }

    public function testWithoutToken()
    {
        $account = AccountFactory::create();
        $response = $this->get($account->route(self::ENDPOINT));
        $response->assertUnauthorized();
    }

    public function testWithoutOpenQuestionService()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);

        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $currentUser->id);
        $response->assertStatus(Response::HTTP_PRECONDITION_FAILED);
    }

    public function testWithUserFromDifferentAccount()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);
        $this->be($currentUser);

        $account2 = AccountFactory::create();
        $userToGet = UserFactory::createColleague($account2);

        AccountServiceFactory::createBaseconeOpenQuestions($account);

        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id);
        $response->assertForbidden();
    }

    public function testWithUserNotManagedByLoggedInUser()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);

        $userToGet = UserFactory::createColleague($account);

        AccountServiceFactory::createBaseconeOpenQuestions($account);

        $headers = ['Authorization' => 'Bearer ' . $currentUser->createToken('user-token')->plainTextToken];
        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id, $headers);
        $response->assertForbidden();
    }

    public function testWithUserIdAndAuthId()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createColleague($account);

        $userToGet = UserFactory::createColleague($account);

        AccountServiceFactory::createBaseconeOpenQuestions($account);

        $headers = ['Authorization' => 'Bearer ' . $currentUser->createToken('user-token')->plainTextToken];
        $route = $account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id . '&auth_id=' . $userToGet->auth_id;

        $response = $this->get($route, $headers);
        $response->assertStatus(Response::HTTP_BAD_REQUEST);
    }

    public function testWithUserSanctumToken()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createManager($account);

        $userToGet = UserFactory::createColleague($account);

        AccountServiceFactory::createBaseconeOpenQuestions($account);

        $headers = [
            'Authorization' => 'Bearer ' . $currentUser->createToken('user-token', ['bizcuit'])->plainTextToken
        ];
        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id, $headers);
        $response->assertOk();
        $response->assertJsonStructure(['open_questions_total_count', 'open_questions_urgent_count']);
    }

    public function testWithUserSanctumTokenWithWrongAbility()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createManager($account);

        $userToGet = UserFactory::createColleague($account);

        AccountServiceFactory::createBaseconeOpenQuestions($account);

        $headers = ['Authorization' => 'Bearer ' . $currentUser->createToken('user-token', ['secure-share'])->plainTextToken];
        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id, $headers);
        $response->assertForbidden();
    }

    public function testWithAccountSanctumToken()
    {
        $account = AccountFactory::create();
        $userToGet = UserFactory::createColleague($account);

        AccountServiceFactory::createBaseconeOpenQuestions($account);

        $headers = ['Authorization' => 'Bearer ' . $account->createToken('api-test')->plainTextToken];
        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id, $headers);
        $response->assertOk();
        $response->assertJsonStructure(['open_questions_total_count', 'open_questions_urgent_count']);
    }

    public function testCorrectData()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createManager($account);
        $this->be($currentUser);

        $userToGet = UserFactory::createColleague($account);
        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);

        $company = CompanyFactory::create($account);
        $companyUser = CompanyUserFactory::create($company, $userToGet);
        NotifierUserSettingFactory::create($companyUser);

        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->setUrgent(true)->build();

        $response = $this->get($account->route(self::ENDPOINT) . '?user_id=' . $userToGet->id);
        $response->assertOk();
        $response->assertJsonStructure(['open_questions_total_count', 'open_questions_urgent_count', 'url_to_answer']);
        $this->assertEquals(4, $response->json('open_questions_total_count'));
        $this->assertEquals(1, $response->json('open_questions_urgent_count'));
    }

    public function testCorrectDataWithAuthId()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createManager($account);
        $this->be($currentUser);

        $userToGet = UserFactory::createColleague($account);
        $accountService = AccountServiceFactory::createBaseconeOpenQuestions($account);

        $company = CompanyFactory::create($account);
        $companyUser = CompanyUserFactory::create($company, $userToGet);
        NotifierUserSettingFactory::create($companyUser);

        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->build();
        OpenQuestionBuilder::new()->setAccountService($accountService)->setCompany($company)->setUrgent(true)->build();

        $response = $this->get($account->route(self::ENDPOINT) . '?auth_id=' . $userToGet->auth_id);
        $response->assertOk();
        $response->assertJsonStructure(['open_questions_total_count', 'open_questions_urgent_count', 'url_to_answer']);
        $this->assertEquals(3, $response->json('open_questions_total_count'));
        $this->assertEquals(1, $response->json('open_questions_urgent_count'));
    }
}
