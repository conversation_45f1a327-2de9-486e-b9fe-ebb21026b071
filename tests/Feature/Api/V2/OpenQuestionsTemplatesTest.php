<?php

namespace Tests\Feature\Api\V2;

use App\Factories\Models\OpenQuestions\Questions\Templates\TemplateFieldFactory;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\OpenQuestions\Questions\Templates\TemplateFieldType;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\Template\TemplateFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class OpenQuestionsTemplatesTest extends TestCase
{
    public function testLoadSchemaUnauthorized(): void
    {
        $account = AccountFactory::create();
        $url = $account->route('api.v2.templates.load', ['id' => 1]);
        $this->getJson($url)->assertUnauthorized();
    }

    public function testLoadSchema(): void
    {
        $user = UserFactory::createCompanyManager();
        $account = $user->account;
        $this->be($user);

        $template = TemplateFactory::create($account, fieldTypes: []);

        $field1 = TemplateFieldFactory::createNew(
            $user,
            'delta',
            TemplateFieldType::SECTION,
            3,
            true,
            'delta_key',
            [
                'name' => 'Delta',
                'type' => 'group',
                'builder' => [
                    'type' => 'container',
                    'label' => 'Delta container'
                ],
                'conditions' => [
                    [
                        'checkbox',
                        '==',
                        true
                    ]
                ]
            ]
        );
        $field1->template()->associate($template);
        $field1->save();

        $account = AccountFactory::create();
        $url = $account->route('api.v2.templates.load', ['id' => $template->id]);
        $json = json_decode($this->getJson($url)->getContent(), true);
        $this->assertCount(4, $json);
        $this->assertArrayHasKey('id', $json);
        $this->assertArrayHasKey('name', $json);
        $this->assertArrayHasKey('category', $json);
        $this->assertArrayHasKey('form', $json);
        $this->assertArrayHasKey('delta_key', $json['form']['schema']);
    }

    public function testStoreNewSchema(): void
    {
        $user = UserFactory::createCompanyManager();
        $account = $user->account;
        $this->be($user);

        $form = $this->asset('OpenQuestionTemplates/sample.json');
        $title = 'My latest form';
        $category = 'bookkeeping';

        $json = [
            'id' => null,
            'name' => $title,
            'category' => $category,
            'form' => json_decode($form)
        ];

        $url = $account->route('api.v2.templates.store');
        $response = $this->postJson($url, $json);
        $response->assertOk();
        $responseJson = json_decode($response->getContent(), true);

        $this->assertGreaterThanOrEqual(1, $responseJson['data']['id']);

        $template = Template::findOrFail($responseJson['data']['id']);

        $this->assertEquals($user->id, $template->created_by);
        $this->assertEquals($title, $template->name);
        $this->assertEquals($category, $template->category);

        $this->assertCount(11, $template->fields);

        $url = $account->route('api.v2.templates.load', ['id' => $template->id]);
        $json = json_decode($this->getJson($url)->getContent(), true);
        $this->assertCount(7, $json['form']['schema']); // is less than number of fields because some are within section
    }
}
