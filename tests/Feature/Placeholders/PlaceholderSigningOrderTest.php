<?php

namespace Tests\Feature\Placeholders;

use App\Models\CommunicationChannels\CommunicationChannel;
use App\Models\TaskFile\Placeholder;
use App\ServiceTask;
use App\ValueObject\Placeholder\Stamp\InputTextPlaceholder;
use Tests\Support\Builders\CommunicationChannels\AccountCommunicationChannelBuilder;
use Tests\Support\Builders\CommunicationChannels\UserCommunicationChannelPreferenceBuilder;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class PlaceholderSigningOrderTest extends TestCase
{
    private const ENDPOINT = 'new.task_file_placeholders.check_order';

    public function testWithNoOrderNorPostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 1
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $this->assertEmpty($response->json('data'));
    }

    public function testWithSameOrderAndOnePostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);
        $clientUser3 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);
        CompanyUserFactory::create($task->company, $clientUser3);

        $accountCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser2)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser3->id,
                    Placeholder::SIGNING_ORDER => 1
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $data = $response->json('data');
        $this->assertEquals(2, $data[0][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(1, $data[1][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(2, $data[2][Placeholder::SIGNING_ORDER]);
    }

    public function testWithSameOrderAndAllPostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);
        $clientUser3 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);
        CompanyUserFactory::create($task->company, $clientUser3);

        $accountCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser2)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser3)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser3->id,
                    Placeholder::SIGNING_ORDER => 1
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $data = $response->json('data');
        $this->assertEquals(3, $data[0][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(2, $data[1][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(1, $data[2][Placeholder::SIGNING_ORDER]);
    }

    public function testWithDifferentOrderAndOnePostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);
        $clientUser3 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);
        CompanyUserFactory::create($task->company, $clientUser3);

        $accountCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser2)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => true,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 2
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser3->id,
                    Placeholder::SIGNING_ORDER => 3
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $data = $response->json('data');
        $this->assertEquals(2, $data[0][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(1, $data[1][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(4, $data[2][Placeholder::SIGNING_ORDER]);
    }

    public function testWithDifferentOrderAndAllPostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);
        $clientUser3 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);
        CompanyUserFactory::create($task->company, $clientUser3);

        $accountCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser2)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser3)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => true,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 2
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser3->id,
                    Placeholder::SIGNING_ORDER => 3
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $this->assertEmpty($response->json('data'));
    }

    public function testWithOneTimeSignerNoPostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => null,
                    Placeholder::SIGNING_ORDER => 1,
                    'one_time_signer_id' => 'one_time_signer_1'
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $this->assertEmpty($response->json('data'));
    }

    public function testWithOneTimeSignerAndPostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setIsDefault(true)
            ->build();
        $emailCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::EMAIL)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser)
            ->setAccountCommunicationChannel($emailCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => null,
                    Placeholder::SIGNING_ORDER => 1,
                    'one_time_signer_id' => 'one_time_signer_1'
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $data = $response->json('data');
        $this->assertEquals(1, $data[0][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(2, $data[1][Placeholder::SIGNING_ORDER]);
    }

    public function testWithDifferentOrderPostbodeOrderCorrect(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);
        $clientUser3 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);
        CompanyUserFactory::create($task->company, $clientUser3);

        $accountCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser2)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => true,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 2
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser3->id,
                    Placeholder::SIGNING_ORDER => 2
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $this->assertEmpty($response->json('data'));
    }

    public function testWithMultiplePlaceholderForSameClientPostbode(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);
        $clientUser2 = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);
        CompanyUserFactory::create($task->company, $clientUser2);

        $accountCommunicationChannel = AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->build();
        UserCommunicationChannelPreferenceBuilder::new()
            ->setUser($clientUser2)
            ->setAccountCommunicationChannel($accountCommunicationChannel)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 2
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser2->id,
                    Placeholder::SIGNING_ORDER => 3
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $data = $response->json('data');
        $this->assertEquals(3, $data[0][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(1, $data[1][Placeholder::SIGNING_ORDER]);
        $this->assertEquals(1, $data[2][Placeholder::SIGNING_ORDER]);
    }

    public function testWithColleaguesOnly(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $colleague = UserFactory::createColleague($account);
        $colleague2 = UserFactory::createColleague($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $colleague);
        CompanyUserFactory::create($task->company, $colleague2);

        AccountCommunicationChannelBuilder::new()
            ->setAccount($account)
            ->setChannel(CommunicationChannel::POSTBODE)
            ->setSubject(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setIsDefault(true)
            ->build();

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $colleague->id,
                    Placeholder::SIGNING_ORDER => 1
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $colleague2->id,
                    Placeholder::SIGNING_ORDER => 1
                ]
            ]
        ];

        $response = $this->post($account->route(self::ENDPOINT, [$task->id, $taskFile->id]), $payload)->assertOk();
        $this->assertEmpty($response->json('data'));
    }
}
