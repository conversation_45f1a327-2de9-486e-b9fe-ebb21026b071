<?php

namespace Tests\Unit\ValueObject\AlureOnPremise;

use App\Models\TaskFile;
use App\ValueObject\AlureOnPremise\DocListItem;
use App\ValueObject\Services\Other\AlureOnPremiseDocs\Configuration;
use Tests\Support\UnitTestCase;

class DocListItemTest extends UnitTestCase
{
    public function testItem()
    {
        $data = [
            DocListItem::KEY_RELNR => '1234',
            DocListItem::KEY_SUBTITLE => 'subtitle test text',
            DocListItem::KEY_PATH => 'T:\\Documents and Settings\\Flamingo\\Untitled.pdf',
            DocListItem::KEY_UUID => 'ABC5-6784-DEF4-5675',
            DocListItem::KEY_VOLGNR => '678',
            DocListItem::KEY_DOCNR => '123'
        ];

        $config = new Configuration('T:\\', '\\\\Test\\');
        $item = new DocListItem($data, $config);

        $this->assertEquals(TaskFile::EXTENSION_PDF, $item->getExtension());
        $this->assertTrue($item->typeIsSupported());
        $this->assertEquals('b40b6b5ea91b1e6f18fbf7000a7d4395', $item->getPathChecksum());
        $instruction = $item->getInstruction();
        $this->assertEquals('b40b6b5ea91b1e6f18fbf7000a7d4395', $instruction['id']);
        $this->assertEquals('get-file', $instruction['action']);
        $this->assertEquals('\\\\Test\\Documents and Settings\\Flamingo\\Untitled.pdf', $instruction['params']['path']);
        $this->assertEquals($data[DocListItem::KEY_RELNR], $instruction['meta']['relnr']);
        $this->assertEquals($data[DocListItem::KEY_SUBTITLE], $instruction['meta']['subtitle']);
        $this->assertEquals($data[DocListItem::KEY_UUID], $instruction['meta']['uuid']);
        $this->assertEquals($data[DocListItem::KEY_VOLGNR], $instruction['meta']['volgnr']);
        $this->assertEquals($data[DocListItem::KEY_DOCNR], $instruction['meta']['docnr']);
    }

    public function testReplacePrefix()
    {
        $replaced = DocListItem::replacePrefix('T:\\Folder\\File.pdf', 'T:\\', '\\\\Test\\');
        $this->assertEquals('\\\\Test\\Folder\\File.pdf', $replaced);
    }

    public function testReplacePrefixNoMatch()
    {
        $replaced = DocListItem::replacePrefix('T:\\Folder\\File.pdf', 'X:\\', '\\\\Test\\');
        $this->assertEquals('T:\\Folder\\File.pdf', $replaced);
    }
}
