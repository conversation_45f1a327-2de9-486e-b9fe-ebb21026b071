<?php

namespace Tests\Unit\ValueObject\Placeholder\Signing;

use App\Models\TaskFile\Placeholder;
use App\Services\Pdf\PdfSigningService;
use App\ValueObject\Placeholder\Signing\ImagePlaceholder;
use Tests\Support\UnitTestCase;

class ImagePlaceholderTest extends UnitTestCase
{
    public function testAspectRatio()
    {
        require_once('vendor-local/setasign/SetaPDF/Autoload.php');

        $content = $this->asset('pdf/document/laboratory report.pdf');
        $service = new PdfSigningService();
        $service->setContent($content);
        $doc = $service->getDocument();

        $ph = new Placeholder();
        $ph->left = 15;
        $ph->right = 60;
        $ph->top = 3;
        $ph->bottom = 80;
        $ph->page = 1;
        $ph->filled_data = base64_encode($this->asset('images/signature.png'));
        $ph->status = Placeholder::STATUS_FILLED;
        $ph->type = Placeholder::TYPE_IMAGE_SIGNATURE;

        $visualPlaceholder = $service->setPlaceholder($ph);
        $xObject = $visualPlaceholder->getPdfObject($doc);

        $this->assertTrue($visualPlaceholder instanceof ImagePlaceholder);
        $this->assertEquals(148.819, $visualPlaceholder->getWidth());
        $this->assertEquals(57.047283333333326, $visualPlaceholder->getHeight());
        $this->assertEquals(0.8267722222222221, $visualPlaceholder->ratio);
        $this->assertEquals(155376, $visualPlaceholder->getSize());

        $this->assertEquals(155.6545230743827, $xObject->getWidth());
        $this->assertEquals(63.88280640771604, $xObject->getHeight());
    }
}
