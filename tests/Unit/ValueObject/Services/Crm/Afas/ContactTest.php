<?php

namespace Tests\Unit\ValueObject\Services\Crm\Afas;

use App\Services\Service\Crm\AbstractCrmService;
use App\ValueObject\Services\Crm\Afas\Contact;
use Tests\Support\UnitTestCase;

class ContactTest extends UnitTestCase
{
    public function testAuthId()
    {
        $record = new Contact(
            id: 123456,
            name: '<PERSON><PERSON>',
            address: '<PERSON><PERSON><PERSON>jnplein',
            fiscalNumber: 4567890,
            kvkNumber: 23456789,
            contactType: 'TEST',
            externalId: 'EXTERNALID',
            person: 'PERSON',
            function: 'FUNCTION',
            mobileWork: '0612345678',
            mobilePrivate: '06876543210',
            emailWork: '<EMAIL>',
            emailPrivate: '<EMAIL>',
            firstName: 'Piet',
            middleName: 'Peter',
            lastName: 'Pepernoot',
            bsnNumber: 666666666,
            clientIB: 6666666,
            vatNumber: 4444444,
            properties: [
                AbstractCrmService::PROPERTY_AUTH_ID_STRATEGY => AbstractCrmService::AUTH_ID_STRATEGY_NAME
            ]
        );

        $this->assertEquals('pietpepernoot', $record->authId());
    }
}
