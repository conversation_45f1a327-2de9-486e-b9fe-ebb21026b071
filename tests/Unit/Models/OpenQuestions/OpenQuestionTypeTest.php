<?php

namespace Tests\Unit\Models\OpenQuestions;

use Tests\Support\UnitTestCase;
use App\Models\OpenQuestions\OpenQuestionType;

class OpenQuestionTypeTest extends UnitTestCase
{
    public function testPropertyEnabled()
    {
        $type = new OpenQuestionType();
        $this->assertFalse($type->hasProperty(OpenQuestionType::PROPERTY_ENABLED));
        $this->assertTrue($type->isEnabled());
        $type->preferences = [OpenQuestionType::PROPERTY_ENABLED => true];
        $this->assertTrue($type->hasProperty(OpenQuestionType::PROPERTY_ENABLED));
        $this->assertTrue($type->isEnabled());
    }

    public function testPropertyFileUploadRequired()
    {
        $type = new OpenQuestionType();
        $this->assertFalse($type->hasProperty(OpenQuestionType::PROPERTY_FILE_UPLOAD_REQUIRED));
        $this->assertFalse($type->fileUploadRequired());
        $type->preferences = [OpenQuestionType::PROPERTY_FILE_UPLOAD_REQUIRED => true];
        $this->assertTrue($type->hasProperty(OpenQuestionType::PROPERTY_FILE_UPLOAD_REQUIRED));
        $this->assertTrue($type->fileUploadRequired());
    }
}