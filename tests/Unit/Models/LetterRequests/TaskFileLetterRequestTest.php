<?php

namespace Tests\Unit\Models\LetterRequests;

use App\Enums\LetterRequests\LetterRequestStatus;
use Tests\Support\Builders\LetterRequest\LetterRequestBuilder;
use Tests\Support\Builders\LetterRequest\TaskFileLetterRequestBuilder;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\TestCase;

class TaskFileLetterRequestTest extends TestCase
{
    public function testDelete()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createTwinfieldDeclarations($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setVat()
            ->build();

        $letterRequest = LetterRequestBuilder::new()
            ->setStatus(LetterRequestStatus::SENT)
            ->setAccount($account)
            ->build();
        $taskFileLetterRequest = TaskFileLetterRequestBuilder::new()
            ->setLetterRequest($letterRequest)
            ->setTaskFile($task->files[0])
            ->build();
        $taskFileLetterRequest->delete();

        $letterRequest->refresh();
        $this->assertModelMissing($taskFileLetterRequest);
        $this->assertNotNull($letterRequest->deleted_at);
    }
}
