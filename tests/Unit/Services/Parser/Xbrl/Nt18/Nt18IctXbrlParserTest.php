<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt18;

use App\Helpers\XmlDetectorHelper;
use App\Services\Parser\Xbrl\Nt18\Nt18IctXbrlParser;
use Tests\Support\UnitTestCase;

class Nt18IctXbrlParserTest extends UnitTestCase
{
    private ?string $xbrl;
    private ?string $xbrl1;

    protected function setUp(): void
    {
        $this->xbrl = $this->asset(
            'logius/xbrl/NT18/ICP-2024/VB-01_bd-rpt-icp-opgaaf-2024.xbrl'
        );
        $this->xbrl1 = $this->asset(
            'logius/xbrl/NT18/ICP-2024/VB-02_bd-rpt-icp-opgaaf-2024.xbrl'
        );
        parent::setUp();
    }

    protected function tearDown(): void
    {
        $this->xbrl = null;
        $this->xbrl1 = null;
        parent::tearDown();
    }

    public function testDetect()
    {
        $parser = XmlDetectorHelper::detectParser($this->xbrl);
        $this->assertInstanceOf(Nt18IctXbrlParser::class, $parser);
        $parser = XmlDetectorHelper::detectParser($this->xbrl1);
        $this->assertInstanceOf(Nt18IctXbrlParser::class, $parser);
    }

    public function testData()
    {
        $parser = XmlDetectorHelper::detectParser($this->xbrl);
        $data = $parser->getParsedResult();
        $this->assertEquals('001000159B06', $data['obnumber']);
        $this->assertEquals('001000299B01', $data['fiscal_entity_obnumber']);
        $this->assertEquals('2024-07-01', $data['date_start']);
        $this->assertEquals('2024-07-31', $data['date_end']);
        $this->assertEquals('2024', $data['year']);
        $this->assertEquals('A.A.N.', $data['contact_initials']);
        $this->assertEquals('van den', $data['contact_prefix']);
        $this->assertEquals('Leveraar', $data['contact_surname']);
        $this->assertEquals('EUR', $data['currency']);
        $this->assertEquals(2, count($data['summary']['intra-community-supplies']));
        $this->assertEquals(1, count($data['summary']['intra-community-supplies-correction']));
        $this->assertEquals(2, count($data['summary']['intra-community-services']));
        $this->assertEquals(2, count($data['summary']['intra-community-services-correction']));
        $this->assertEquals(3, count($data['summary']['intra-community-abc-supplies']));
        $this->assertEquals(3, count($data['summary']['intra-community-abc-supplies-correction']));
        $this->assertEquals(-1, $data['summary']['intra-community-abc-supplies'][1]['amount']);
        $this->assertEquals(-200, $data['summary']['intra-community-abc-supplies-correction'][0]['amount']);
    }

    public function testDataOtherFile()
    {
        $parser = XmlDetectorHelper::detectParser($this->xbrl1);
        $data = $parser->getParsedResult();
        $this->assertEquals('001004621B07', $data['obnumber']);
        $this->assertEquals('900000090B01', $data['fiscal_entity_obnumber']);
        $this->assertEquals('2024-12-01', $data['date_start']);
        $this->assertEquals('2024-12-31', $data['date_end']);
        $this->assertEquals('2024', $data['year']);
        $this->assertEquals('A.A.N.', $data['contact_initials']);
        $this->assertEquals('van den', $data['contact_prefix']);
        $this->assertEquals('Leveraar', $data['contact_surname']);
        $this->assertEquals('EUR', $data['currency']);
        $this->assertEquals(1, count($data['summary']['intra-community-supplies']));
        $this->assertEquals(2, count($data['summary']['intra-community-supplies-correction']));
        $this->assertEquals(0, count($data['summary']['intra-community-services']));
        $this->assertEquals(0, count($data['summary']['intra-community-services-correction']));
        $this->assertEquals(2, count($data['summary']['intra-community-abc-supplies']));
        $this->assertEquals(1, count($data['summary']['intra-community-abc-supplies-correction']));
        $this->assertEquals(200, $data['summary']['intra-community-abc-supplies'][1]['amount']);
        $this->assertEquals(-5999, $data['summary']['intra-community-abc-supplies-correction'][0]['amount']);
    }
}
