<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt14;

use App\Helpers\XmlDetectorHelper;
use Tests\Support\UnitTestCase;

class Nt14SbaVpbXbrlParserTest extends UnitTestCase
{
    public function testParse()
    {
        $xbrl = $this->asset('logius/xbrl/NT14/SBA-VPB-2019/VB-01_bd-rpt-vpb-sba-2019.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $data = $parser->getParsedResult();

        $this->assertEquals('2020-01-31', $data['NoticeOfAssessmentDate']);
        $this->assertEquals('http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-vpb-sba-2019.xsd', $data['taxonomy_url']);
        $this->assertEquals('2019', $data['year']);
        $this->assertEquals('2019-01-01', $data['date_start']);
        $this->assertEquals('2019-12-31', $data['date_end']);
    }
}
