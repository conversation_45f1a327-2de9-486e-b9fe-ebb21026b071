<?php

namespace Tests\Unit\Services\Parser\Xml;

use App\Helpers\XmlDetectorHelper;
use App\Services\Parser\Xml\GenericXmlParser;
use Tests\Support\UnitTestCase;

class GenericXmlParserTest extends UnitTestCase
{
    private ?string $xml;

    protected function setUp(): void
    {
        $this->xml = $this->asset(
            'Service/Nmbrs/SEPA_Salary_shannonsl_2020_R11.xml'
        );
        parent::setUp();
    }

    protected function tearDown(): void
    {
        $this->xml = null;
        parent::tearDown();
    }

    public function testDetectGenericXmlParser()
    {
        $parser = XmlDetectorHelper::detectParser($this->xml);
        $this->assertInstanceOf(GenericXmlParser::class, $parser);
    }
}