<?php

namespace Tests\Unit\Services\Gateway\Logius;

use App\CompanyIdentifier;
use App\Models\Authorizations\LogiusAuthorization;
use App\Models\Authorizations\LogiusAuthorizationType;
use App\Services\Gateway\Logius\LogiusDoMaConditionChecker;
use App\Services\Gateway\Logius\LogiusDoMaConditionParser;
use Tests\Support\UnitTestCase;

class LogiusDoMaConditionCheckerTest extends UnitTestCase
{
    use LogiusAccountCertTrait;

    public function testParsingProcessingResponse()
    {
        $identifier = new CompanyIdentifier();
        $identifier->identifier = '1234567B01';

        $this->addAccountCert(1, 'cert/sl-serial.pem');

        $request = new LogiusAuthorization();
        $request->account_id = 1;
        $request->type = LogiusAuthorizationType::TYPE_DOMA_IH;
        $request->companyIdentifier = $identifier;
        $sender = new LogiusDoMaConditionChecker($request);
        $response = $this->asset('logius/via-status-response1.soap.xml');
        $parser = new LogiusDoMaConditionParser($response);
        $parsed = $parser->response;

        $this->assertEquals('status', $parsed['type']);
        $this->assertEquals('In Behandeling', $parsed['status']);
        $this->assertEquals('2013-10-24T14:04:46.937Z', $parsed['date']);
        $this->assertEquals('S2S', $parsed['actor']);
    }

    public function testParsingFaultResponse()
    {
        $identifier = new CompanyIdentifier();
        $identifier->identifier = '1234567B01';
        $request = new LogiusAuthorization();
        $request->account_id = 1;
        $request->type = LogiusAuthorizationType::TYPE_DOMA_IH;
        $request->companyIdentifier = $identifier;

        $this->addAccountCert(1, 'cert/sl-serial.pem');
        $sender = new LogiusDoMaConditionChecker($request);
        $response = $this->asset('logius/via-status-response-fault.soap.xml');
        $parser = new LogiusDoMaConditionParser($response);
        $parsed = $parser->response;

        $this->assertEquals('error', $parsed['type']);
        $this->assertEquals('soapenv:Server', $parsed['faultcode']);
        $this->assertEquals('Algemene fout', $parsed['faultstring']);
        $this->assertEquals('MRS240', $parsed['detail']['code']);
        $this->assertGreaterThan(100, strlen($parsed['detail']['descr']));
    }
}
