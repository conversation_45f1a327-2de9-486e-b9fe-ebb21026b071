<?php

namespace Tests\Unit;

use App\AccountService;
use App\CompanyIdentifier;
use App\Service;
use App\Services\CompanyUserPermissionsService;
use App\ServiceTask;
use Tests\Support\Builders\AccountServiceBuilder;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceUploadTypeFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyIdentifierFactory;
use Tests\Support\Factories\CompanyUploadTypePermissionFactory;
use Tests\Support\Factories\CompanyUserPermissionFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\DefaultCompanyUserPermissionFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class PermissionsTest extends TestCase
{
    public function testAllUserPermission()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->setAllUserPermission('approve')
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        $this->be($internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $permissionService = resolve(CompanyUserPermissionsService::class);

        $this->assertEquals('approve', $permissionService->getForTaskAndUser($task, $companyUser->user));

        $props = $accountService->properties;
        $props[AccountService::PROPERTY_ALL_USERS_PERMISSION] = 'inform';

        $accountService->setPropertiesAttribute($props);
        $accountService->save();

        $this->assertEquals('inform', $permissionService->getForTaskAndUser($task->refresh(), $companyUser->user));
    }

    public function testDefaultCompanyUserPermission()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->setAllUserPermission('inform')
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        $this->be($internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);
        $client2 = UserFactory::createClientUser($account);
        $companyUser2 = CompanyUserFactory::create($company, $client2);

        $client3 = UserFactory::createClientUser($account); // this user should get all user permission
        $companyUser3 = CompanyUserFactory::create($company, $client3);

        $client4 = UserFactory::createClientUser($account);
        $companyUser4 = CompanyUserFactory::create($company, $client4);

        DefaultCompanyUserPermissionFactory::create($company, $companyUser, 'inform');
        DefaultCompanyUserPermissionFactory::create($company, $companyUser2, 'approve');
        DefaultCompanyUserPermissionFactory::create($company, $companyUser4, 'none');

        $permissionService = resolve(CompanyUserPermissionsService::class);

        $this->assertEquals('inform', $permissionService->getForTaskAndUser($task, $companyUser->user));
        $this->assertEquals('approve', $permissionService->getForTaskAndUser($task, $companyUser2->user));
        $this->assertEquals('inform', $permissionService->getForTaskAndUser($task, $companyUser3->user));
        $this->assertEquals('none', $permissionService->getForTaskAndUser($task, $companyUser4->user));
    }

    public function testUploadTypePermissions()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->setAllUserPermission('approve')
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        $this->be($internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);
        $client2 = UserFactory::createClientUser($account);
        $companyUser2 = CompanyUserFactory::create($company, $client2);

        $client3 = UserFactory::createClientUser($account); // this user should get all user permission
        $companyUser3 = CompanyUserFactory::create($company, $client3);

        $client4 = UserFactory::createClientUser($account);
        $companyUser4 = CompanyUserFactory::create($company, $client4);

        $uploadType = AccountServiceUploadTypeFactory::create('Document', $accountService);
        $task->refresh();
        $task->files[0]->account_service_upload_type_id = $uploadType->id;
        $task->push();

        CompanyUploadTypePermissionFactory::create($uploadType, $company, $companyUser, 'approve');
        CompanyUploadTypePermissionFactory::create($uploadType, $company, $companyUser2, 'inform');
        CompanyUploadTypePermissionFactory::create($uploadType, $company, $companyUser4, 'none');

        $permissionService = resolve(CompanyUserPermissionsService::class);

        $this->assertEquals('approve', $permissionService->getForTaskAndUser($task, $companyUser->user));
        $this->assertEquals('inform', $permissionService->getForTaskAndUser($task, $companyUser2->user));
        $this->assertEquals('approve', $permissionService->getForTaskAndUser($task, $companyUser3->user));
        $this->assertEquals('none', $permissionService->getForTaskAndUser($task, $companyUser4->user));
    }

    public function testIdentifierPermissions()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->setAllUserPermission('inform')
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();

        $internal = UserFactory::createColleague($account);
        $this->be($internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);
        $client2 = UserFactory::createClientUser($account);
        $companyUser2 = CompanyUserFactory::create($company, $client2);
        $client3 = UserFactory::createClientUser($account); // this user should get all user permission
        $companyUser3 = CompanyUserFactory::create($company, $client3);
        $client4 = UserFactory::createClientUser($account);
        $companyUser4 = CompanyUserFactory::create($company, $client4);

        $companyIdentifier = CompanyIdentifier::query()->where([
            'account_id' => $account->id,
            'identifier' => $task->getIdentifier(),
        ])->firstOrFail();

        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser, 'inform');
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser2, 'approve');
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser4, 'none');

        $permissionService = resolve(CompanyUserPermissionsService::class);

        $this->assertEquals('inform', $permissionService->getForTaskAndUser($task, $companyUser->user));
        $this->assertEquals('approve', $permissionService->getForTaskAndUser($task, $companyUser2->user));
        $this->assertEquals('inform', $permissionService->getForTaskAndUser($task, $companyUser3->user));
        $this->assertEquals('none', $permissionService->getForTaskAndUser($task, $companyUser4->user));
    }
}
