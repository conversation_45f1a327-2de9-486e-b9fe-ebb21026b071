<?php

namespace Tests\Unit\Objects;

use App\AccountService;
use App\Objects\PropertiesAwareInterface;
use PHPUnit\Framework\TestCase;

class PropertiesAwareTraitTest extends TestCase
{
    /** @var PropertiesAwareInterface */
    private $testedSubject;

    protected function setUp(): void
    {
        parent::setUp();

        $this->testedSubject = new AccountService();
        if (($this->testedSubject instanceof PropertiesAwareInterface) === false) {
            throw new \RuntimeException('These tests needs an instance of PropertiesAwareInterface class.');
        }
    }

    /**
     * These tests are testing both the setter and the getter of properties
     */
    public function testSettingAndGettingProperties()
    {
        $this->testedSubject->properties = $value = ['someArrayValue'];
        $this->assertEquals($this->testedSubject->properties, $value);

        $properties = [];
        $properties['someKey'] = 'someValue';
        $properties['anotherKey'] = 'anotherValue';
        $this->testedSubject->properties = $properties;

        $this->assertEquals(
            $this->testedSubject->properties,
            [
                'someKey' => 'someValue',
                'anotherKey' => 'anotherValue'
            ]
        );
    }
}