<?php

namespace Tests\Support\Factories;

use App\AccountService;
use App\AccountServiceCompany;
use App\Company;
use App\ServiceTask;
use DB;

class AccountServiceCompanyFactory
{
    public static function create(AccountService $accountService = null, Company $company = null): AccountServiceCompany
    {
        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        $id = DB::table('account_service_companies')
            ->insertGetId([
                 'account_id' => $accountService->account->id,
                 'account_service_id' => $accountService->id,
                 'company_id' => $company->id,
                 'external_id' => 'SomeRandomExternalId'
             ]);

        return AccountServiceCompany::find($id);
    }
}
