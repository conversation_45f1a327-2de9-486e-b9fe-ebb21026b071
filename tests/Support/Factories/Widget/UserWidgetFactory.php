<?php

namespace Tests\Support\Factories\Widget;

use App\Account;
use App\ContextWidget;
use App\User;
use App\UserWidget;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\UserFactory;

class UserWidgetFactory
{
    public static function create(
        Account $account = null,
        ContextWidget $contextWidget = null,
        User $user = null
    ): UserWidget {
        if ($account === null) {
            $account = AccountFactory::create();
        }

        if ($user === null) {
            $user = UserFactory::createColleague($account);
        }

        if ($contextWidget === null) {
            $contextWidget = ContextWidgetFactory::create($account);
        }

        return UserWidget::factory()->create(
            [
                'user_id' => $user->id,
                'parent_id' => $contextWidget->id,
                'generic_widget_id' => $contextWidget->generic_widget_id,
                'properties' => []
            ]
        );
    }
}
