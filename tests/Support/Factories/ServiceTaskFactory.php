<?php

namespace Tests\Support\Factories;

use App\AccountService;
use App\Company;
use App\CompanyIdentifier;
use App\Factories\TaskFileFactory;
use App\Models\ServiceTask\DividendTaxApprovalTask;
use App\Models\ServiceTask\DocumentApprovalTask;
use App\Models\ServiceTask\IctApprovalTask;
use App\Models\ServiceTask\IhzApprovalTask;
use App\Models\ServiceTask\SbaMessageTask;
use App\Models\ServiceTask\SbrYearworkTask;
use App\Models\ServiceTask\VpbApprovalTask;
use App\Models\ServiceTask\WageTaxApprovalTask;
use App\Models\ServiceTask\YearworkApprovalTask;
use App\Models\ServiceTask\VatApprovalTask;
use App\Models\TaskFile;
use App\Services\Declarations\DeclarationsService;
use App\ServiceTask;
use App\ValueObject\Declarations\DeclarationData;
use Tests\Support\Factories\TaskFiles\XbrlFactory;
use App\Services\ForecastTaskService;

class ServiceTaskFactory
{
    public static function createVatApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): VatApprovalTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xbrl/NT16/OB-2022/VB-01_bd-rpt-ob-aangifte-2022.xbrl'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'vat.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'VAT Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createIhzApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): IhzApprovalTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xbrl/NT17/IHZ-2022/VB-01_bd-rpt-ihz-aangifte-2022.xbrl'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'ihz.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'IHZ Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createVpbApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): VpbApprovalTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xbrl/NT17/VPB-2022/VB-01_bd-rpt-vpb-aangifte-2022.xbrl'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'vpb.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'VPB Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createIctApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): IctApprovalTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xbrl/twinfield-ict.xbrl.xml'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'ict.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'ICT Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createForecastTask(ServiceTask $task): ServiceTask
    {
        $forecastTaskService = resolve(ForecastTaskService::class);
        $forecasts = $forecastTaskService->createForecastTask($task);
        return $forecasts[0];
    }

    public static function createWageTax(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): WageTaxApprovalTask {
        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xml/nmbrs-wage_tax-data_from_prod.xml'));
        }

        $xml = TaskFileFactory::create($accountService, $content, 'wage-tax.xml');
        $data = new DeclarationData($xml, $xml->parsed_data);
        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createYearworkForMediumCompany(
        ?AccountService $accountService = null,
        ?Company $company = null,
        $withAuditReport = false
    ): ServiceTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        $path = base_path('tests/Support/assets/logius/xbrl/NT16/Jaarwerk/CompanySizes/NT16-yearwork-medium.xbrl');
        $content = file_get_contents($path);
        $auditReportPath = base_path('tests/Support/assets/logius/xbrl/accountantsverklaring.xbrl');
        $auditReportContent = file_get_contents($auditReportPath);
        $xbrl = TaskFileFactory::create($accountService, $content, 'yearwork-nt16-medium.xbrl');
        $data = new DeclarationData($xbrl, $xbrl->parsed_data);
        $declarationsService = resolve(DeclarationsService::class);
        $task = $declarationsService->generateTask($accountService, $data, $company);
        if ($withAuditReport) {
            $auditReport = TaskFileFactory::create($accountService, $auditReportContent, 'accountantsverklaring.xbrl');
            $auditReport->task()->associate($task);
            $auditReport->save();
        }

        return $task->refresh();
    }

    public static function createYearworkWithAdoptionDate(
        ?AccountService $accountService = null,
        ?Company $company = null
    ): ServiceTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        $path = 'tests/Support/assets/logius/xbrl/NT15/Jaarwerk/yearwork-micro-nt15.xbrl'; //phpcs:ignore
        $content = file_get_contents($path);
        $xbrl = TaskFileFactory::create($accountService, $content, 'yearwork-nt15-micro.xbrl');
        $data = new DeclarationData($xbrl, $xbrl->parsed_data);
        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createDocumentApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): DocumentApprovalTask {
        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(
                base_path(
                    'tests/Support/assets/pdf/declaration/Fiscale eenheid 1 - VPB-aangifte 2019 - Corporate Tax declaration.pdf'
                )
            );
        }

        $pdf = TaskFileFactory::create($accountService, $content, 'declaration.pdf', TaskFile::TYPE_ATTACHMENT);
        $parsedData = $pdf->parsed_data;
        $parsedData['type'] = ServiceTask::TYPE_DOCUMENT_APPROVAL;
        $data = new DeclarationData($pdf, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createManualForecast(
        CompanyIdentifier $companyIdentifier,
        AccountService $accountService
    ): ServiceTask {
        return ServiceTask::factory()->create(
            [
                ServiceTask::TITLE => 'title',
                ServiceTask::ACCOUNT_ID => $companyIdentifier->account_id,
                ServiceTask::ACCOUNT_SERVICE_ID => $accountService->id,
                ServiceTask::COMPANY_ID => $companyIdentifier->company_id,
                ServiceTask::STATUS => ServiceTask::STATUS_FORECAST,
                ServiceTask::TYPE => ServiceTask::TYPE_VAT_APPROVAL,
                ServiceTask::PROPERTIES => [
                    'identifier' => $companyIdentifier->identifier,
                    'identifier_type' => $companyIdentifier->type
                ]
            ]
        );
    }

    public static function createDocumentApprovalXml(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): DocumentApprovalTask
    {
        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(
                base_path(
                    'tests/Support/assets/Service/Nmbrs/SEPA_Salary_shannonsl_2020_R11.xml'
                )
            );
        }

        $xml = TaskFileFactory::create($accountService, $content, 'SEPA_Salary_shannonsl_2020_R11.xml');
        $parsedData = $xml->parsed_data;
        $data = new DeclarationData($xml, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createSbrYearworkApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): SbrYearworkTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/xbrl/FT17/frc-rpt-nt-sbr-jaarrekening-rechtspersoon-2022.xbrl'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'vpb.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'SBR Yearwork Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createSbaApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): SbaMessageTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xbrl/NT17/SBAZVW-2022/VB01_bd-rpt-zvw-sba-2022.xbrl'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'sba.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'SBA Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }

    public static function createDividendTaxApproval(
        ?AccountService $accountService = null,
        ?Company $company = null,
        ?string $content = null
    ): DividendTaxApprovalTask {

        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        if ($company === null) {
            $company = CompanyFactory::create($accountService->account);
        }

        if ($content === null) {
            $content = file_get_contents(base_path('tests/Support/assets/logius/xbrl/NT18/DIBAO/VB01-bd-rpt-div-aangifte.xbrl'));
        }

        $xbrl = TaskFileFactory::create($accountService, $content, 'sba.xbrl');

        $parsedData = $xbrl->parsed_data;
        $parsedData['title'] = 'Dividend Tax Task Title';
        $data = new DeclarationData($xbrl, $parsedData);

        $declarationsService = resolve(DeclarationsService::class);
        return $declarationsService->generateTask($accountService, $data, $company);
    }
}
