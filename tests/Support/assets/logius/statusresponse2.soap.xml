<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wsa="http://www.w3.org/2005/08/addressing">
  <soapenv:Header>
    <s:Security soapenv:mustUnderstand="1" xmlns:d="http://www.w3.org/2000/09/xmldsig#" xmlns:s="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:u="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:x="http://www.w3.org/2001/10/xml-exc-c14n#">
      <u:Timestamp u:Id="w_20">
        <u:Created>2020-03-06T12:02:53.823Z</u:Created>
        <u:Expires>2020-03-06T12:12:53.823Z</u:Expires>
      </u:Timestamp>
      <wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="SecurityToken-fcdb18ca-b16e-4ba3-ad03-262e1ec427cf" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">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</wsse:BinarySecurityToken>
      <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
        <SignedInfo>
          <CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
          <SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/>
          <Reference URI="#w_20">
            <Transforms>
              <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            </Transforms>
            <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
            <DigestValue>Tkg/GmIcx0Rc0hU9PsGcqge/P0A=</DigestValue>
          </Reference>
          <Reference URI="#w_21">
            <Transforms>
              <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            </Transforms>
            <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
            <DigestValue>FEEPN6eRa848m8Gs6rZ1+nlg5rA=</DigestValue>
          </Reference>
          <Reference URI="#w_22">
            <Transforms>
              <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            </Transforms>
            <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
            <DigestValue>MeTdvYjbprZa5FcEMPELm2O9POc=</DigestValue>
          </Reference>
          <Reference URI="#w_23">
            <Transforms>
              <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            </Transforms>
            <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
            <DigestValue>cIHLO9hO0JN/5NgHMO/JsYPryLY=</DigestValue>
          </Reference>
          <Reference URI="#w_24">
            <Transforms>
              <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            </Transforms>
            <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
            <DigestValue>VljU5K4UAgIdX4pBEoTXs2+vrkQ=</DigestValue>
          </Reference>
          <Reference URI="#w_25">
            <Transforms>
              <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            </Transforms>
            <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
            <DigestValue>ear52ZmDyAJVYoIbqbkmkiVaehk=</DigestValue>
          </Reference>
        </SignedInfo>
        <SignatureValue>EnTJADf4DaFq1R9tDbhblkBNzm4RyaDT5W0MV47xOgPyFq/Hnepyc/4Pji58TMGR5isgLYTdi+MvyhBElfDi8AKSMg++ZAw8xQRqfYOpKiNfbRO6kWWdOcBRPxykJsq2UYtMcHq3Z+vJ1h1On1+gQSn/l5oBqzUzBLGv9RM8BaOujjpd8KTk8XhSe/7oUrG6Jb21uZuyCnomtwZYUWMDk0TW53cBKyegLRklsx1nfhIc81TKUZlC9S0/K9+52TKSfQX3FlayqK2RNMGSYyZDSdkI0wfAs09bKamorqN+E6o2xUcGzkRym7Qzult8vAITsQBpty5ZMUutkTIKB9PchQ==</SignatureValue>
        <KeyInfo>
          <wsse:SecurityTokenReference xmlns="" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <wsse:Reference URI="#SecurityToken-fcdb18ca-b16e-4ba3-ad03-262e1ec427cf" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3"/>
          </wsse:SecurityTokenReference>
        </KeyInfo>
      </Signature>
    </s:Security>
    <wsa:To wsu:Id="w_21" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">http://www.w3.org/2005/08/addressing/anonymous</wsa:To>
    <wsa:MessageID wsu:Id="w_22" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">uuid:6e070ae7-9373-43f0-bdfc-bc917d23e547</wsa:MessageID>
    <wsa:Action wsu:Id="w_23" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">http://logius.nl/digipoort/wus/2.0/statusinformatieservice/1.2/StatusinformatieService/getStatussenProcesResponse</wsa:Action>
    <wsa:RelatesTo wsu:Id="w_24" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">uuid:f4db84bf-8b6f-4d02-7497-5c34412438ec</wsa:RelatesTo>
  </soapenv:Header>
  <soapenv:Body wsu:Id="w_25" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
    <getStatussenProcesResponse xmlns="http://logius.nl/digipoort/koppelvlakservices/1.2/">
      <getStatussenProcesReturn>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>500</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.000Z</tijdstempelStatus>
          <statusomschrijving>Validatie bij de uitvragende partij gelukt</statusomschrijving>
          <statusdetails>&lt;Identification&gt;&lt;MessageId&gt;5c2c6fdc-5ec7-11ea-9e8a-0ae01c630000&lt;/MessageId&gt;&lt;RefersToMessageId&gt;geen&lt;/RefersToMessageId&gt;&lt;MessageIdBd&gt;AA1E87660AE182B3BFB3CE5317FC30C5&lt;/MessageIdBd&gt;&lt;DatumTijdOntvangst&gt;2020-03-05T09:54:52.000Z&lt;/DatumTijdOntvangst&gt;&lt;ResponseType&gt;acknowledgement&lt;/ResponseType&gt;&lt;/Identification&gt;</statusdetails>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>105</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.127Z</tijdstempelStatus>
          <statusomschrijving>Aanleverproces gestart</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>100</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.135Z</tijdstempelStatus>
          <statusomschrijving>Aanleveren gelukt.</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>110</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.174Z</tijdstempelStatus>
          <statusomschrijving>Aanleverproces wordt aangeboden</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>200</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.197Z</tijdstempelStatus>
          <statusomschrijving>Authenticatie gelukt.</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>301</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.301Z</tijdstempelStatus>
          <statusomschrijving>Validatie gelukt.</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>301</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.305Z</tijdstempelStatus>
          <statusomschrijving>Validatie gelukt.</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>405</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.355Z</tijdstempelStatus>
          <statusomschrijving>Afleveren naar uitvragende partij bezig...</statusomschrijving>
        </StatusResultaat>
        <StatusResultaat>
          <kenmerk>0e0e0e39-3e7e-4046-a119-d98bf9b31599</kenmerk>
          <identiteitBelanghebbende>
            <nummer>811372376B01</nummer>
            <type>BTW</type>
          </identiteitBelanghebbende>
          <statuscode>400</statuscode>
          <tijdstempelStatus>2020-03-05T09:54:52.421Z</tijdstempelStatus>
          <statusomschrijving>Afleveren uitvragende partij gelukt.</statusomschrijving>
        </StatusResultaat>
      </getStatussenProcesReturn>
    </getStatussenProcesResponse>
  </soapenv:Body>
</soapenv:Envelope>