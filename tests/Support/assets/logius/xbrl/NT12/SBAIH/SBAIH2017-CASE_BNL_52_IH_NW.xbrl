<?xml version="1.0" encoding="UTF-8"?>
<!-- Testcase CASE_BNL_52_IH_NW, stroom MEDIH2017-XBRL, based on Taxonomy NT12 created by B/CA-CCT (DIT IS EEN TESTBERICHT) -->
<!-- Situatie: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ge, samen<PERSON><PERSON> met fipa en kind, deel jaar eigen woning, gezamenlijke aangifte. Inkomen uit ter beschikkingstelling alsmede inkomen uit EW. Tevens tijdelijke verhuur en aftrek geen of geringe eigen woningschuld. De AHK wordt berekend en is niet geheel te gebruiken. Bij de Persoonsgebonden aftrek komt een lijfrenteaftrek voor met gebruik van jaarruimte 2016 en 2015. 
<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>'s wordt geen IACK berekend omdat er geen werkzaamheden buitenshuis worden verricht door de aangever. Er is dus de mogelijkheid om zelf de opvang te verzorgen. -->
<!-- Created: 201709131911 -->
<!-- Verwacht resultaat: Go<PERSON> -->
<xbrli:xbrl xmlns:bd-i="http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-data" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ih-sba-2017.xsd"/>
	<xbrli:context id="c1d1">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2017-01-01</xbrli:startDate>
			<xbrli:endDate>2017-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="PURE">
		<xbrli:measure>xbrli:pure</xbrli:measure>
	</xbrli:unit>
	<bd-i:ContributionBaseIncome contextRef="c1d1" decimals="INF" unitRef="EUR">4352</bd-i:ContributionBaseIncome>
	<bd-i:DateOfBirth contextRef="c1d1">1985-09-09</bd-i:DateOfBirth>
	<bd-i:FiscalYear contextRef="c1d1">2017</bd-i:FiscalYear>
	<bd-i:GeneralTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1590</bd-i:GeneralTaxCreditCalculated>
	<bd-i:IncomeTaxBracket1 contextRef="c1d1" decimals="INF" unitRef="EUR">387</bd-i:IncomeTaxBracket1>
	<bd-i:NoticeOfAssessmentDate contextRef="c1d1">2018-07-20</bd-i:NoticeOfAssessmentDate>
	<bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2018-08-25</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
	<bd-i:NoticeOfAssessmentNumber contextRef="c1d1">2621.71.387.H.76.01</bd-i:NoticeOfAssessmentNumber>
	<bd-i:ObjectionDeadlineDate contextRef="c1d1">2018-08-31</bd-i:ObjectionDeadlineDate>
	<bd-i:OverallIncome contextRef="c1d1" decimals="INF" unitRef="EUR">5185</bd-i:OverallIncome>
	<bd-i:PersonDependentRemainderDeductionSettledWithIncomeBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">11542</bd-i:PersonDependentRemainderDeductionSettledWithIncomeBox1>
	<bd-i:SocialInsuranceContributionBalance contextRef="c1d1" decimals="INF" unitRef="EUR">1203</bd-i:SocialInsuranceContributionBalance>
	<bd-i:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">27.650</bd-i:SocialInsuranceRatePercentage>
	<bd-i:TaxAppropriation contextRef="c1d1">H</bd-i:TaxAppropriation>
	<bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">0</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
	<bd-i:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">387</bd-i:TaxBox1>
	<bd-i:TaxBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">249</bd-i:TaxBox3>
	<bd-i:TaxConsultantNameAddress1 contextRef="c1d1">J. de Vries</bd-i:TaxConsultantNameAddress1>
	<bd-i:TaxConsultantNameAddress2 contextRef="c1d1">Dennenlaan 3</bd-i:TaxConsultantNameAddress2>
	<bd-i:TaxConsultantNameAddress3 contextRef="c1d1">Wittem</bd-i:TaxConsultantNameAddress3>
	<bd-i:TaxConsultantNameAddress4 contextRef="c1d1">7924BR</bd-i:TaxConsultantNameAddress4>
	<bd-i:TaxReturnMessageType contextRef="c1d1">62</bd-i:TaxReturnMessageType>
	<bd-i:TaxableIncomeBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">29050</bd-i:TaxableIncomeBox3>
	<bd-i:TaxableIncomeEmploymentOwnerOccupiedHouse contextRef="c1d1" decimals="INF" unitRef="EUR">15894</bd-i:TaxableIncomeEmploymentOwnerOccupiedHouse>
	<bd-i:TaxpayerNameAddress1 contextRef="c1d1">K.H. Spijker</bd-i:TaxpayerNameAddress1>
	<bd-i:TaxpayerNameAddress2 contextRef="c1d1">zandweg6</bd-i:TaxpayerNameAddress2>
	<bd-i:TaxpayerNameAddress3 contextRef="c1d1">nergenshuizen</bd-i:TaxpayerNameAddress3>
	<bd-i:TaxpayerNameAddress4 contextRef="c1d1">1234aa</bd-i:TaxpayerNameAddress4>
	<bd-i:WithholdingTaxBox3AbroadSettled contextRef="c1d1" decimals="INF" unitRef="EUR">249</bd-i:WithholdingTaxBox3AbroadSettled>
</xbrli:xbrl>
