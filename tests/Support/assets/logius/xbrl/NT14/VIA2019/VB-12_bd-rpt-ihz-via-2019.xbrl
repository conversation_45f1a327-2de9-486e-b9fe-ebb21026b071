<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-ihz-via-2019.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Aanvulling -->
<!-- Created: 2020-02-14T14:00:00 -->
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt14/bd/20191211/validation/bd-axes" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-domain-members" xmlns:bd-i="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data" xmlns:bd-t="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-tuples" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
    <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-ihz-via-2019.xsd"/>
    <xbrli:context id="Context_Duration">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:startDate>2019-01-01</xbrli:startDate>
            <xbrli:endDate>2019-12-31</xbrli:endDate>
        </xbrli:period>
    </xbrli:context>
    <xbrli:context id="Context_Instant_Begin">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:instant>2019-01-01</xbrli:instant>
        </xbrli:period>
        <xbrli:scenario>
            <xbrldi:explicitMember dimension="bd-dim-dim:TimeDimension">bd-dim-mem:Begin</xbrldi:explicitMember>
        </xbrli:scenario>
    </xbrli:context>
    <xbrli:context id="Context_Instant_End">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:instant>2019-12-31</xbrli:instant>
        </xbrli:period>
        <xbrli:scenario>
            <xbrldi:explicitMember dimension="bd-dim-dim:TimeDimension">bd-dim-mem:End</xbrldi:explicitMember>
        </xbrli:scenario>
    </xbrli:context>
    <xbrli:unit id="DKK">
        <xbrli:measure>iso4217:DKK</xbrli:measure>
    </xbrli:unit>
    <xbrli:unit id="EUR">
        <xbrli:measure>iso4217:EUR</xbrli:measure>
    </xbrli:unit>
    <xbrli:unit id="PURE">
        <xbrli:measure>xbrli:pure</xbrli:measure>
    </xbrli:unit>
    <bd-i:DateOfBirth contextRef="Context_Duration">1978-10-15</bd-i:DateOfBirth>
    <bd-i:IncomeDependentCombinationTaxCreditChildIdentificationNumber contextRef="Context_Duration">*********</bd-i:IncomeDependentCombinationTaxCreditChildIdentificationNumber>
    <bd-i:IncomeDependentCombinationTaxCreditExists contextRef="Context_Duration">true</bd-i:IncomeDependentCombinationTaxCreditExists>
    <bd-i:PrefilledTaxReturnIncomeUseWatermark contextRef="Context_Duration">ff371089393d473c9d2fcef950514c49</bd-i:PrefilledTaxReturnIncomeUseWatermark>
    <bd-i:TaxpayerName contextRef="Context_Duration">DV62</bd-i:TaxpayerName>
    <bd-t:AssetsOnBankAccountsSpecification>
        <bd-i:AssetsOnBankAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankAccountsAccountHolderIdentificationNumber>
        <bd-i:AssetsOnBankAccountsBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">85365</bd-i:AssetsOnBankAccountsBalance>
        <bd-i:AssetsOnBankAccountsBalanceOriginalCurrency contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">85365</bd-i:AssetsOnBankAccountsBalanceOriginalCurrency>
        <bd-i:AssetsOnBankAccountsBalanceType contextRef="Context_Duration">29</bd-i:AssetsOnBankAccountsBalanceType>
        <bd-i:AssetsOnBankAccountsBankAccountNumber contextRef="Context_Duration">853</bd-i:AssetsOnBankAccountsBankAccountNumber>
        <bd-i:AssetsOnBankAccountsBankProductLabel contextRef="Context_Duration">bezitstroom</bd-i:AssetsOnBankAccountsBankProductLabel>
        <bd-i:AssetsOnBankAccountsCommonInterestIndication contextRef="Context_Duration">01</bd-i:AssetsOnBankAccountsCommonInterestIndication>
        <bd-i:AssetsOnBankAccountsProductID contextRef="Context_Duration">100074</bd-i:AssetsOnBankAccountsProductID>
        <bd-i:AssetsOnBankAccountsRecordType contextRef="Context_Duration">06</bd-i:AssetsOnBankAccountsRecordType>
        <bd-i:AssetsOnBankAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankAccountsSourceBankTaxReferenceNumber>
    </bd-t:AssetsOnBankAccountsSpecification>
    <bd-t:AssetsOnStockAccountsSpecification>
        <bd-i:AssetsOnStockAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnStockAccountsAccountHolderIdentificationNumber>
        <bd-i:AssetsOnStockAccountsBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">10500</bd-i:AssetsOnStockAccountsBalance>
        <bd-i:AssetsOnStockAccountsBalanceBenefit contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">10500</bd-i:AssetsOnStockAccountsBalanceBenefit>
        <bd-i:AssetsOnStockAccountsBalanceBox3BankAccountSavings contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">10500</bd-i:AssetsOnStockAccountsBalanceBox3BankAccountSavings>
        <bd-i:AssetsOnStockAccountsBalanceOriginalCurrency contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">10500</bd-i:AssetsOnStockAccountsBalanceOriginalCurrency>
        <bd-i:AssetsOnStockAccountsBalanceType contextRef="Context_Duration">41</bd-i:AssetsOnStockAccountsBalanceType>
        <bd-i:AssetsOnStockAccountsBankAccountNumber contextRef="Context_Duration">******************</bd-i:AssetsOnStockAccountsBankAccountNumber>
        <bd-i:AssetsOnStockAccountsBankProductLabel contextRef="Context_Duration">RABO INTERNET</bd-i:AssetsOnStockAccountsBankProductLabel>
        <bd-i:AssetsOnStockAccountsCommonInterestIndication contextRef="Context_Duration">01</bd-i:AssetsOnStockAccountsCommonInterestIndication>
        <bd-i:AssetsOnStockAccountsProductID contextRef="Context_Duration">31</bd-i:AssetsOnStockAccountsProductID>
        <bd-i:AssetsOnStockAccountsRecordType contextRef="Context_Duration">07</bd-i:AssetsOnStockAccountsRecordType>
        <bd-i:AssetsOnStockAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:AssetsOnStockAccountsSourceBankTaxReferenceNumber>
    </bd-t:AssetsOnStockAccountsSpecification>
    <bd-t:DebtAccountsSpecification>
        <bd-i:DebtAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:DebtAccountsAccountHolderIdentificationNumber>
        <bd-i:DebtAccountsBalanceType contextRef="Context_Duration">29</bd-i:DebtAccountsBalanceType>
        <bd-i:DebtAccountsBankAccountNumber contextRef="Context_Duration">777777</bd-i:DebtAccountsBankAccountNumber>
        <bd-i:DebtAccountsBankProductLabel contextRef="Context_Duration">lengtenetok</bd-i:DebtAccountsBankProductLabel>
        <bd-i:DebtAccountsCommonInterestIndication contextRef="Context_Duration">01</bd-i:DebtAccountsCommonInterestIndication>
        <bd-i:DebtAccountsLoanStartAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">5000</bd-i:DebtAccountsLoanStartAmount>
        <bd-i:DebtAccountsMortgageIndication contextRef="Context_Duration">true</bd-i:DebtAccountsMortgageIndication>
        <bd-i:DebtAccountsRecordType contextRef="Context_Duration">06</bd-i:DebtAccountsRecordType>
        <bd-i:DebtAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:DebtAccountsSourceBankTaxReferenceNumber>
        <bd-i:DebtAccountsSourceProductID contextRef="Context_Duration">100055</bd-i:DebtAccountsSourceProductID>
        <bd-t:DebtInterestSpecification>
            <bd-i:DebtInterestAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">1313</bd-i:DebtInterestAmount>
            <bd-i:DebtInterestAmountOriginalCurrency contextRef="Context_Duration" decimals="INF" unitRef="DKK">999999</bd-i:DebtInterestAmountOriginalCurrency>
            <bd-i:DebtInterestBalanceType contextRef="Context_Duration">21</bd-i:DebtInterestBalanceType>
        </bd-t:DebtInterestSpecification>
        <bd-t:DebtPositionSpecification>
            <bd-i:DebtPositionBankAccountBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">*********</bd-i:DebtPositionBankAccountBalance>
            <bd-i:DebtPositionBankAccountBalance contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">*********</bd-i:DebtPositionBankAccountBalance>
            <bd-i:DebtPositionBankAccountBalanceOriginalCurrency contextRef="Context_Instant_Begin" decimals="INF" unitRef="DKK">*********</bd-i:DebtPositionBankAccountBalanceOriginalCurrency>
            <bd-i:DebtPositionBankAccountBalanceOriginalCurrency contextRef="Context_Instant_End" decimals="INF" unitRef="DKK">*********</bd-i:DebtPositionBankAccountBalanceOriginalCurrency>
        </bd-t:DebtPositionSpecification>
    </bd-t:DebtAccountsSpecification>
    <bd-t:OwnHouseSecondHomeImmovablePropertyOtherSpecification>
        <bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAddress contextRef="Context_Duration">8172EH63</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAddress>
        <bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAppurtenanceObjectNumber contextRef="Context_Duration" decimals="INF" unitRef="PURE">***********</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAppurtenanceObjectNumber>
        <bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawMunicipalityName contextRef="Context_Duration">Venlo</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawMunicipalityName>
        <bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawValue contextRef="Context_Duration" decimals="INF" unitRef="EUR">300000</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawValue>
        <bd-t:ResidenceSituationPeriodQualificationSpecification>
            <bd-i:ResidenceSituation contextRef="Context_Duration">02</bd-i:ResidenceSituation>
            <bd-i:ResidenceSituationEndDate contextRef="Context_Duration">2019-12-31</bd-i:ResidenceSituationEndDate>
            <bd-i:ResidenceSituationPreQualification contextRef="Context_Duration">04</bd-i:ResidenceSituationPreQualification>
            <bd-i:ResidenceSituationStartDate contextRef="Context_Duration">2019-01-01</bd-i:ResidenceSituationStartDate>
        </bd-t:ResidenceSituationPeriodQualificationSpecification>
    </bd-t:OwnHouseSecondHomeImmovablePropertyOtherSpecification>
    <bd-t:PresentWorkSpecification>
        <bd-i:PresentWorkEmployedPersonsTaxCreditSettled contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PresentWorkEmployedPersonsTaxCreditSettled>
        <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="Context_Duration">DEN DONDER D</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
        <bd-i:PresentWorkHealthInsuranceLawCode contextRef="Context_Duration">K</bd-i:PresentWorkHealthInsuranceLawCode>
        <bd-i:PresentWorkHealthInsuranceLawContribution contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PresentWorkHealthInsuranceLawContribution>
        <bd-i:PresentWorkHealthInsuranceLawWithheld contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PresentWorkHealthInsuranceLawWithheld>
        <bd-i:PresentWorkIncomeRatioCode contextRef="Context_Duration">11</bd-i:PresentWorkIncomeRatioCode>
        <bd-i:PresentWorkLifecycleLeaveReductionApplied contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PresentWorkLifecycleLeaveReductionApplied>
        <bd-i:PresentWorkWages contextRef="Context_Duration" decimals="INF" unitRef="EUR">2000</bd-i:PresentWorkWages>
        <bd-i:PresentWorkWageTaxChartCode contextRef="Context_Duration">013</bd-i:PresentWorkWageTaxChartCode>
        <bd-i:PresentWorkWithheldWageLevy contextRef="Context_Duration" decimals="INF" unitRef="EUR">1000</bd-i:PresentWorkWithheldWageLevy>
    </bd-t:PresentWorkSpecification>
    <bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
        <bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate contextRef="Context_Duration">2019-12-31</bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate>
        <bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate contextRef="Context_Duration">2019-01-01</bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate>
        <bd-i:RelationshipTaxpayerDateOfBirth contextRef="Context_Duration">2014-03-24</bd-i:RelationshipTaxpayerDateOfBirth>
        <bd-i:RelationshipTaxpayerIdentificationNumber contextRef="Context_Duration">*********</bd-i:RelationshipTaxpayerIdentificationNumber>
        <bd-i:RelationshipTaxpayerRelationCode contextRef="Context_Duration">01</bd-i:RelationshipTaxpayerRelationCode>
        <bd-i:RelationshipTaxpayerSurname contextRef="Context_Duration">DV109</bd-i:RelationshipTaxpayerSurname>
    </bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
    <bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
        <bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate contextRef="Context_Duration">2019-12-31</bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate>
        <bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate contextRef="Context_Duration">2019-01-01</bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate>
        <bd-i:RelationshipTaxpayerDateOfBirth contextRef="Context_Duration">2014-03-24</bd-i:RelationshipTaxpayerDateOfBirth>
        <bd-i:RelationshipTaxpayerIdentificationNumber contextRef="Context_Duration">*********</bd-i:RelationshipTaxpayerIdentificationNumber>
        <bd-i:RelationshipTaxpayerRelationCode contextRef="Context_Duration">01</bd-i:RelationshipTaxpayerRelationCode>
        <bd-i:RelationshipTaxpayerSurname contextRef="Context_Duration">DV110</bd-i:RelationshipTaxpayerSurname>
    </bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
    <bd-t:TaxAssessmentProvisionalIncomeTaxDataSpecification>
        <bd-i:TaxAssessmentProvisionalNoticeOfAssessmentDate contextRef="Context_Duration">2019-07-04</bd-i:TaxAssessmentProvisionalNoticeOfAssessmentDate>
        <bd-i:TaxAssessmentProvisionalNoticeOfAssessmentNumber contextRef="Context_Duration">*********H9001</bd-i:TaxAssessmentProvisionalNoticeOfAssessmentNumber>
        <bd-i:TaxAssessmentProvisionalTaxAssessmentAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">-6262</bd-i:TaxAssessmentProvisionalTaxAssessmentAmount>
        <bd-i:TaxAssessmentProvisionalTaxIndication contextRef="Context_Duration">IBPV</bd-i:TaxAssessmentProvisionalTaxIndication>
    </bd-t:TaxAssessmentProvisionalIncomeTaxDataSpecification>
</xbrli:xbrl>
