<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt16/bd/20220921/bd-rpt-ih-sba-2023.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 26-07-2022 16:02:00 -->
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-i="http://www.nltaxonomie.nl/nt16/bd/20211208/dictionary/bd-data" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt16/bd/20220216/dictionary/bd-data-ext1" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
    <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt16/bd/20220921/entrypoints/bd-rpt-ih-sba-2023.xsd"/>
    <xbrli:context id="c1d1">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:startDate>2023-01-01</xbrli:startDate>
            <xbrli:endDate>2023-12-31</xbrli:endDate>
        </xbrli:period>
    </xbrli:context>
    <xbrli:unit id="EUR">
        <xbrli:measure>iso4217:EUR</xbrli:measure>
    </xbrli:unit>
    <xbrli:unit id="PURE">
        <xbrli:measure>xbrli:pure</xbrli:measure>
    </xbrli:unit>
    <bd-i-ext1:ContributionBaseIncome contextRef="c1d1" decimals="INF" unitRef="EUR">33791</bd-i-ext1:ContributionBaseIncome>
    <bd-i-ext1:EmployedPersonsTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">2833</bd-i-ext1:EmployedPersonsTaxCreditCalculated>
    <bd-i-ext1:FiscalYear contextRef="c1d1">2023</bd-i-ext1:FiscalYear>
    <bd-i-ext1:GeneralTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1507</bd-i-ext1:GeneralTaxCreditCalculated>
    <bd-i-ext1:IncomeTaxBracket1 contextRef="c1d1" decimals="INF" unitRef="EUR">1778</bd-i-ext1:IncomeTaxBracket1>
    <bd-i-ext1:IncomeTaxBracket2 contextRef="c1d1" decimals="INF" unitRef="EUR">1815</bd-i-ext1:IncomeTaxBracket2>
    <bd-i-ext1:IncomeTaxBracket3 contextRef="c1d1" decimals="INF" unitRef="EUR">733</bd-i-ext1:IncomeTaxBracket3>
    <bd-i-ext1:ObjectionDeadlineDate contextRef="c1d1">2023-07-13</bd-i-ext1:ObjectionDeadlineDate>
    <bd-i-ext1:SocialInsuranceContributionBalance contextRef="c1d1" decimals="INF" unitRef="EUR">9343</bd-i-ext1:SocialInsuranceContributionBalance>
    <bd-i-ext1:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">27.650</bd-i-ext1:SocialInsuranceRatePercentage>
    <bd-i-ext1:TaxAppropriation contextRef="c1d1">H</bd-i-ext1:TaxAppropriation>
    <bd-i-ext1:TaxAssesmentAmountExclusiveOfTaxInterestCompensate contextRef="c1d1" decimals="INF" unitRef="EUR">-3478</bd-i-ext1:TaxAssesmentAmountExclusiveOfTaxInterestCompensate>
    <bd-i-ext1:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">13669</bd-i-ext1:TaxBox1>
    <bd-i-ext1:TaxConsultantNameAddress1 contextRef="c1d1">B. Eswaar</bd-i-ext1:TaxConsultantNameAddress1>
    <bd-i-ext1:TaxConsultantNameAddress2 contextRef="c1d1">Consu lentes 123</bd-i-ext1:TaxConsultantNameAddress2>
    <bd-i-ext1:TaxConsultantNameAddress3 contextRef="c1d1">Zuthpen</bd-i-ext1:TaxConsultantNameAddress3>
    <bd-i-ext1:TaxConsultantNameAddress4 contextRef="c1d1">7203BE</bd-i-ext1:TaxConsultantNameAddress4>
    <bd-i-ext1:TaxCreditsCombined contextRef="c1d1" decimals="INF" unitRef="EUR">4340</bd-i-ext1:TaxCreditsCombined>
    <bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse contextRef="c1d1" decimals="INF" unitRef="EUR">35590</bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse>
    <bd-i:DateOfBirth contextRef="c1d1">1976-07-00</bd-i:DateOfBirth>
    <bd-i:NoticeOfAssessmentDate contextRef="c1d1">2023-06-01</bd-i:NoticeOfAssessmentDate>
    <bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2023-09-13</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
    <bd-i:NoticeOfAssessmentNumber contextRef="c1d1">1111.11.134.H.23.01</bd-i:NoticeOfAssessmentNumber>
    <bd-i:OverallIncome contextRef="c1d1" decimals="INF" unitRef="EUR">35590</bd-i:OverallIncome>
    <bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">-3536</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
    <bd-i:TaxReturnMessageType contextRef="c1d1">20</bd-i:TaxReturnMessageType>
    <bd-i:TaxpayerNameAddress1 contextRef="c1d1">*********</bd-i:TaxpayerNameAddress1>
    <bd-i:TaxpayerNameAddress2 contextRef="c1d1">Kerkweg 23</bd-i:TaxpayerNameAddress2>
    <bd-i:TaxpayerNameAddress3 contextRef="c1d1">Vleuten</bd-i:TaxpayerNameAddress3>
    <bd-i:TaxpayerNameAddress4 contextRef="c1d1">3425JZ</bd-i:TaxpayerNameAddress4>
</xbrli:xbrl>
