<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt13/bd/20181212/entrypoints/bd-rpt-ih-sba-2018.xsd -->
<!--Intellectual Property State of the Netherlands -->
<!--Created on: Created on: 05-11-2018 11:00:00 -->
<xbrli:xbrl xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt13/bd/20181212/validation/bd-axes" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt13/bd/20181212/dictionary/bd-domain-members" xmlns:bd-i="http://www.nltaxonomie.nl/nt13/bd/20181212/dictionary/bd-data" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt13/bd/20181212/entrypoints/bd-rpt-ih-sba-2018.xsd"/>
	<xbrli:context id="c1d1">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:context id="c3d1dd">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
			<xbrldi:explicitMember dimension="bd-dim-dim:TaxpayerDimension">bd-dim-mem:Domestic</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="c4d1df">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
			<xbrldi:explicitMember dimension="bd-dim-dim:TaxpayerDimension">bd-dim-mem:Foreign</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="PURE">
		<xbrli:measure>xbrli:pure</xbrli:measure>
	</xbrli:unit>
	<bd-i:DateOfBirth contextRef="c1d1">1974-03-02</bd-i:DateOfBirth>
	<bd-i:FiscalYear contextRef="c1d1">2018</bd-i:FiscalYear>
	<bd-i:NoticeOfAssessmentDate contextRef="c1d1">2018-07-20</bd-i:NoticeOfAssessmentDate>
	<bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2018-08-25</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
	<bd-i:NoticeOfAssessmentNumber contextRef="c1d1">3421.12.430.H.86.01</bd-i:NoticeOfAssessmentNumber>
	<bd-i:ObjectionDeadlineDate contextRef="c1d1">2018-08-31</bd-i:ObjectionDeadlineDate>
	<bd-i:SavingsAndInvestmentsBaseBracket1 contextRef="c3d1dd" decimals="INF" unitRef="EUR">75000</bd-i:SavingsAndInvestmentsBaseBracket1>
	<bd-i:SavingsAndInvestmentsBaseBracket1 contextRef="c4d1df" decimals="INF" unitRef="EUR">75000</bd-i:SavingsAndInvestmentsBaseBracket1>
	<bd-i:SavingsAndInvestmentsBaseBracket2 contextRef="c3d1dd" decimals="INF" unitRef="EUR">70000</bd-i:SavingsAndInvestmentsBaseBracket2>
	<bd-i:SavingsAndInvestmentsBaseBracket2 contextRef="c4d1df" decimals="INF" unitRef="EUR">32000</bd-i:SavingsAndInvestmentsBaseBracket2>
	<bd-i:SavingsAndInvestmentsBaseYieldClass1Bracket1 contextRef="c4d1df" decimals="INF" unitRef="EUR">50250</bd-i:SavingsAndInvestmentsBaseYieldClass1Bracket1>
	<bd-i:SavingsAndInvestmentsBaseYieldClass1Bracket2 contextRef="c3d1dd" decimals="INF" unitRef="EUR">14700</bd-i:SavingsAndInvestmentsBaseYieldClass1Bracket2>
	<bd-i:SavingsAndInvestmentsBaseYieldClass1Bracket2 contextRef="c4d1df" decimals="INF" unitRef="EUR">6720</bd-i:SavingsAndInvestmentsBaseYieldClass1Bracket2>
	<bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket1 contextRef="c3d1dd" decimals="INF" unitRef="EUR">24750</bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket1>
	<bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket1 contextRef="c4d1df" decimals="INF" unitRef="EUR">24750</bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket1>
	<bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket2 contextRef="c3d1dd" decimals="INF" unitRef="EUR">55300</bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket2>
	<bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket2 contextRef="c4d1df" decimals="INF" unitRef="EUR">25280</bd-i:SavingsAndInvestmentsBaseYieldClass2Bracket2>
	<bd-i:SavingsAndInvestmentsBenefits contextRef="c4d1df" decimals="INF" unitRef="EUR">1262</bd-i:SavingsAndInvestmentsBenefits>
	<bd-i:SavingsAndInvestmentsBenefitsBracketRate contextRef="c3d1dd" decimals="INF" unitRef="EUR">5372</bd-i:SavingsAndInvestmentsBenefitsBracketRate>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket1 contextRef="c3d1dd" decimals="INF" unitRef="EUR">819</bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket1>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket1 contextRef="c4d1df" decimals="INF" unitRef="EUR">814</bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket1>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket2 contextRef="c3d1dd" decimals="INF" unitRef="EUR">2396</bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket2>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket2 contextRef="c4d1df" decimals="INF" unitRef="EUR">109</bd-i:SavingsAndInvestmentsBenefitsYieldClass1Bracket2>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket1 contextRef="c3d1dd" decimals="INF" unitRef="EUR">1334</bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket1>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket1 contextRef="c4d1df" decimals="INF" unitRef="EUR">1334</bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket1>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket2 contextRef="c3d1dd" decimals="INF" unitRef="EUR">2980</bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket2>
	<bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket2 contextRef="c4d1df" decimals="INF" unitRef="EUR">1362</bd-i:SavingsAndInvestmentsBenefitsYieldClass2Bracket2>
	<bd-i:SavingsAndInvestmentsTaxBaseYieldClass1TaxBracket1Percentage contextRef="c1d1" decimals="INF" unitRef="PURE">67.00</bd-i:SavingsAndInvestmentsTaxBaseYieldClass1TaxBracket1Percentage>
	<bd-i:SavingsAndInvestmentsTaxBaseYieldClass1TaxBracket2Percentage contextRef="c1d1" decimals="INF" unitRef="PURE">21.00</bd-i:SavingsAndInvestmentsTaxBaseYieldClass1TaxBracket2Percentage>
	<bd-i:SavingsAndInvestmentsTaxBaseYieldClass2TaxBracket1Percentage contextRef="c1d1" decimals="INF" unitRef="PURE">33.00</bd-i:SavingsAndInvestmentsTaxBaseYieldClass2TaxBracket1Percentage>
	<bd-i:SavingsAndInvestmentsTaxBaseYieldClass2TaxBracket2Percentage contextRef="c1d1" decimals="INF" unitRef="PURE">79.00</bd-i:SavingsAndInvestmentsTaxBaseYieldClass2TaxBracket2Percentage>
	<bd-i:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">27.650</bd-i:SocialInsuranceRatePercentage>
	<bd-i:TaxAppropriation contextRef="c1d1">H</bd-i:TaxAppropriation>
	<bd-i:TaxAssesmentAmountExclusiveOfTaxInterestCompensate contextRef="c1d1" decimals="INF" unitRef="EUR">860</bd-i:TaxAssesmentAmountExclusiveOfTaxInterestCompensate>
	<bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">894</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
	<bd-i:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">860</bd-i:TaxBox1>
	<bd-i:TaxReturnMessageType contextRef="c1d1">20</bd-i:TaxReturnMessageType>
	<bd-i:TaxpayerNameAddress1 contextRef="c1d1">M.I. Grant</bd-i:TaxpayerNameAddress1>
	<bd-i:TaxpayerNameAddress2 contextRef="c1d1">Grenzweg 23</bd-i:TaxpayerNameAddress2>
	<bd-i:TaxpayerNameAddress3 contextRef="c1d1">Nirgendwo</bd-i:TaxpayerNameAddress3>
	<bd-i:TaxpayerNameAddress4 contextRef="c1d1">D-23590</bd-i:TaxpayerNameAddress4>
</xbrli:xbrl>
