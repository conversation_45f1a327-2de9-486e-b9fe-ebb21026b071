<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt17/bd/20221207/entrypoints/bd-rpt-ihz-via-2022.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 03-10-2022 16:00:00 -->
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt17/bd/20221207/validation/bd-axes" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-domain-members" xmlns:bd-i="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-data" xmlns:bd-t="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-tuples" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt17/bd/20221207/entrypoints/bd-rpt-ihz-via-2022.xsd"/>
  <xbrli:context id="Context_Duration">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2022-01-01</xbrli:startDate>
      <xbrli:endDate>2022-12-31</xbrli:endDate>
    </xbrli:period>
  </xbrli:context>
  <xbrli:context id="Context_Instant_Begin">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:instant>2022-01-01</xbrli:instant>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:TimeDimension">bd-dim-mem:Begin</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="Context_Instant_End">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:instant>2022-12-31</xbrli:instant>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:TimeDimension">bd-dim-mem:End</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <bd-i:DateOfBirth contextRef="Context_Duration">1970-05-05</bd-i:DateOfBirth>
    <bd-i:PersonDependentDeductionRemainder contextRef="Context_Duration" decimals="INF" unitRef="EUR">100</bd-i:PersonDependentDeductionRemainder>
    <bd-i:PersonDependentDeductionRemainderPotential contextRef="Context_Duration">true</bd-i:PersonDependentDeductionRemainderPotential>
  <bd-i:PrefilledTaxReturnIncomeUseWatermark contextRef="Context_Duration">OLOgwFYN2OLOgwFYN2OLOgwFYN2OLOgw</bd-i:PrefilledTaxReturnIncomeUseWatermark>
  <bd-i:TaxpayerName contextRef="Context_Duration">A. Angever</bd-i:TaxpayerName>
  <bd-i:YoungDisabledPersonsTaxCreditRightExists contextRef="Context_Duration">true</bd-i:YoungDisabledPersonsTaxCreditRightExists>
  <bd-t:AssetsOnBankAccountsSpecification>
    <bd-i:AssetsOnBankAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankAccountsAccountHolderIdentificationNumber>
    <bd-i:AssetsOnBankAccountsBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">6341</bd-i:AssetsOnBankAccountsBalance>
    <bd-i:AssetsOnBankAccountsBalanceType contextRef="Context_Duration">01</bd-i:AssetsOnBankAccountsBalanceType>
    <bd-i:AssetsOnBankAccountsBankAccountNumber contextRef="Context_Duration">*********0*********01234</bd-i:AssetsOnBankAccountsBankAccountNumber>
    <bd-i:AssetsOnBankAccountsBankProductLabel contextRef="Context_Duration">BankLabel70</bd-i:AssetsOnBankAccountsBankProductLabel>
    <bd-i:AssetsOnBankAccountsCommonInterestIndication contextRef="Context_Duration">02</bd-i:AssetsOnBankAccountsCommonInterestIndication>
    <bd-i:AssetsOnBankAccountsProductID contextRef="Context_Duration">*********0*********0</bd-i:AssetsOnBankAccountsProductID>
    <bd-i:AssetsOnBankAccountsRecordType contextRef="Context_Duration">05</bd-i:AssetsOnBankAccountsRecordType>
    <bd-i:AssetsOnBankAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankAccountsSourceBankTaxReferenceNumber>
  </bd-t:AssetsOnBankAccountsSpecification>
  <bd-t:AssetsOnBankStockAccountsForeignSpecification>
    <bd-i:AssetsOnBankStockAccountsForeignAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankStockAccountsForeignAccountHolderIdentificationNumber>
    <bd-i:AssetsOnBankStockAccountsForeignBankAccountNumber contextRef="Context_Duration">***************</bd-i:AssetsOnBankStockAccountsForeignBankAccountNumber>
    <bd-i:AssetsOnBankStockAccountsForeignBankName contextRef="Context_Duration">Danske Bank</bd-i:AssetsOnBankStockAccountsForeignBankName>
    <bd-i:AssetsOnBankStockAccountsForeignCountryCode contextRef="Context_Duration">DNK</bd-i:AssetsOnBankStockAccountsForeignCountryCode>
  </bd-t:AssetsOnBankStockAccountsForeignSpecification>
  <bd-t:AssetsOnBankStockAccountsForeignSpecification>
    <bd-i:AssetsOnBankStockAccountsForeignAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankStockAccountsForeignAccountHolderIdentificationNumber>
    <bd-i:AssetsOnBankStockAccountsForeignBankAccountNumber contextRef="Context_Duration">ES15bank456461</bd-i:AssetsOnBankStockAccountsForeignBankAccountNumber>
    <bd-i:AssetsOnBankStockAccountsForeignBankName contextRef="Context_Duration">Espanol Bank</bd-i:AssetsOnBankStockAccountsForeignBankName>
    <bd-i:AssetsOnBankStockAccountsForeignCountryCode contextRef="Context_Duration">ESP</bd-i:AssetsOnBankStockAccountsForeignCountryCode>
    <bd-i:CommonReportingStandardCode contextRef="Context_Duration">502</bd-i:CommonReportingStandardCode>
  </bd-t:AssetsOnBankStockAccountsForeignSpecification>
  <bd-t:AssetsOnBankStockAccountsForeignSpecification>
    <bd-i:AssetsOnBankStockAccountsForeignAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnBankStockAccountsForeignAccountHolderIdentificationNumber>
    <bd-i:AssetsOnBankStockAccountsForeignBankAccountNumber contextRef="Context_Duration">FR15CL9873210</bd-i:AssetsOnBankStockAccountsForeignBankAccountNumber>
    <bd-i:AssetsOnBankStockAccountsForeignBankName contextRef="Context_Duration">Credit Lyonais</bd-i:AssetsOnBankStockAccountsForeignBankName>
    <bd-i:AssetsOnBankStockAccountsForeignCountryCode contextRef="Context_Duration">FRA</bd-i:AssetsOnBankStockAccountsForeignCountryCode>
    <bd-i:CommonReportingStandardCode contextRef="Context_Duration">501</bd-i:CommonReportingStandardCode>
  </bd-t:AssetsOnBankStockAccountsForeignSpecification>
  <bd-t:BenefitStaffLoanSpecification>
    <bd-i:BenefitStaffLoanAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">244</bd-i:BenefitStaffLoanAmount>
    <bd-i:BenefitStaffLoanEmployerOrBenefitsAgencyName contextRef="Context_Duration">BAAS</bd-i:BenefitStaffLoanEmployerOrBenefitsAgencyName>
    <bd-i:BenefitStaffLoanIncomeRatioNumber contextRef="Context_Duration">1234</bd-i:BenefitStaffLoanIncomeRatioNumber>
    <bd-i:BenefitStaffLoanWageTaxNumber contextRef="Context_Duration">*********L01</bd-i:BenefitStaffLoanWageTaxNumber>
  </bd-t:BenefitStaffLoanSpecification>
  <bd-t:ConstructionDepotSpecification>
    <bd-i:ConstructionDepotAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotAccountHolderIdentificationNumber>
    <bd-i:ConstructionDepotBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">45000</bd-i:ConstructionDepotBalance>
    <bd-i:ConstructionDepotBalance contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">42000</bd-i:ConstructionDepotBalance>
    <bd-i:ConstructionDepotBankAccountNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotBankAccountNumber>
    <bd-i:ConstructionDepotCommonInterestIndication contextRef="Context_Duration">01</bd-i:ConstructionDepotCommonInterestIndication>
    <bd-i:ConstructionDepotInterest contextRef="Context_Duration" decimals="INF" unitRef="EUR">1800</bd-i:ConstructionDepotInterest>
    <bd-i:ConstructionDepotLabel contextRef="Context_Duration">bouwdepot</bd-i:ConstructionDepotLabel>
    <bd-i:ConstructionDepotProductID contextRef="Context_Duration">ABCDEFGH</bd-i:ConstructionDepotProductID>
    <bd-i:ConstructionDepotSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotSourceBankTaxReferenceNumber>
  </bd-t:ConstructionDepotSpecification>
  <bd-t:ConstructionDepotSpecification>
    <bd-i:ConstructionDepotAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotAccountHolderIdentificationNumber>
    <bd-i:ConstructionDepotBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">8000</bd-i:ConstructionDepotBalance>
    <bd-i:ConstructionDepotBalance contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">75000</bd-i:ConstructionDepotBalance>
    <bd-i:ConstructionDepotBankAccountNumber contextRef="Context_Duration">1</bd-i:ConstructionDepotBankAccountNumber>
    <bd-i:ConstructionDepotCommonInterestIndication contextRef="Context_Duration">02</bd-i:ConstructionDepotCommonInterestIndication>
    <bd-i:ConstructionDepotInterest contextRef="Context_Duration" decimals="INF" unitRef="EUR">3000</bd-i:ConstructionDepotInterest>
    <bd-i:ConstructionDepotLabel contextRef="Context_Duration">max depot</bd-i:ConstructionDepotLabel>
    <bd-i:ConstructionDepotProductID contextRef="Context_Duration">ZYXWVUTS</bd-i:ConstructionDepotProductID>
    <bd-i:ConstructionDepotSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotSourceBankTaxReferenceNumber>
  </bd-t:ConstructionDepotSpecification>
  <bd-t:DividendSpecification>
    <bd-i:DividendAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:DividendAccountHolderIdentificationNumber>
    <bd-i:DividendBankAccountNumber contextRef="Context_Duration">123-9999</bd-i:DividendBankAccountNumber>
    <bd-i:DividendCommonInterestIndication contextRef="Context_Duration">01</bd-i:DividendCommonInterestIndication>
    <bd-i:DividendLabel contextRef="Context_Duration">PENSREK</bd-i:DividendLabel>
    <bd-t:DividendPerCountrySpecification>
      <bd-i:DividendRevenues contextRef="Context_Duration" decimals="INF" unitRef="EUR">3500</bd-i:DividendRevenues>
      <bd-i:DividendTaxAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">364</bd-i:DividendTaxAmount>
      <bd-i:DividendWithholdingTaxCountryCode contextRef="Context_Duration">LUX</bd-i:DividendWithholdingTaxCountryCode>
    </bd-t:DividendPerCountrySpecification>
    <bd-i:DividendProductID contextRef="Context_Duration">ProdID1</bd-i:DividendProductID>
    <bd-i:DividendSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:DividendSourceBankTaxReferenceNumber>
  </bd-t:DividendSpecification>
  <bd-t:PremiumDepotSpecification>
    <bd-i:PremiumDepotCode contextRef="Context_Duration">WDEP</bd-i:PremiumDepotCode>
    <bd-i:PremiumDepotInsurancePolicyNumberDepotNumber contextRef="Context_Duration">Pnr123456</bd-i:PremiumDepotInsurancePolicyNumberDepotNumber>
    <bd-i:PremiumDepotLabel contextRef="Context_Duration">Aegon</bd-i:PremiumDepotLabel>
    <bd-i:PremiumDepotProductID contextRef="Context_Duration">AegonPremieDepot001</bd-i:PremiumDepotProductID>
    <bd-i:PremiumDepotValue contextRef="Context_Duration" decimals="INF" unitRef="EUR">15002</bd-i:PremiumDepotValue>
  </bd-t:PremiumDepotSpecification>
  <bd-t:PresentWorkSpecification>
    <bd-i:PresentWorkEmployedPersonsTaxCreditSettled contextRef="Context_Duration" decimals="INF" unitRef="EUR">950</bd-i:PresentWorkEmployedPersonsTaxCreditSettled>
    <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="Context_Duration">Baas</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
    <bd-i:PresentWorkIncomeRatioCode contextRef="Context_Duration">44</bd-i:PresentWorkIncomeRatioCode>
    <bd-i:PresentWorkWages contextRef="Context_Duration" decimals="INF" unitRef="EUR">40000</bd-i:PresentWorkWages>
    <bd-i:PresentWorkWageTaxChartCode contextRef="Context_Duration">123</bd-i:PresentWorkWageTaxChartCode>
    <bd-i:PresentWorkWithheldWageLevy contextRef="Context_Duration" decimals="INF" unitRef="EUR">1200</bd-i:PresentWorkWithheldWageLevy>
    <bd-i:TravelAllowanceReceivedAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">240</bd-i:TravelAllowanceReceivedAmount>
  </bd-t:PresentWorkSpecification>
  <bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption contextRef="Context_Duration" decimals="INF" unitRef="EUR">4303</bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName contextRef="Context_Duration">Interpolis</bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxWageLevy contextRef="Context_Duration" decimals="INF" unitRef="EUR">75</bd-i:RedemptionAnnuitiesCoveredByWagetaxWageLevy>
  </bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
  <bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
    <bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate contextRef="Context_Duration">2022-12-31</bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate>
    <bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate contextRef="Context_Duration">2022-01-01</bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate>
    <bd-i:RelationshipTaxpayerDateOfBirth contextRef="Context_Duration">1972-06-06</bd-i:RelationshipTaxpayerDateOfBirth>
    <bd-i:RelationshipTaxpayerIdentificationNumber contextRef="Context_Duration">*********</bd-i:RelationshipTaxpayerIdentificationNumber>
    <bd-i:RelationshipTaxpayerRelationCode contextRef="Context_Duration">02</bd-i:RelationshipTaxpayerRelationCode>
    <bd-i:RelationshipTaxpayerSurname contextRef="Context_Duration">P. Artner</bd-i:RelationshipTaxpayerSurname>
  </bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
  <bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
    <bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate contextRef="Context_Duration">2022-12-31</bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate>
    <bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate contextRef="Context_Duration">2022-01-01</bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate>
    <bd-i:RelationshipTaxpayerDateOfBirth contextRef="Context_Duration">2022-09-09</bd-i:RelationshipTaxpayerDateOfBirth>
    <bd-i:RelationshipTaxpayerIdentificationNumber contextRef="Context_Duration">*********</bd-i:RelationshipTaxpayerIdentificationNumber>
    <bd-i:RelationshipTaxpayerRelationCode contextRef="Context_Duration">01</bd-i:RelationshipTaxpayerRelationCode>
    <bd-i:RelationshipTaxpayerSurname contextRef="Context_Duration">BeBe</bd-i:RelationshipTaxpayerSurname>
  </bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
  <bd-t:TaxAssessmentProvisionalIncomeTaxDataSpecification>
    <bd-i:TaxAssessmentProvisionalNoticeOfAssessmentDate contextRef="Context_Duration">2022-02-01</bd-i:TaxAssessmentProvisionalNoticeOfAssessmentDate>
    <bd-i:TaxAssessmentProvisionalNoticeOfAssessmentNumber contextRef="Context_Duration">*********H03</bd-i:TaxAssessmentProvisionalNoticeOfAssessmentNumber>
    <bd-i:TaxAssessmentProvisionalTaxAssessmentAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">2</bd-i:TaxAssessmentProvisionalTaxAssessmentAmount>
    <bd-i:TaxAssessmentProvisionalTaxIndication contextRef="Context_Duration">IBPV</bd-i:TaxAssessmentProvisionalTaxIndication>
  </bd-t:TaxAssessmentProvisionalIncomeTaxDataSpecification>
</xbrli:xbrl>
