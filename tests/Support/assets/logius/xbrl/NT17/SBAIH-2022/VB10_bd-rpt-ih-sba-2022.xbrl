<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt17/bd/20230215.a/bd-rpt-ih-sba-2022.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 03-11-2022 16:02:00 -->
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt17/bd/20221207/validation/bd-axes" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-domain-members" xmlns:bd-i="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-data" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt17/bd/20230215.a/dictionary/bd-data-ext1" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
    <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt17/bd/20230215.a/entrypoints/bd-rpt-ih-sba-2022.xsd"/>
    <xbrli:context id="c1d1">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:startDate>2022-01-01</xbrli:startDate>
            <xbrli:endDate>2022-12-31</xbrli:endDate>
        </xbrli:period>
    </xbrli:context>
    <xbrli:context id="c3d1dd">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:startDate>2022-01-01</xbrli:startDate>
            <xbrli:endDate>2022-12-31</xbrli:endDate>
        </xbrli:period>
        <xbrli:scenario>
            <xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
            <xbrldi:explicitMember dimension="bd-dim-dim:TaxpayerDimension">bd-dim-mem:Domestic</xbrldi:explicitMember>
        </xbrli:scenario>
    </xbrli:context>
    <xbrli:unit id="EUR">
        <xbrli:measure>iso4217:EUR</xbrli:measure>
    </xbrli:unit>
    <xbrli:unit id="PURE">
        <xbrli:measure>xbrli:pure</xbrli:measure>
    </xbrli:unit>
    <bd-i-ext1:ContributionBaseIncome contextRef="c1d1" decimals="INF" unitRef="EUR">0</bd-i-ext1:ContributionBaseIncome>
    <bd-i-ext1:EmployedPersonsTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1332</bd-i-ext1:EmployedPersonsTaxCreditCalculated>
    <bd-i-ext1:FiscalYear contextRef="c1d1">2022</bd-i-ext1:FiscalYear>
    <bd-i-ext1:GeneralTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1080</bd-i-ext1:GeneralTaxCreditCalculated>
    <bd-i-ext1:IncomeDependentCombinationTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1331</bd-i-ext1:IncomeDependentCombinationTaxCreditCalculated>
    <bd-i-ext1:IncomeTaxBracket1 contextRef="c1d1" decimals="INF" unitRef="EUR">623</bd-i-ext1:IncomeTaxBracket1>
    <bd-i-ext1:ObjectionDeadlineDate contextRef="c1d1">2022-08-31</bd-i-ext1:ObjectionDeadlineDate>
    <bd-i-ext1:PersonDependentRemainderDeductionSettledWithIncomeBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">37746</bd-i-ext1:PersonDependentRemainderDeductionSettledWithIncomeBox1>
    <bd-i-ext1:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">09.650</bd-i-ext1:SocialInsuranceRatePercentage>
    <bd-i-ext1:TaxAppropriation contextRef="c1d1">H</bd-i-ext1:TaxAppropriation>
    <bd-i-ext1:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">623</bd-i-ext1:TaxBox1>
    <bd-i-ext1:TaxBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">1335</bd-i-ext1:TaxBox3>
    <bd-i-ext1:TaxConsultantNameAddress1 contextRef="c1d1">J. de Vries</bd-i-ext1:TaxConsultantNameAddress1>
    <bd-i-ext1:TaxConsultantNameAddress2 contextRef="c1d1">Dennenlaan 3</bd-i-ext1:TaxConsultantNameAddress2>
    <bd-i-ext1:TaxConsultantNameAddress3 contextRef="c1d1">Wittem</bd-i-ext1:TaxConsultantNameAddress3>
    <bd-i-ext1:TaxConsultantNameAddress4 contextRef="c1d1">7924 BR</bd-i-ext1:TaxConsultantNameAddress4>
    <bd-i-ext1:TaxConsultantNameAddress5 contextRef="c1d1">038-4568855</bd-i-ext1:TaxConsultantNameAddress5>
    <bd-i-ext1:TaxableIncomeBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">4453</bd-i-ext1:TaxableIncomeBox3>
    <bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse contextRef="c1d1" decimals="INF" unitRef="EUR">7010</bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse>
    <bd-i-ext1:TaxationElsewhereBox1Deduction contextRef="c1d1" decimals="INF" unitRef="EUR">623</bd-i-ext1:TaxationElsewhereBox1Deduction>
    <bd-i-ext1:WithholdingTaxBox3AbroadPreviousYearsToBeSetoff contextRef="c1d1" decimals="INF" unitRef="EUR">3680</bd-i-ext1:WithholdingTaxBox3AbroadPreviousYearsToBeSetoff>
    <bd-i-ext1:WithholdingTaxBox3AbroadSettled contextRef="c1d1" decimals="INF" unitRef="EUR">1335</bd-i-ext1:WithholdingTaxBox3AbroadSettled>
    <bd-i:DateOfBirth contextRef="c1d1">1961-05-00</bd-i:DateOfBirth>
    <bd-i:NoticeOfAssessmentDate contextRef="c1d1">2022-07-20</bd-i:NoticeOfAssessmentDate>
    <bd-i:NoticeOfAssessmentNumber contextRef="c1d1">2621.52.848.H.26.01</bd-i:NoticeOfAssessmentNumber>
    <bd-i:OverallIncome contextRef="c1d1" decimals="INF" unitRef="EUR">11463</bd-i:OverallIncome>
    <bd-i:SavingsAndInvestmentsTaxBaseTaxpayerPartyShare contextRef="c3d1dd" decimals="INF" unitRef="EUR">125000</bd-i:SavingsAndInvestmentsTaxBaseTaxpayerPartyShare>
    <bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">0</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
    <bd-i:TaxReturnMessageType contextRef="c1d1">20</bd-i:TaxReturnMessageType>
    <bd-i:TaxpayerNameAddress1 contextRef="c1d1">O.P. Gennep</bd-i:TaxpayerNameAddress1>
    <bd-i:TaxpayerNameAddress2 contextRef="c1d1">Straatweg 5</bd-i:TaxpayerNameAddress2>
    <bd-i:TaxpayerNameAddress3 contextRef="c1d1">Nergenshuizen</bd-i:TaxpayerNameAddress3>
    <bd-i:TaxpayerNameAddress4 contextRef="c1d1">1234AB</bd-i:TaxpayerNameAddress4>
    <bd-i:TaxpayerNameAddress5 contextRef="c1d1">030-1254463</bd-i:TaxpayerNameAddress5>
</xbrli:xbrl>
