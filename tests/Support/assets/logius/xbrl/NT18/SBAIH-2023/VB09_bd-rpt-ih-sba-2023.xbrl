<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt18/bd/20240221.a/bd-rpt-ih-sba-2023.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 31-10-2023 16:01:00 -->
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-i="http://www.nltaxonomie.nl/nt18/bd/20231213/dictionary/bd-data" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt18/bd/20240221.a/dictionary/bd-data-ext1" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
    <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt18/bd/20240221.a/entrypoints/bd-rpt-ih-sba-2023.xsd"/>
    <xbrli:context id="c1d1">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:startDate>2023-01-01</xbrli:startDate>
            <xbrli:endDate>2023-12-31</xbrli:endDate>
        </xbrli:period>
    </xbrli:context>
    <xbrli:unit id="EUR">
        <xbrli:measure>iso4217:EUR</xbrli:measure>
    </xbrli:unit>
    <xbrli:unit id="PURE">
        <xbrli:measure>xbrli:pure</xbrli:measure>
    </xbrli:unit>
    <bd-i-ext1:ContributionBaseIncome contextRef="c1d1" decimals="INF" unitRef="EUR">4352</bd-i-ext1:ContributionBaseIncome>
    <bd-i-ext1:FiscalYear contextRef="c1d1">2023</bd-i-ext1:FiscalYear>
    <bd-i-ext1:GeneralTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1590</bd-i-ext1:GeneralTaxCreditCalculated>
    <bd-i-ext1:IncomeTaxBracket1 contextRef="c1d1" decimals="INF" unitRef="EUR">387</bd-i-ext1:IncomeTaxBracket1>
    <bd-i-ext1:ObjectionDeadlineDate contextRef="c1d1">2023-08-31</bd-i-ext1:ObjectionDeadlineDate>
    <bd-i-ext1:PersonDependentRemainderDeductionSettledWithIncomeBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">11542</bd-i-ext1:PersonDependentRemainderDeductionSettledWithIncomeBox1>
    <bd-i-ext1:SocialInsuranceContributionBalance contextRef="c1d1" decimals="INF" unitRef="EUR">1203</bd-i-ext1:SocialInsuranceContributionBalance>
    <bd-i-ext1:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">27.650</bd-i-ext1:SocialInsuranceRatePercentage>
    <bd-i-ext1:TaxAppropriation contextRef="c1d1">H</bd-i-ext1:TaxAppropriation>
    <bd-i-ext1:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">387</bd-i-ext1:TaxBox1>
    <bd-i-ext1:TaxBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">249</bd-i-ext1:TaxBox3>
    <bd-i-ext1:TaxConsultantNameAddress1 contextRef="c1d1">J. de Vries</bd-i-ext1:TaxConsultantNameAddress1>
    <bd-i-ext1:TaxConsultantNameAddress2 contextRef="c1d1">Dennenlaan 3</bd-i-ext1:TaxConsultantNameAddress2>
    <bd-i-ext1:TaxConsultantNameAddress3 contextRef="c1d1">Wittem</bd-i-ext1:TaxConsultantNameAddress3>
    <bd-i-ext1:TaxConsultantNameAddress4 contextRef="c1d1">7924BR</bd-i-ext1:TaxConsultantNameAddress4>
    <bd-i-ext1:TaxableIncomeBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">29050</bd-i-ext1:TaxableIncomeBox3>
    <bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse contextRef="c1d1" decimals="INF" unitRef="EUR">15894</bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse>
    <bd-i-ext1:WithholdingTaxBox3AbroadSettled contextRef="c1d1" decimals="INF" unitRef="EUR">249</bd-i-ext1:WithholdingTaxBox3AbroadSettled>
    <bd-i:DateOfBirth contextRef="c1d1">1985-09-00</bd-i:DateOfBirth>
    <bd-i:NoticeOfAssessmentDate contextRef="c1d1">2023-07-20</bd-i:NoticeOfAssessmentDate>
    <bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2023-08-25</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
    <bd-i:NoticeOfAssessmentNumber contextRef="c1d1">2621.71.387.H.22.01</bd-i:NoticeOfAssessmentNumber>
    <bd-i:OverallIncome contextRef="c1d1" decimals="INF" unitRef="EUR">5185</bd-i:OverallIncome>
    <bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">0</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
    <bd-i:TaxReturnMessageType contextRef="c1d1">20</bd-i:TaxReturnMessageType>
    <bd-i:TaxpayerNameAddress1 contextRef="c1d1">K.H. Spijker</bd-i:TaxpayerNameAddress1>
    <bd-i:TaxpayerNameAddress2 contextRef="c1d1">zandweg6</bd-i:TaxpayerNameAddress2>
    <bd-i:TaxpayerNameAddress3 contextRef="c1d1">nergenshuizen</bd-i:TaxpayerNameAddress3>
    <bd-i:TaxpayerNameAddress4 contextRef="c1d1">1234AB</bd-i:TaxpayerNameAddress4>
</xbrli:xbrl>
